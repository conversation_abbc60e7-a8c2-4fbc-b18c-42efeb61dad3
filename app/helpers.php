<?php

if (!function_exists('get_hero_style')) {
    /**
     * Get the hero background style
     *
     * @return string The CSS style for the hero background
     */
    function get_hero_style()
    {
        $styles = [];

        // Get gradient settings first
        $gradientEnabled = \App\Models\Setting::get('hero_gradient_enabled', 'false') === 'true';

        // Get hero image
        $heroImage = \App\Models\Setting::get('hero_image');

        if ($gradientEnabled) {
            $startColor = \App\Models\Setting::get('hero_gradient_start', '#0d6efd');
            $middleColor = \App\Models\Setting::get('hero_gradient_middle', '#6610f2');
            $endColor = \App\Models\Setting::get('hero_gradient_end', '#dc3545');
            $direction = \App\Models\Setting::get('hero_gradient_direction', 'to right');
            $opacity = \App\Models\Setting::get('hero_gradient_opacity', '80') / 100;

            // Convert hex to rgba - define function inside to avoid conflicts
            $hexToRgba = function($hex, $alpha) {
                $hex = ltrim($hex, '#');
                if (strlen($hex) === 3) {
                    $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
                }
                $r = hexdec(substr($hex, 0, 2));
                $g = hexdec(substr($hex, 2, 2));
                $b = hexdec(substr($hex, 4, 2));
                return "rgba($r, $g, $b, $alpha)";
            };

            $startRgba = $hexToRgba($startColor, $opacity);
            $middleRgba = $hexToRgba($middleColor, $opacity);
            $endRgba = $hexToRgba($endColor, $opacity);

            $gradient = "linear-gradient($direction, $startRgba, $middleRgba, $endRgba)";

            // If there's a background image, overlay the gradient
            if ($heroImage) {
                $imageUrl = get_image_url($heroImage, 'defaults/hero-bg.jpg');
                $styles[] = "background-image: $gradient, url('$imageUrl')";
                $styles[] = "background-size: cover";
                $styles[] = "background-position: center center";
            } else {
                $styles[] = "background-image: $gradient";
            }
        } else {
            // Only show background image if gradient is disabled
            if ($heroImage) {
                $imageUrl = get_image_url($heroImage, 'defaults/hero-bg.jpg');
                $styles[] = "background-image: url('$imageUrl')";
                $styles[] = "background-size: cover";
                $styles[] = "background-position: center center";
            } else {
                // Fallback background color if no gradient and no image
                $styles[] = "background-color: #007bff";
            }
        }

        return implode('; ', $styles);
    }
}

if (!function_exists('normalize_storage_path')) {
    /**
     * Normalize a storage path to ensure consistent format
     *
     * @param string|null $path The path to normalize
     * @param string $defaultDir The default directory if path doesn't specify one
     * @return array An array containing [storagePath, publicPath, urlPath]
     */
    function normalize_storage_path($path, $defaultDir = 'images')
    {
        if (empty($path)) {
            return [
                'storagePath' => null,
                'publicPath' => null,
                'urlPath' => null
            ];
        }

        // Remove any leading slashes and storage prefix
        $cleanPath = preg_replace('#^/?(storage/)?#', '', $path);

        // If the path doesn't include a directory, add the default directory
        if (!str_contains($cleanPath, '/')) {
            $cleanPath = $defaultDir . '/' . $cleanPath;
        }

        return [
            'storagePath' => 'app/public/' . $cleanPath,
            'publicPath' => 'storage/' . $cleanPath,
            'urlPath' => 'storage/' . $cleanPath
        ];
    }
}

if (!function_exists('ensure_file_in_public')) {
    /**
     * Ensure a file exists in the public storage directory
     * If it exists in app/public but not in public/storage, copy it
     *
     * @param string $path The path to check and fix
     * @return bool Whether the file exists or was successfully copied
     */
    function ensure_file_in_public($path)
    {
        $normalized = normalize_storage_path($path);

        if (empty($normalized['storagePath'])) {
            return false;
        }

        $publicFullPath = public_path($normalized['publicPath']);
        $storageFullPath = storage_path($normalized['storagePath']);

        // Check if paths are within allowed directories before using file_exists
        try {
            // If file already exists in public, we're good
            if (@file_exists($publicFullPath)) {
                return true;
            }

            // If file exists in storage but not in public, copy it
            if (@file_exists($storageFullPath)) {
                try {
                    $dir = dirname($publicFullPath);
                    if (!@file_exists($dir)) {
                        mkdir($dir, 0755, true);
                    }

                    return copy($storageFullPath, $publicFullPath);
                } catch (\Exception $e) {
                    \Log::error('Failed to copy file: ' . $e->getMessage());
                    return false;
                }
            }
        } catch (\ErrorException $e) {
            // Log the error but don't crash
            \Log::warning('open_basedir restriction: ' . $e->getMessage());
            return false;
        }

        return false;
    }
}

if (!function_exists('get_image_url')) {
    /**
     * Get the URL for an image from filename
     *
     * @param string|null $filename The image filename (without path)
     * @param string $default The default image path if $filename is empty or file doesn't exist
     * @return string The full URL to the image
     */
    function get_image_url($filename, $default = 'defaults/document-default.svg')
    {
        // Return default image if filename is empty
        if (empty($filename)) {
            return asset('images/' . $default);
        }

        // Check if this is a default image path
        if (str_starts_with($filename, 'defaults/')) {
            return asset('images/' . $filename);
        }

        // Check if the filename already starts with 'http' or '//' (external URL)
        if (str_starts_with($filename, 'http') || str_starts_with($filename, '//')) {
            return $filename;
        }

        // สร้าง path สำหรับรูปภาพ
        $imagePath = 'storage/images/' . $filename;
        $fullPath = public_path($imagePath);
        $storageFullPath = storage_path('app/public/images/' . $filename);

        try {
            // ตรวจสอบว่าไฟล์มีอยู่ใน public/storage/images หรือไม่
            if (@file_exists($fullPath)) {
                return asset($imagePath);
            }
            // ถ้าไม่พบใน public แต่พบใน storage ให้คัดลอกไฟล์
            else if (@file_exists($storageFullPath)) {
                // ตรวจสอบว่าโฟลเดอร์ปลายทางมีอยู่หรือไม่
                $targetDir = dirname($fullPath);
                if (!@file_exists($targetDir)) {
                    @mkdir($targetDir, 0755, true);
                }

                // คัดลอกไฟล์จาก storage ไปยัง public
                if (@copy($storageFullPath, $fullPath)) {
                    \Log::info("Copied image file from storage to public: {$storageFullPath} -> {$fullPath}");
                    return asset($imagePath);
                } else {
                    \Log::warning("Failed to copy image file: {$storageFullPath} -> {$fullPath}");
                }

                // ถึงแม้จะคัดลอกไม่สำเร็จ แต่ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
                return asset($imagePath);
            } else {
                // File doesn't exist, log this for debugging
                \Log::info("Image file not found: {$fullPath}, using default image");
                // ใช้รูปภาพ default
                return asset('images/' . $default);
            }
        } catch (\ErrorException $e) {
            \Log::warning('Error checking if image file exists: ' . $e->getMessage());
            // ในกรณีที่เกิดข้อผิดพลาด ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
            return asset($imagePath);
        }
    }
}

if (!function_exists('get_file_url')) {
    /**
     * Get the URL for a file from filename
     *
     * @param string|null $filename The filename (without path)
     * @return string|null The full URL to the file or null if filename is empty
     */
    function get_file_url($filename)
    {
        if (empty($filename)) {
            return null;
        }

        // Check if the filename already starts with 'http' or '//' (external URL)
        if (str_starts_with($filename, 'http') || str_starts_with($filename, '//')) {
            return $filename;
        }

        // กำหนดโฟลเดอร์ตามประเภทไฟล์
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        $folder = 'files'; // default

        if (in_array($extension, ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'])) {
            $folder = 'audio';
        } elseif (in_array($extension, ['mp4', 'webm', 'avi', 'mov', 'mkv', 'flv'])) {
            $folder = 'video';
        } elseif (in_array($extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
            $folder = 'documents';
        }

        // สร้าง path สำหรับไฟล์
        $filePath = 'storage/' . $folder . '/' . $filename;
        $fullPath = public_path($filePath);
        $storageFullPath = storage_path('app/public/' . $folder . '/' . $filename);

        try {
            // ตรวจสอบว่าไฟล์มีอยู่ใน public/storage/{folder} หรือไม่
            if (@file_exists($fullPath)) {
                return asset($filePath);
            }
            // ถ้าไม่พบใน public แต่พบใน storage ให้คัดลอกไฟล์
            else if (@file_exists($storageFullPath)) {
                // ตรวจสอบว่าโฟลเดอร์ปลายทางมีอยู่หรือไม่
                $targetDir = dirname($fullPath);
                if (!@file_exists($targetDir)) {
                    @mkdir($targetDir, 0755, true);
                }

                // คัดลอกไฟล์จาก storage ไปยัง public
                if (@copy($storageFullPath, $fullPath)) {
                    \Log::info("Copied file from storage to public: {$storageFullPath} -> {$fullPath}");
                    return asset($filePath);
                } else {
                    \Log::warning("Failed to copy file: {$storageFullPath} -> {$fullPath}");
                }

                // ถึงแม้จะคัดลอกไม่สำเร็จ แต่ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
                return asset($filePath);
            } else {
                // File doesn't exist, but return URL anyway for display
                \Log::info("File not found: {$fullPath}, returning URL anyway");
                return asset($filePath);
            }
        } catch (\ErrorException $e) {
            \Log::warning('Error checking if file exists: ' . $e->getMessage());
            // ในกรณีที่เกิดข้อผิดพลาด ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
            return asset($filePath);
        }
    }
}

if (!function_exists('get_file_storage_path')) {
    /**
     * สร้าง path สำหรับไฟล์ตามประเภท
     *
     * @param string $filename ชื่อไฟล์
     * @param string $type ประเภทไฟล์ (image, audio, video, document, file)
     * @return string
     */
    function get_file_storage_path($filename, $type = null)
    {
        if (empty($filename)) {
            return '';
        }

        // ถ้าไม่ระบุประเภท ให้ตรวจสอบจากนามสกุลไฟล์
        if (!$type) {
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])) {
                $type = 'image';
            } elseif (in_array($extension, ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'])) {
                $type = 'audio';
            } elseif (in_array($extension, ['mp4', 'webm', 'avi', 'mov', 'mkv', 'flv'])) {
                $type = 'video';
            } elseif (in_array($extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
                $type = 'document';
            } else {
                $type = 'file';
            }
        }

        // กำหนดโฟลเดอร์ตามประเภท
        switch ($type) {
            case 'image':
                return 'storage/images/' . $filename;
            case 'audio':
                return 'storage/audio/' . $filename;
            case 'video':
                return 'storage/video/' . $filename;
            case 'document':
                return 'storage/documents/' . $filename;
            case 'file':
            default:
                return 'storage/files/' . $filename;
        }
    }
}

if (!function_exists('get_base_path')) {
    /**
     * Get the base path for subfolder installations
     *
     * @return string
     */
    function get_base_path()
    {
        // Get the script name from the server variables
        $scriptName = request()->server('SCRIPT_NAME', '');
        $basePath = '';

        // If the script name contains a path, extract it
        if (!empty($scriptName)) {
            $scriptPath = dirname($scriptName);
            if ($scriptPath !== '/' && $scriptPath !== '\\') {
                $basePath = trim($scriptPath, '/');
            }
        }

        return $basePath;
    }
}
