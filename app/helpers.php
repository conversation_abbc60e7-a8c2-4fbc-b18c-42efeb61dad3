<?php

if (!function_exists('get_hero_style')) {
    /**
     * Get the hero background style
     *
     * @return string The CSS style for the hero background
     */
    function get_hero_style()
    {
        $heroImage = \App\Models\Setting::get('hero_image');
        return $heroImage
            ? "background-image: url('" . get_image_url($heroImage, 'defaults/hero-bg.jpg') . "'); background-size: cover; background-position: center center;"
            : '';
    }
}

if (!function_exists('normalize_storage_path')) {
    /**
     * Normalize a storage path to ensure consistent format
     *
     * @param string|null $path The path to normalize
     * @param string $defaultDir The default directory if path doesn't specify one
     * @return array An array containing [storagePath, publicPath, urlPath]
     */
    function normalize_storage_path($path, $defaultDir = 'images')
    {
        if (empty($path)) {
            return [
                'storagePath' => null,
                'publicPath' => null,
                'urlPath' => null
            ];
        }

        // Remove any leading slashes and storage prefix
        $cleanPath = preg_replace('#^/?(storage/)?#', '', $path);

        // If the path doesn't include a directory, add the default directory
        if (!str_contains($cleanPath, '/')) {
            $cleanPath = $defaultDir . '/' . $cleanPath;
        }

        return [
            'storagePath' => 'app/public/' . $cleanPath,
            'publicPath' => 'storage/' . $cleanPath,
            'urlPath' => 'storage/' . $cleanPath
        ];
    }
}

if (!function_exists('ensure_file_in_public')) {
    /**
     * Ensure a file exists in the public storage directory
     * If it exists in app/public but not in public/storage, copy it
     *
     * @param string $path The path to check and fix
     * @return bool Whether the file exists or was successfully copied
     */
    function ensure_file_in_public($path)
    {
        $normalized = normalize_storage_path($path);

        if (empty($normalized['storagePath'])) {
            return false;
        }

        $publicFullPath = public_path($normalized['publicPath']);
        $storageFullPath = storage_path($normalized['storagePath']);

        // Check if paths are within allowed directories before using file_exists
        try {
            // If file already exists in public, we're good
            if (@file_exists($publicFullPath)) {
                return true;
            }

            // If file exists in storage but not in public, copy it
            if (@file_exists($storageFullPath)) {
                try {
                    $dir = dirname($publicFullPath);
                    if (!@file_exists($dir)) {
                        mkdir($dir, 0755, true);
                    }

                    return copy($storageFullPath, $publicFullPath);
                } catch (\Exception $e) {
                    \Log::error('Failed to copy file: ' . $e->getMessage());
                    return false;
                }
            }
        } catch (\ErrorException $e) {
            // Log the error but don't crash
            \Log::warning('open_basedir restriction: ' . $e->getMessage());
            return false;
        }

        return false;
    }
}

if (!function_exists('get_image_url')) {
    /**
     * Get the URL for an image
     *
     * @param string|null $path The image path
     * @param string $default The default image path if $path is empty or file doesn't exist
     * @return string The full URL to the image
     */
    function get_image_url($path, $default = 'defaults/document-default.svg')
    {
        // Return default image if path is empty
        if (empty($path)) {
            return asset('images/' . $default);
        }

        // Check if the path already starts with 'http' or '//'
        if (str_starts_with($path, 'http') || str_starts_with($path, '//')) {
            // For external URLs, we can't check if the file exists
            return $path;
        }

        // Check if this is a default image path
        if (str_starts_with($path, 'defaults/')) {
            return asset('images/' . $path);
        }

        // Normalize the path
        $normalized = normalize_storage_path($path, 'images');

        // Full path to check if file exists
        $fullPath = null;
        $assetUrl = null;
        $storageFullPath = null;

        // ตรวจสอบว่าเป็นไฟล์โหลดผ่าน storage หรือไม่
        if (str_starts_with($path, 'storage/')) {
            $fullPath = public_path($path);
            $assetUrl = asset($path);
            $storageFullPath = storage_path('app/public/' . str_replace('storage/', '', $path));
        }
        // ตรวจสอบว่าเป็นไฟล์อยู่ใน public/storage หรือไม่
        else if (str_starts_with($path, 'public/storage/')) {
            $fullPath = public_path(str_replace('public/', '', $path));
            $assetUrl = asset(str_replace('public/', '', $path));
            $storageFullPath = storage_path('app/public/' . str_replace(['public/', 'storage/'], '', $path));
        }
        // สร้าง URL โดยตรง ไฟล์ใน storage
        else if (str_starts_with($normalized['urlPath'], 'storage/')) {
            $fullPath = public_path($normalized['urlPath']);
            $assetUrl = asset($normalized['urlPath']);
            $storageFullPath = storage_path('app/public/' . str_replace('storage/', '', $normalized['urlPath']));
        }
        // ถ้าเป็นไฟล์ใน public directory
        else {
            try {
                $fullPath = public_path($path);
                if (@file_exists($fullPath)) {
                    return asset($path);
                }
            } catch (\ErrorException $e) {
                \Log::warning('open_basedir restriction in get_image_url: ' . $e->getMessage());
            }

            // ใช้ symlink URL แทนการตรวจสอบไฟล์
            if (!empty($normalized['urlPath'])) {
                $fullPath = public_path($normalized['urlPath']);
                $assetUrl = asset($normalized['urlPath']);
                $storageFullPath = storage_path($normalized['storagePath']);
            }
        }

        // Check if the file exists in public directory
        if ($fullPath && $assetUrl) {
            try {
                if (@file_exists($fullPath)) {
                    return $assetUrl;
                }
                // ถ้าไม่พบในโฟลเดอร์ public แต่พบในโฟลเดอร์ storage ให้คัดลอกไฟล์
                else if ($storageFullPath && @file_exists($storageFullPath)) {
                    // ตรวจสอบว่าโฟลเดอร์ปลายทางมีอยู่หรือไม่
                    $targetDir = dirname($fullPath);
                    if (!@file_exists($targetDir)) {
                        @mkdir($targetDir, 0755, true);
                    }

                    // คัดลอกไฟล์จาก storage ไปยัง public
                    if (@copy($storageFullPath, $fullPath)) {
                        \Log::info("Copied image file from storage to public: {$storageFullPath} -> {$fullPath}");
                        return $assetUrl;
                    } else {
                        \Log::warning("Failed to copy image file: {$storageFullPath} -> {$fullPath}");
                    }

                    // ถึงแม้จะคัดลอกไม่สำเร็จ แต่ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
                    // เพื่อให้แสดงรูปภาพได้ในครั้งถัดไปเมื่อมีการ refresh
                    return $assetUrl;
                } else {
                    // File doesn't exist, log this for debugging
                    \Log::info("Image file not found: {$fullPath}, using missing image indicator");
                    // ถึงแม้จะไม่พบไฟล์ แต่ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
                    // เพื่อให้แสดงรูปภาพได้ในครั้งถัดไปเมื่อมีการ refresh
                    return $assetUrl;
                }
            } catch (\ErrorException $e) {
                \Log::warning('Error checking if file exists in get_image_url: ' . $e->getMessage());
                // ในกรณีที่เกิดข้อผิดพลาด ให้ใช้ URL ของไฟล์ที่ควรจะมีอยู่
                return $assetUrl;
            }
        }

        // Always use default images from public/images/defaults if we can't find the file
        return asset('images/' . $default);
    }
}

if (!function_exists('get_file_url')) {
    /**
     * Get the URL for a file
     *
     * @param string|null $path The file path
     * @return string|null The full URL to the file or null if path is empty
     */
    function get_file_url($path)
    {
        if (empty($path)) {
            return null;
        }

        // Check if the path already starts with 'http' or '//'
        if (str_starts_with($path, 'http') || str_starts_with($path, '//')) {
            return $path;
        }

        // Normalize the path
        $normalized = normalize_storage_path($path, 'files');

        // Try to ensure the file exists in public storage
        ensure_file_in_public($path);

        // Return the URL using asset() to handle subfolder installations
        return asset($normalized['urlPath']);
    }
}

if (!function_exists('get_base_path')) {
    /**
     * Get the base path for subfolder installations
     *
     * @return string
     */
    function get_base_path()
    {
        // Get the script name from the server variables
        $scriptName = request()->server('SCRIPT_NAME', '');
        $basePath = '';

        // If the script name contains a path, extract it
        if (!empty($scriptName)) {
            $scriptPath = dirname($scriptName);
            if ($scriptPath !== '/' && $scriptPath !== '\\') {
                $basePath = trim($scriptPath, '/');
            }
        }

        return $basePath;
    }
}
