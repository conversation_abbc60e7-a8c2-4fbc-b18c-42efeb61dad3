<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\UpdateManuscriptProvinceSeeder;

class UpdateManuscriptProvince extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-manuscript-province';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update document provinces from manuscript.sql file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update document provinces from manuscript.sql...');
        
        $seeder = new UpdateManuscriptProvinceSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Province update completed!');
        
        return 0;
    }
}
