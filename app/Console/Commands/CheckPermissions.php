<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CheckPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and fix missing permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking permissions...');

        // Define all expected permissions
        $expectedPermissions = [
            // Items
            'view items',
            'create items',
            'edit items',
            'delete items',

            // Categories
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',

            // Types
            'view types',
            'create types',
            'edit types',
            'delete types',

            // Materials
            'view materials',
            'create materials',
            'edit materials',
            'delete materials',

            // Languages
            'view languages',
            'create languages',
            'edit languages',
            'delete languages',

            // Scripts
            'view scripts',
            'create scripts',
            'edit scripts',
            'delete scripts',

            // Users
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Settings
            'view settings',
            'edit settings',

            // Roles
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',

            // Permissions
            'view permissions',
            'create permissions',
            'edit permissions',
            'delete permissions',
        ];

        // Check for missing permissions
        $missingPermissions = [];
        foreach ($expectedPermissions as $permission) {
            if (!Permission::where('name', $permission)->exists()) {
                $missingPermissions[] = $permission;
            }
        }

        // Create missing permissions
        if (count($missingPermissions) > 0) {
            $this->info('Creating missing permissions:');
            foreach ($missingPermissions as $permission) {
                Permission::create(['name' => $permission]);
                $this->line("- {$permission}");
            }
            $this->info('All missing permissions have been created.');
        } else {
            $this->info('All expected permissions exist.');
        }

        // Check roles
        $expectedRoles = ['admin', 'editor', 'user'];
        $missingRoles = [];
        foreach ($expectedRoles as $role) {
            if (!Role::where('name', $role)->exists()) {
                $missingRoles[] = $role;
            }
        }

        // Create missing roles
        if (count($missingRoles) > 0) {
            $this->info('Creating missing roles:');
            foreach ($missingRoles as $role) {
                Role::create(['name' => $role]);
                $this->line("- {$role}");
            }
            $this->info('All missing roles have been created.');
        } else {
            $this->info('All expected roles exist.');
        }

        // Ensure admin role has all permissions
        $adminRole = Role::findByName('admin');
        $adminRole->syncPermissions(Permission::all());
        $this->info('Admin role has been updated with all permissions.');

        // Update editor role permissions
        $editorRole = Role::findByName('editor');
        $editorRole->syncPermissions([
            'view items', 'create items', 'edit items', 'delete items',
            'view categories', 'view types', 'view materials', 'view languages', 'view scripts',
            'view roles', 'view permissions',
        ]);
        $this->info('Editor role permissions have been updated.');

        // Update user role permissions
        $userRole = Role::findByName('user');
        $userRole->syncPermissions([
            'view items',
        ]);
        $this->info('User role permissions have been updated.');

        $this->info('Permission check completed successfully.');
    }
}
