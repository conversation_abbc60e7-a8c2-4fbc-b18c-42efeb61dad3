<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ImportItemViews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-item-views';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import views counts from documents in test database to items table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to import views counts from test database...');

        // Check if test database connection is configured
        try {
            DB::connection('test')->getPdo();
        } catch (\Exception $e) {
            $this->error('Test database connection failed: ' . $e->getMessage());
            return 1;
        }

        // Check if documents table exists in test database
        try {
            $testTableExists = DB::connection('test')->select("SHOW TABLES LIKE 'documents'");
            if (empty($testTableExists)) {
                $this->error("Table documents does not exist in test database.");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("Error checking table documents in test database: " . $e->getMessage());
            return 1;
        }

        // Check if items table exists in main database
        if (!Schema::hasTable('items')) {
            $this->error("Table items does not exist in main database.");
            return 1;
        }

        // Check if views column exists in documents table
        try {
            $columnsInfo = DB::connection('test')->select("SHOW COLUMNS FROM documents LIKE 'views'");
            if (empty($columnsInfo)) {
                $this->error("Column 'views' does not exist in documents table.");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("Error checking column 'views' in documents table: " . $e->getMessage());
            return 1;
        }

        // Check if views column exists in items table
        if (!Schema::hasColumn('items', 'views')) {
            $this->info("Column 'views' does not exist in items table. Adding it now...");
            Schema::table('items', function ($table) {
                $table->integer('views')->default(0);
            });
            $this->info("Added 'views' column to items table.");
        }

        try {
            // Get all documents with views counts from test database
            $documents = DB::connection('test')->table('documents')
                ->select('id', 'views')
                ->get();

            if ($documents->isEmpty()) {
                $this->warn("No documents found in test database.");
                return 0;
            }

            $this->info("Found " . count($documents) . " documents with views counts in test database.");

            // Update views counts in items table
            $updateCount = 0;
            $errorCount = 0;

            foreach ($documents as $document) {
                try {
                    // Update views count in items table
                    $affected = DB::table('items')
                        ->where('id', $document->id)
                        ->update(['views' => $document->views]);

                    if ($affected > 0) {
                        $updateCount++;
                    }
                } catch (\Exception $e) {
                    $this->error("Error updating views count for item ID {$document->id}: " . $e->getMessage());
                    $errorCount++;
                }
            }

            $this->info("Successfully updated views counts for {$updateCount} items.");
            if ($errorCount > 0) {
                $this->warn("{$errorCount} items failed to update.");
            }

        } catch (\Exception $e) {
            $this->error("Error importing views counts: " . $e->getMessage());
            return 1;
        }

        $this->info('Views counts imported successfully!');
        return 0;
    }
}
