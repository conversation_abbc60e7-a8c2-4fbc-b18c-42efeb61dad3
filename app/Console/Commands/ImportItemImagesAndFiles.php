<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class ImportItemImagesAndFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-item-images-and-files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import item images and files from test database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to import item images and files from test database...');

        // Check if test database connection is configured
        try {
            DB::connection('test')->getPdo();
        } catch (\Exception $e) {
            $this->error('Test database connection failed: ' . $e->getMessage());
            return 1;
        }

        // Import document_images to item_images
        $this->importImages();

        // Import document_files to item_files
        $this->importFiles();

        $this->info('All images and files imported successfully!');
        return 0;
    }

    /**
     * Import document_images to item_images
     */
    private function importImages()
    {
        $this->info("Importing document_images to item_images table");

        // Check if document_images table exists in test database
        try {
            $testTableExists = DB::connection('test')->select("SHOW TABLES LIKE 'document_images'");
            if (empty($testTableExists)) {
                $this->warn("Table document_images does not exist in test database. Skipping.");
                return;
            }
        } catch (\Exception $e) {
            $this->error("Error checking table document_images in test database: " . $e->getMessage());
            return;
        }

        // Check if item_images table exists in main database
        if (!Schema::hasTable('item_images')) {
            $this->warn("Table item_images does not exist in main database. Skipping.");
            return;
        }

        try {
            // Get data from test database
            $images = DB::connection('test')->table('document_images')->get();
            
            if ($images->isEmpty()) {
                $this->warn("No images found in document_images table. Skipping.");
                return;
            }
            
            $this->info("Found " . count($images) . " images in document_images table.");
            
            // Get existing IDs in main database
            $existingIds = DB::table('item_images')->pluck('id')->toArray();
            
            // Get columns from main database table
            $columns = Schema::getColumnListing('item_images');
            
            // Import each image
            $importCount = 0;
            $errorCount = 0;
            
            foreach ($images as $image) {
                // Convert image to array
                $imageArray = (array) $image;
                
                // Skip if image already exists
                if (in_array($imageArray['id'], $existingIds)) {
                    continue;
                }
                
                // Rename document_id to item_id if it exists
                if (isset($imageArray['document_id'])) {
                    $imageArray['item_id'] = $imageArray['document_id'];
                    unset($imageArray['document_id']);
                }
                
                // Filter out columns that don't exist in the main table
                $filteredData = array_intersect_key($imageArray, array_flip($columns));
                
                // Insert into main database
                try {
                    DB::table('item_images')->insert($filteredData);
                    $importCount++;
                } catch (\Exception $e) {
                    $this->error("Error importing image ID {$imageArray['id']} to item_images table: " . $e->getMessage());
                    $errorCount++;
                }
            }
            
            $this->info("Successfully imported {$importCount} images to item_images table.");
            if ($errorCount > 0) {
                $this->warn("{$errorCount} images failed to import.");
            }
            
        } catch (\Exception $e) {
            $this->error("Error importing images: " . $e->getMessage());
        }
    }

    /**
     * Import document_files to item_files
     */
    private function importFiles()
    {
        $this->info("Importing document_files to item_files table");

        // Check if document_files table exists in test database
        try {
            $testTableExists = DB::connection('test')->select("SHOW TABLES LIKE 'document_files'");
            if (empty($testTableExists)) {
                $this->warn("Table document_files does not exist in test database. Skipping.");
                return;
            }
        } catch (\Exception $e) {
            $this->error("Error checking table document_files in test database: " . $e->getMessage());
            return;
        }

        // Check if item_files table exists in main database
        if (!Schema::hasTable('item_files')) {
            $this->warn("Table item_files does not exist in main database. Skipping.");
            return;
        }

        try {
            // Get data from test database
            $files = DB::connection('test')->table('document_files')->get();
            
            if ($files->isEmpty()) {
                $this->warn("No files found in document_files table. Skipping.");
                return;
            }
            
            $this->info("Found " . count($files) . " files in document_files table.");
            
            // Get existing IDs in main database
            $existingIds = DB::table('item_files')->pluck('id')->toArray();
            
            // Get columns from main database table
            $columns = Schema::getColumnListing('item_files');
            
            // Import each file
            $importCount = 0;
            $errorCount = 0;
            
            foreach ($files as $file) {
                // Convert file to array
                $fileArray = (array) $file;
                
                // Skip if file already exists
                if (in_array($fileArray['id'], $existingIds)) {
                    continue;
                }
                
                // Rename document_id to item_id if it exists
                if (isset($fileArray['document_id'])) {
                    $fileArray['item_id'] = $fileArray['document_id'];
                    unset($fileArray['document_id']);
                }
                
                // Filter out columns that don't exist in the main table
                $filteredData = array_intersect_key($fileArray, array_flip($columns));
                
                // Insert into main database
                try {
                    DB::table('item_files')->insert($filteredData);
                    $importCount++;
                } catch (\Exception $e) {
                    $this->error("Error importing file ID {$fileArray['id']} to item_files table: " . $e->getMessage());
                    $errorCount++;
                }
            }
            
            $this->info("Successfully imported {$importCount} files to item_files table.");
            if ($errorCount > 0) {
                $this->warn("{$errorCount} files failed to import.");
            }
            
        } catch (\Exception $e) {
            $this->error("Error importing files: " . $e->getMessage());
        }
    }
}
