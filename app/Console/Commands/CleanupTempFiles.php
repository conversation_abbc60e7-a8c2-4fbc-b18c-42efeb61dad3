<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CleanupTempFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:temp-files {--hours=24 : Hours to keep files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up temporary files that were uploaded but not associated with any document';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = $this->option('hours');
        $this->info("Cleaning up temporary files older than {$hours} hours...");

        // Get the cutoff time
        $cutoffTime = Carbon::now()->subHours($hours);

        // Directories to check
        $directories = [
            'documents-all',
            'images'
        ];

        $totalRemoved = 0;

        foreach ($directories as $directory) {
            $path = public_path($directory);

            if (!File::exists($path)) {
                $this->warn("Directory {$directory} does not exist. Skipping.");
                continue;
            }

            $files = File::files($path);
            $this->info("Found " . count($files) . " files in {$directory}");

            foreach ($files as $file) {
                $filename = $file->getFilename();
                $filePath = $directory . '/' . $filename;

                // Special handling for documents-all directory
                if ($directory === 'documents-all') {
                    // Check if this file is referenced in the database
                    $isReferenced = $this->isFileReferencedInDatabase($filePath);

                    if (!$isReferenced) {
                        // For documents-all, if it has a timestamp prefix, we can delete it if it's older than cutoff
                        if (preg_match('/^(\d+)_/', $filename, $matches)) {
                            $timestamp = (int)$matches[1];
                            $fileTime = Carbon::createFromTimestamp($timestamp);

                            if ($fileTime->lt($cutoffTime)) {
                                // Delete the file
                                File::delete($file->getPathname());
                                $this->info("Deleted orphaned file: {$filename}");
                                $totalRemoved++;

                                // Log the deletion
                                Log::info("Deleted orphaned file: {$filePath}");
                            }
                        } else {
                            // For files without timestamp prefix in documents-all, check file modification time
                            $fileModTime = Carbon::createFromTimestamp($file->getMTime());

                            if ($fileModTime->lt($cutoffTime)) {
                                // Delete the file
                                File::delete($file->getPathname());
                                $this->info("Deleted old file without timestamp: {$filename}");
                                $totalRemoved++;

                                // Log the deletion
                                Log::info("Deleted old file without timestamp: {$filePath}");
                            }
                        }
                    }
                } else {
                    // For images directory, only delete files with timestamp prefix
                    if (preg_match('/^(\d+)_/', $filename, $matches)) {
                        $timestamp = (int)$matches[1];
                        $fileTime = Carbon::createFromTimestamp($timestamp);

                        // If the file is older than the cutoff time
                        if ($fileTime->lt($cutoffTime)) {
                            // Check if this file is referenced in the database
                            $isReferenced = $this->isFileReferencedInDatabase($filePath);

                            if (!$isReferenced) {
                                // Delete the file
                                File::delete($file->getPathname());
                                $this->info("Deleted: {$filename}");
                                $totalRemoved++;

                                // Log the deletion
                                Log::info("Deleted orphaned temporary file: {$filePath}");
                            }
                        }
                    }
                }
            }
        }

        $this->info("Cleanup complete. Removed {$totalRemoved} orphaned temporary files.");
        return 0;
    }

    /**
     * Check if a file is referenced in the database
     * Checks file_path and image_path columns in documents table, document_images table,
     * settings table, and users table
     */
    private function isFileReferencedInDatabase($filePath)
    {
        // Normalize the file path for comparison
        $normalizedPath = $filePath;
        $basename = basename($filePath);

        // Check in documents table file_path column with exact path
        $documentFilePathCount = \DB::table('documents')
            ->where('file_path', $normalizedPath)
            ->count();

        if ($documentFilePathCount > 0) {
            $this->info("File is referenced in documents.file_path: {$normalizedPath}");
            return true;
        }

        // Check in documents table image_path column with basename
        $documentImagePathCount = \DB::table('documents')
            ->where('image_path', $basename)
            ->count();

        if ($documentImagePathCount > 0) {
            $this->info("File is referenced in documents.image_path: {$basename}");
            return true;
        }

        // Check in document_images table
        $imageCount = \DB::table('document_images')
            ->where('image_path', $basename)
            ->count();

        if ($imageCount > 0) {
            $this->info("File is referenced in document_images table: {$basename}");
            return true;
        }

        // Check in settings table (for site_logo, hero_image, favicon)
        $settingsCount = \DB::table('settings')
            ->whereIn('key', ['site_logo', 'hero_image', 'favicon'])
            ->where('value', $basename)
            ->count();

        if ($settingsCount > 0) {
            $this->info("File is referenced in settings table: {$basename}");
            return true;
        }

        // Check in users table profile_image column
        $usersCount = \DB::table('users')
            ->where('profile_image', $basename)
            ->count();

        if ($usersCount > 0) {
            $this->info("File is referenced in users.profile_image: {$basename}");
            return true;
        }

        return false;
    }
}
