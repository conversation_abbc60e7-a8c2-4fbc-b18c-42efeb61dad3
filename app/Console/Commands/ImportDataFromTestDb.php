<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ImportDataFromTestDb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-data-from-test-db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import data from test database to current database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import from test database...');

        // Check if test database has documents table
        try {
            $hasDocumentsTable = DB::connection('test')->select("SHOW TABLES LIKE 'documents'");
            
            if (empty($hasDocumentsTable)) {
                $this->error('The test database does not have a documents table.');
                return 1;
            }
            
            // Get count of documents in test database
            $documentsCount = DB::connection('test')->select("SELECT COUNT(*) as count FROM documents");
            $count = $documentsCount[0]->count ?? 0;
            
            $this->info("Found {$count} documents in test database.");
            
            if ($count == 0) {
                $this->warn('No documents to import.');
                return 0;
            }
            
            // Import documents to items
            $this->importDocuments();
            
            // Import document_images to item_images
            $this->importDocumentImages();
            
            // Import document_files to item_files
            $this->importDocumentFiles();
            
            $this->info('Import completed successfully!');
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Error connecting to test database: ' . $e->getMessage());
            return 1;
        }
    }
    
    /**
     * Import documents to items
     */
    private function importDocuments()
    {
        $this->info('Importing documents to items...');
        
        // Get all documents from test database
        $documents = DB::connection('test')->select("SELECT * FROM documents");
        
        // Check if we have any documents
        if (empty($documents)) {
            $this->warn('No documents found in test database.');
            return;
        }
        
        $this->info('Found ' . count($documents) . ' documents to import.');
        
        // Import each document
        $importCount = 0;
        foreach ($documents as $document) {
            // Convert document to array
            $documentArray = (array) $document;
            
            // Check if item already exists
            $existingItem = DB::table('items')->where('id', $documentArray['id'])->first();
            
            if ($existingItem) {
                $this->warn("Item with ID {$documentArray['id']} already exists. Skipping.");
                continue;
            }
            
            // Rename document_type_id to item_type_id if it exists
            if (isset($documentArray['document_type_id'])) {
                $documentArray['item_type_id'] = $documentArray['document_type_id'];
                unset($documentArray['document_type_id']);
            }
            
            // Insert into items table
            try {
                DB::table('items')->insert($documentArray);
                $importCount++;
                $this->line("Imported document ID {$documentArray['id']} to items table.");
            } catch (\Exception $e) {
                $this->error("Error importing document ID {$documentArray['id']}: " . $e->getMessage());
            }
        }
        
        $this->info("Successfully imported {$importCount} documents to items table.");
    }
    
    /**
     * Import document_images to item_images
     */
    private function importDocumentImages()
    {
        $this->info('Importing document_images to item_images...');
        
        // Check if document_images table exists in test database
        try {
            $hasDocumentImagesTable = DB::connection('test')->select("SHOW TABLES LIKE 'document_images'");
            
            if (empty($hasDocumentImagesTable)) {
                $this->warn('The test database does not have a document_images table.');
                return;
            }
            
            // Get all document_images from test database
            $documentImages = DB::connection('test')->select("SELECT * FROM document_images");
            
            // Check if we have any document_images
            if (empty($documentImages)) {
                $this->warn('No document_images found in test database.');
                return;
            }
            
            $this->info('Found ' . count($documentImages) . ' document_images to import.');
            
            // Import each document_image
            $importCount = 0;
            foreach ($documentImages as $documentImage) {
                // Convert document_image to array
                $documentImageArray = (array) $documentImage;
                
                // Check if item_image already exists
                $existingItemImage = DB::table('item_images')->where('id', $documentImageArray['id'])->first();
                
                if ($existingItemImage) {
                    $this->warn("Item image with ID {$documentImageArray['id']} already exists. Skipping.");
                    continue;
                }
                
                // Rename document_id to item_id if it exists
                if (isset($documentImageArray['document_id'])) {
                    $documentImageArray['item_id'] = $documentImageArray['document_id'];
                    unset($documentImageArray['document_id']);
                }
                
                // Insert into item_images table
                try {
                    DB::table('item_images')->insert($documentImageArray);
                    $importCount++;
                    $this->line("Imported document_image ID {$documentImageArray['id']} to item_images table.");
                } catch (\Exception $e) {
                    $this->error("Error importing document_image ID {$documentImageArray['id']}: " . $e->getMessage());
                }
            }
            
            $this->info("Successfully imported {$importCount} document_images to item_images table.");
            
        } catch (\Exception $e) {
            $this->error('Error importing document_images: ' . $e->getMessage());
        }
    }
    
    /**
     * Import document_files to item_files
     */
    private function importDocumentFiles()
    {
        $this->info('Importing document_files to item_files...');
        
        // Check if document_files table exists in test database
        try {
            $hasDocumentFilesTable = DB::connection('test')->select("SHOW TABLES LIKE 'document_files'");
            
            if (empty($hasDocumentFilesTable)) {
                $this->warn('The test database does not have a document_files table.');
                return;
            }
            
            // Get all document_files from test database
            $documentFiles = DB::connection('test')->select("SELECT * FROM document_files");
            
            // Check if we have any document_files
            if (empty($documentFiles)) {
                $this->warn('No document_files found in test database.');
                return;
            }
            
            $this->info('Found ' . count($documentFiles) . ' document_files to import.');
            
            // Import each document_file
            $importCount = 0;
            foreach ($documentFiles as $documentFile) {
                // Convert document_file to array
                $documentFileArray = (array) $documentFile;
                
                // Check if item_file already exists
                $existingItemFile = DB::table('item_files')->where('id', $documentFileArray['id'])->first();
                
                if ($existingItemFile) {
                    $this->warn("Item file with ID {$documentFileArray['id']} already exists. Skipping.");
                    continue;
                }
                
                // Rename document_id to item_id if it exists
                if (isset($documentFileArray['document_id'])) {
                    $documentFileArray['item_id'] = $documentFileArray['document_id'];
                    unset($documentFileArray['document_id']);
                }
                
                // Insert into item_files table
                try {
                    DB::table('item_files')->insert($documentFileArray);
                    $importCount++;
                    $this->line("Imported document_file ID {$documentFileArray['id']} to item_files table.");
                } catch (\Exception $e) {
                    $this->error("Error importing document_file ID {$documentFileArray['id']}: " . $e->getMessage());
                }
            }
            
            $this->info("Successfully imported {$importCount} document_files to item_files table.");
            
        } catch (\Exception $e) {
            $this->error('Error importing document_files: ' . $e->getMessage());
        }
    }
}
