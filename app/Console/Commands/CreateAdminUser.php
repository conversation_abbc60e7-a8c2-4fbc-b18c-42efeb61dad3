<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-admin-user {email?} {name?} {password?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new admin user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? $this->ask('Enter admin email', '<EMAIL>');
        $name = $this->argument('name') ?? $this->ask('Enter admin name', 'Admin User');
        $password = $this->argument('password') ?? $this->secret('Enter admin password (min 8 characters)') ?? 'password';

        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email format');
            return 1;
        }

        // Validate password
        if (strlen($password) < 8) {
            $this->error('Password must be at least 8 characters');
            return 1;
        }

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            if (!$this->confirm("User with email {$email} already exists. Do you want to update it?", true)) {
                $this->info('Operation cancelled');
                return 0;
            }
        }

        // Create or update user
        $user = User::updateOrCreate(
            ['email' => $email],
            [
                'name' => $name,
                'password' => Hash::make($password),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign admin role if using spatie/laravel-permission
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $user->assignRole($adminRole);
            $this->info("Admin role assigned to {$email}");
        } else {
            $this->warn("Admin role not found. User created with 'admin' role field only.");
        }

        $this->info("Admin user {$email} created/updated successfully!");
        return 0;
    }
}
