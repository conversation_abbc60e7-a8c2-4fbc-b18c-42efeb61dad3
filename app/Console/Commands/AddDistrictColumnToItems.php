<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddDistrictColumnToItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:add-district-column-to-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add district column to items table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to add district column to items table...');

        // Check if items table exists
        if (!Schema::hasTable('items')) {
            $this->error('Items table does not exist!');
            return 1;
        }

        // Check if district column already exists
        if (Schema::hasColumn('items', 'district')) {
            $this->info('District column already exists in items table.');
            return 0;
        }

        // Add district column to items table
        Schema::table('items', function ($table) {
            $table->string('district')->nullable()->after('amphures');
        });

        $this->info('Added district column to items table.');

        // Import remaining items from test database
        $this->importRemainingItems();

        $this->info('Items table schema fixed successfully!');
        return 0;
    }

    /**
     * Import remaining items from test database
     */
    private function importRemainingItems()
    {
        $this->info('Importing remaining items from test database...');

        try {
            // Get all documents from test database
            $documents = DB::connection('test')->select("SELECT * FROM documents");
            
            if (empty($documents)) {
                $this->warn('No documents found in test database.');
                return;
            }
            
            $this->info('Found ' . count($documents) . ' documents in test database.');
            
            // Get existing items
            $existingItemIds = DB::table('items')->pluck('id')->toArray();
            
            // Import each document that doesn't exist yet
            $importCount = 0;
            foreach ($documents as $document) {
                // Convert document to array
                $documentArray = (array) $document;
                
                // Skip if item already exists
                if (in_array($documentArray['id'], $existingItemIds)) {
                    continue;
                }
                
                // Rename document_type_id to item_type_id if it exists
                if (isset($documentArray['document_type_id'])) {
                    $documentArray['item_type_id'] = $documentArray['document_type_id'];
                    unset($documentArray['document_type_id']);
                }
                
                // Insert into items table
                try {
                    DB::table('items')->insert($documentArray);
                    $importCount++;
                    $this->line("Imported document ID {$documentArray['id']} to items table.");
                } catch (\Exception $e) {
                    $this->error("Error importing document ID {$documentArray['id']}: " . $e->getMessage());
                }
            }
            
            $this->info("Successfully imported {$importCount} additional documents to items table.");
            
        } catch (\Exception $e) {
            $this->error('Error importing documents: ' . $e->getMessage());
        }
    }
}
