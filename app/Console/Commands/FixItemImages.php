<?php

namespace App\Console\Commands;

use App\Models\Item;
use App\Models\ItemImage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class FixItemImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-item-images {--dry-run : Show what would be done without making changes} {--scan-storage : Scan storage directory for images}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix item images by scanning the storage directory and updating the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting item images fix process...');
        $dryRun = $this->option('dry-run');
        $scanStorage = $this->option('scan-storage');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Check if item_images table exists
        if (!Schema::hasTable('item_images')) {
            $this->error('item_images table does not exist. Creating it...');

            if (!$dryRun) {
                Schema::create('item_images', function ($table) {
                    $table->id();
                    $table->unsignedBigInteger('item_id');
                    $table->string('image_path');
                    $table->integer('sort_order')->default(0);
                    $table->timestamps();

                    $table->foreign('item_id')->references('id')->on('items')->onDelete('cascade');
                });

                $this->info('item_images table created successfully.');
            }
        }

        // Get all items
        $items = Item::all();
        $this->info('Found ' . $items->count() . ' items.');

        // Get all images in storage
        $storageImages = [];
        if ($scanStorage) {
            $this->info('Scanning storage directory for images...');
            $storageImages = $this->scanStorageDirectory();
            $this->info('Found ' . count($storageImages) . ' images in storage.');
        }

        $processedCount = 0;

        // Process each item
        foreach ($items as $item) {
            $this->info('Processing item ID: ' . $item->id);

            // Check if item has images
            $images = $item->images;

            if ($images->count() > 0) {
                $this->info('Item has ' . $images->count() . ' images.');

                // Check if images exist in storage
                foreach ($images as $image) {
                    $imagePath = $image->image_path;
                    $storagePath = storage_path('app/public/images/' . $imagePath);

                    if (!file_exists($storagePath)) {
                        $this->warn('Image does not exist in storage: ' . $imagePath);

                        // Try to find a matching image in storage
                        if ($scanStorage) {
                            $matchingImage = $this->findMatchingImage($imagePath, $storageImages);

                            if ($matchingImage) {
                                $this->info('Found matching image in storage: ' . $matchingImage);

                                if (!$dryRun) {
                                    $image->image_path = $matchingImage;
                                    $image->save();
                                    $this->info('Updated image path to: ' . $matchingImage);
                                } else {
                                    $this->info('Would update image path to: ' . $matchingImage);
                                }
                            }
                        }
                    } else {
                        $this->info('Image exists in storage: ' . $imagePath);
                    }
                }
            } else {
                $this->warn('Item has no images.');

                // If scanning storage, try to find images for this item
                if ($scanStorage) {
                    $itemId = $item->id;
                    $itemIdentifier = $item->identifier_no;

                    // Try to find images for this item
                    $matchingImages = $this->findImagesForItem($itemId, $itemIdentifier, $storageImages);

                    if (count($matchingImages) > 0) {
                        $this->info('Found ' . count($matchingImages) . ' potential images for item.');

                        if (!$dryRun) {
                            foreach ($matchingImages as $index => $imagePath) {
                                $itemImage = new ItemImage();
                                $itemImage->item_id = $itemId;
                                $itemImage->image_path = $imagePath;
                                $itemImage->sort_order = $index;
                                $itemImage->save();

                                $this->info('Added image to item: ' . $imagePath);
                            }
                        } else {
                            foreach ($matchingImages as $imagePath) {
                                $this->info('Would add image to item: ' . $imagePath);
                            }
                        }
                    }
                }
            }

            $processedCount++;

            // Show progress
            if ($processedCount % 10 === 0) {
                $this->info('Processed ' . $processedCount . ' of ' . $items->count() . ' items.');
            }
        }

        $this->info('Item images fix process completed. Processed ' . $processedCount . ' items.');
    }

    /**
     * Scan the storage directory for images
     */
    private function scanStorageDirectory()
    {
        $images = [];
        $storagePath = storage_path('app/public/images');

        if (!is_dir($storagePath)) {
            $this->error('Storage directory does not exist: ' . $storagePath);
            return $images;
        }

        $files = scandir($storagePath);

        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $filePath = $storagePath . '/' . $file;

            if (is_file($filePath)) {
                $extension = pathinfo($file, PATHINFO_EXTENSION);

                if (in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $images[] = $file;
                }
            }
        }

        return $images;
    }

    /**
     * Find a matching image in storage
     */
    private function findMatchingImage($imagePath, $storageImages)
    {
        // Get the filename without path
        $filename = basename($imagePath);

        // Check for exact match
        if (in_array($filename, $storageImages)) {
            return $filename;
        }

        // Check for case-insensitive match
        foreach ($storageImages as $storageImage) {
            if (strtolower($filename) === strtolower($storageImage)) {
                return $storageImage;
            }
        }

        // Check for similar match (e.g. same name but different extension)
        $filenameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);

        foreach ($storageImages as $storageImage) {
            $storageFilenameWithoutExt = pathinfo($storageImage, PATHINFO_FILENAME);

            if ($filenameWithoutExt === $storageFilenameWithoutExt) {
                return $storageImage;
            }
        }

        return null;
    }

    /**
     * Find images for an item
     */
    private function findImagesForItem($itemId, $itemIdentifier, $storageImages)
    {
        $matchingImages = [];

        // If item identifier is empty, use item ID
        $searchTerm = $itemIdentifier ?: $itemId;

        if (empty($searchTerm)) {
            return $matchingImages;
        }

        // Convert to string and remove special characters
        $searchTerm = (string) $searchTerm;
        $searchTerm = preg_replace('/[^a-zA-Z0-9]/', '', $searchTerm);

        foreach ($storageImages as $storageImage) {
            $imageNameWithoutExt = pathinfo($storageImage, PATHINFO_FILENAME);
            $imageNameWithoutExt = preg_replace('/[^a-zA-Z0-9]/', '', $imageNameWithoutExt);

            // Check if image name contains item ID or identifier
            if (stripos($imageNameWithoutExt, $searchTerm) !== false) {
                $matchingImages[] = $storageImage;
            }
        }

        return $matchingImages;
    }
}
