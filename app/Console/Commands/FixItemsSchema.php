<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixItemsSchema extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-items-schema';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix items table schema to match the test database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix items table schema...');

        // Check if items table exists
        if (!Schema::hasTable('items')) {
            $this->error('Items table does not exist!');
            return 1;
        }

        // Check if we need to add other_title columns
        $this->addOtherTitleColumns();

        // Import remaining items from test database
        $this->importRemainingItems();

        $this->info('Items table schema fixed successfully!');
        return 0;
    }

    /**
     * Add other_title columns to items table
     */
    private function addOtherTitleColumns()
    {
        $this->info('Checking for other_title columns...');

        $columnsToAdd = [
            'other_title1' => 'Add other_title1 column to items table',
            'other_title2' => 'Add other_title2 column to items table',
            'other_title3' => 'Add other_title3 column to items table',
        ];

        foreach ($columnsToAdd as $column => $message) {
            if (!Schema::hasColumn('items', $column)) {
                $this->info($message);
                
                Schema::table('items', function ($table) use ($column) {
                    $table->string($column)->nullable()->after('title');
                });
                
                $this->info("Added {$column} column to items table.");
            } else {
                $this->info("{$column} column already exists.");
            }
        }
    }

    /**
     * Import remaining items from test database
     */
    private function importRemainingItems()
    {
        $this->info('Importing remaining items from test database...');

        try {
            // Get all documents from test database
            $documents = DB::connection('test')->select("SELECT * FROM documents");
            
            if (empty($documents)) {
                $this->warn('No documents found in test database.');
                return;
            }
            
            $this->info('Found ' . count($documents) . ' documents in test database.');
            
            // Get existing items
            $existingItemIds = DB::table('items')->pluck('id')->toArray();
            
            // Import each document that doesn't exist yet
            $importCount = 0;
            foreach ($documents as $document) {
                // Convert document to array
                $documentArray = (array) $document;
                
                // Skip if item already exists
                if (in_array($documentArray['id'], $existingItemIds)) {
                    continue;
                }
                
                // Rename document_type_id to item_type_id if it exists
                if (isset($documentArray['document_type_id'])) {
                    $documentArray['item_type_id'] = $documentArray['document_type_id'];
                    unset($documentArray['document_type_id']);
                }
                
                // Insert into items table
                try {
                    DB::table('items')->insert($documentArray);
                    $importCount++;
                    $this->line("Imported document ID {$documentArray['id']} to items table.");
                } catch (\Exception $e) {
                    $this->error("Error importing document ID {$documentArray['id']}: " . $e->getMessage());
                }
            }
            
            $this->info("Successfully imported {$importCount} additional documents to items table.");
            
        } catch (\Exception $e) {
            $this->error('Error importing documents: ' . $e->getMessage());
        }
    }
}
