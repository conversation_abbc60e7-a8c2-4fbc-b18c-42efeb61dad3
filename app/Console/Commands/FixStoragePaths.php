<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class FixStoragePaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:fix-paths';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix files stored in incorrect paths';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix storage paths...');
        
        // Check if the incorrect path exists
        $incorrectPath = storage_path('app/private/public');
        if (!File::exists($incorrectPath)) {
            $this->info('No incorrect path found at: ' . $incorrectPath);
            return;
        }
        
        // Get all files in the incorrect path
        $files = File::allFiles($incorrectPath);
        $this->info('Found ' . count($files) . ' files in incorrect path');
        
        $moved = 0;
        $errors = 0;
        
        foreach ($files as $file) {
            // Get the relative path from the incorrect base
            $relativePath = str_replace($incorrectPath . '/', '', $file->getPathname());
            
            // Determine the correct path
            $correctPath = storage_path('app/public/' . $relativePath);
            
            // Ensure the directory exists
            $correctDir = dirname($correctPath);
            if (!File::exists($correctDir)) {
                File::makeDirectory($correctDir, 0755, true);
            }
            
            try {
                // Copy the file to the correct location
                File::copy($file->getPathname(), $correctPath);
                
                // Also copy to the public directory
                $publicPath = public_path('storage/' . $relativePath);
                $publicDir = dirname($publicPath);
                
                if (!File::exists($publicDir)) {
                    File::makeDirectory($publicDir, 0755, true);
                }
                
                File::copy($file->getPathname(), $publicPath);
                
                $this->info('Moved: ' . $relativePath);
                $moved++;
            } catch (\Exception $e) {
                $this->error('Error moving ' . $relativePath . ': ' . $e->getMessage());
                Log::error('Error moving file: ' . $e->getMessage());
                $errors++;
            }
        }
        
        $this->info('Completed moving files:');
        $this->info('- Successfully moved: ' . $moved);
        $this->info('- Errors: ' . $errors);
    }
}
