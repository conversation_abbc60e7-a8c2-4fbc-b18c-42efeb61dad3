<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class UpdateUserPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:update-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update user permissions based on their role field';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating user permissions...');

        // Get all users
        $users = User::all();
        $count = 0;

        foreach ($users as $user) {
            // Skip users who already have roles assigned
            if ($user->roles->count() > 0) {
                $this->line("User {$user->name} already has roles assigned. Skipping.");
                continue;
            }

            // Assign role based on the 'role' field
            switch ($user->role) {
                case 'admin':
                    $user->assignRole('admin');
                    $this->line("Assigned admin role to {$user->name}");
                    $count++;
                    break;
                case 'editor':
                    $user->assignRole('editor');
                    $this->line("Assigned editor role to {$user->name}");
                    $count++;
                    break;
                default:
                    $user->assignRole('user');
                    $this->line("Assigned user role to {$user->name}");
                    $count++;
                    break;
            }
        }

        $this->info("Updated permissions for {$count} users.");
    }
}
