<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FixDatabasePaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:fix-database-paths';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix database records with incorrect file paths';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix database paths...');
        
        // Fix document_images table
        $this->fixTable('document_images', 'image_path');
        
        // Fix document_files table
        $this->fixTable('document_files', 'file_path');
        
        $this->info('Database path fixing completed.');
    }
    
    /**
     * Fix paths in a specific table and column
     */
    private function fixTable($tableName, $columnName)
    {
        $this->info("Checking {$tableName} table...");
        
        // Find records with incorrect paths
        $records = DB::table($tableName)
            ->whereNotNull($columnName)
            ->where(function($query) use ($columnName) {
                $query->where($columnName, 'like', '/storage/public/%')
                      ->orWhere($columnName, 'like', 'storage/public/%')
                      ->orWhere($columnName, 'like', '/public/public/%')
                      ->orWhere($columnName, 'like', 'public/public/%');
            })
            ->get();
        
        $this->info("Found {$records->count()} records with incorrect paths in {$tableName}");
        
        $updated = 0;
        
        foreach ($records as $record) {
            $oldPath = $record->$columnName;
            
            // Fix the path by removing the extra 'public/'
            $newPath = preg_replace('#(/storage/|storage/|/|^)(public/)+#', '$1', $oldPath);
            
            // Ensure the path starts with /storage/
            if (!str_starts_with($newPath, '/storage/')) {
                $newPath = '/storage/' . ltrim($newPath, '/');
            }
            
            try {
                // Update the record
                DB::table($tableName)
                    ->where('id', $record->id)
                    ->update([$columnName => $newPath]);
                
                $this->info("Updated ID {$record->id}: {$oldPath} -> {$newPath}");
                $updated++;
            } catch (\Exception $e) {
                $this->error("Error updating ID {$record->id}: " . $e->getMessage());
                Log::error("Error updating path in {$tableName}: " . $e->getMessage());
            }
        }
        
        $this->info("Updated {$updated} records in {$tableName}");
    }
}
