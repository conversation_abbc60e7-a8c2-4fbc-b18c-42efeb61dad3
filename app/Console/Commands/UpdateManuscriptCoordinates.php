<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\UpdateManuscriptCoordinatesSeeder;

class UpdateManuscriptCoordinates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-manuscript-coordinates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update document coordinates from manuscript.sql file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update document coordinates from manuscript.sql...');
        
        $seeder = new UpdateManuscriptCoordinatesSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Coordinates update completed!');
        
        return 0;
    }
}
