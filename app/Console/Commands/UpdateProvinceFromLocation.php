<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Item;
use App\Models\Province;
use Illuminate\Support\Facades\DB;

class UpdateProvinceFromLocation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-province-from-location';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update province and province_id fields in items table by extracting province from location field';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update province field from location...');

        // โหลดข้อมูลจังหวัดทั้งหมด
        $provinces = Province::all();
        $provinceMap = [];
        $provinceIdMap = [];

        // สร้าง map ของชื่อจังหวัดและรหัส
        foreach ($provinces as $province) {
            // ชื่อจังหวัดภาษาไทย -> code
            $provinceMap[mb_strtolower($province->name_th)] = $province->code;

            // ชื่อจังหวัดภาษาไทย -> id
            $provinceIdMap[mb_strtolower($province->name_th)] = $province->id;

            // เพิ่มชื่อจังหวัดแบบไม่มีคำว่า "จังหวัด"
            $shortName = mb_strtolower(str_replace('จังหวัด', '', $province->name_th));
            $provinceMap[$shortName] = $province->code;
            $provinceIdMap[$shortName] = $province->id;
        }

        // ดึงรายการที่มีข้อมูล location
        $items = Item::whereNotNull('location')
                    ->get();

        $this->info("Found {$items->count()} items with location field");

        $updatedCount = 0;
        $notFoundCount = 0;

        foreach ($items as $item) {
            $location = $item->location;
            $provinceCode = null;
            $provinceId = null;
            $provinceName = null;

            // ค้นหาชื่อจังหวัดจากข้อความในฟิลด์ location
            if (preg_match('/จ\.([\p{Thai}\s]+)/u', $location, $matches)) {
                $provinceName = trim($matches[1]);
                $provinceKey = mb_strtolower($provinceName);

                if (isset($provinceMap[$provinceKey])) {
                    $provinceCode = $provinceMap[$provinceKey];
                    $provinceId = $provinceIdMap[$provinceKey];
                }
            } elseif (preg_match('/จังหวัด([\p{Thai}\s]+)/u', $location, $matches)) {
                $provinceName = trim($matches[1]);
                $provinceKey = mb_strtolower($provinceName);

                if (isset($provinceMap[$provinceKey])) {
                    $provinceCode = $provinceMap[$provinceKey];
                    $provinceId = $provinceIdMap[$provinceKey];
                }
            } else {
                // ค้นหาชื่อจังหวัดโดยตรงในข้อความ
                foreach ($provinceMap as $name => $code) {
                    if (mb_stripos($location, $name) !== false) {
                        $provinceCode = $code;
                        $provinceId = $provinceIdMap[$name];
                        break;
                    }
                }
            }

            // เพิ่มการตรวจสอบกรณีพิเศษ
            if (!$provinceCode) {
                // กรณีเชียงตุง
                if (mb_stripos($location, 'เชียงตุง') !== false) {
                    $provinceCode = 'MM'; // รหัสประเทศพม่า
                }
                // กรณีเมืองยอง
                elseif (mb_stripos($location, 'เมืองยอง') !== false) {
                    $provinceCode = 'MM'; // รหัสประเทศพม่า
                }
                // กรณีพะยา (พะเยา)
                elseif (mb_stripos($location, 'พะยา') !== false) {
                    $provinceCode = '56'; // รหัสจังหวัดพะเยา
                    $province = Province::where('code', '56')->first();
                    if ($province) {
                        $provinceId = $province->id;
                    }
                }
            }

            if ($provinceCode) {
                $item->province = $provinceCode;

                // ไม่ต้องอัปเดต province_id เพราะเราใช้ province แทน
                $this->info("Updated item ID {$item->id}: Set province code to {$provinceCode} from location: {$location}");

                $item->save();
                $updatedCount++;
            } else {
                $this->warn("Could not extract province from location: {$location}");
                $notFoundCount++;
            }
        }

        $this->info("Province update from location completed:");
        $this->info("- Updated: $updatedCount items");
        $this->info("- Not found: $notFoundCount items");

        // ไม่ต้องอัปเดต province_id เพราะเราใช้ province แทน

        return 0;
    }
}
