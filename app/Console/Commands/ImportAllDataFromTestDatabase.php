<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ImportAllDataFromTestDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-all-data-from-test-database';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import all data from test database to main database';

    /**
     * Tables to import in the correct order to respect foreign key constraints
     */
    protected $tables = [
        'countries',
        'provinces',
        'amphures',
        'districts',
        'categories',
        'item_types',
        'materials',
        'languages',
        'scripts',
        'documents', // Will be imported to items table
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to import all data from test database...');

        // Check if test database connection is configured
        try {
            DB::connection('test')->getPdo();
        } catch (\Exception $e) {
            $this->error('Test database connection failed: ' . $e->getMessage());
            return 1;
        }

        // Import tables in order
        foreach ($this->tables as $table) {
            if ($table === 'documents') {
                $this->importDocumentsToItems();
            } else {
                $this->importTable($table);
            }
        }

        $this->info('All data imported successfully!');
        return 0;
    }

    /**
     * Import a table from test database to main database
     */
    private function importTable($table)
    {
        $this->info("Importing table: {$table}");

        // Check if table exists in test database
        try {
            $testTableExists = DB::connection('test')->select("SHOW TABLES LIKE '{$table}'");
            if (empty($testTableExists)) {
                $this->warn("Table {$table} does not exist in test database. Skipping.");
                return;
            }
        } catch (\Exception $e) {
            $this->error("Error checking table {$table} in test database: " . $e->getMessage());
            return;
        }

        // Check if table exists in main database
        if (!Schema::hasTable($table)) {
            $this->warn("Table {$table} does not exist in main database. Skipping.");
            return;
        }

        try {
            // Get data from test database
            $data = DB::connection('test')->table($table)->get();
            
            if ($data->isEmpty()) {
                $this->warn("No data found in table {$table}. Skipping.");
                return;
            }
            
            $this->info("Found " . count($data) . " records in table {$table}.");
            
            // Get existing IDs in main database
            $existingIds = DB::table($table)->pluck('id')->toArray();
            
            // Get columns from main database table
            $columns = Schema::getColumnListing($table);
            
            // Import each record
            $importCount = 0;
            $errorCount = 0;
            
            foreach ($data as $record) {
                // Convert record to array
                $recordArray = (array) $record;
                
                // Skip if record already exists
                if (in_array($recordArray['id'], $existingIds)) {
                    continue;
                }
                
                // Filter out columns that don't exist in the main table
                $filteredData = array_intersect_key($recordArray, array_flip($columns));
                
                // Insert into main database
                try {
                    DB::table($table)->insert($filteredData);
                    $importCount++;
                } catch (\Exception $e) {
                    $this->error("Error importing record ID {$recordArray['id']} to table {$table}: " . $e->getMessage());
                    $errorCount++;
                }
            }
            
            $this->info("Successfully imported {$importCount} records to table {$table}.");
            if ($errorCount > 0) {
                $this->warn("{$errorCount} records failed to import.");
            }
            
        } catch (\Exception $e) {
            $this->error("Error importing table {$table}: " . $e->getMessage());
        }
    }

    /**
     * Import documents table to items table
     */
    private function importDocumentsToItems()
    {
        $this->info("Importing documents to items table");

        try {
            // Get data from test database
            $documents = DB::connection('test')->table('documents')->get();
            
            if ($documents->isEmpty()) {
                $this->warn("No documents found in test database. Skipping.");
                return;
            }
            
            $this->info("Found " . count($documents) . " documents in test database.");
            
            // Get existing items
            $existingItemIds = DB::table('items')->pluck('id')->toArray();
            
            // Get columns from items table
            $itemColumns = Schema::getColumnListing('items');
            
            // Import each document
            $importCount = 0;
            $errorCount = 0;
            
            foreach ($documents as $document) {
                // Convert document to array
                $documentArray = (array) $document;
                
                // Skip if item already exists
                if (in_array($documentArray['id'], $existingItemIds)) {
                    continue;
                }
                
                // Rename document_type_id to item_type_id if it exists
                if (isset($documentArray['document_type_id'])) {
                    $documentArray['item_type_id'] = $documentArray['document_type_id'];
                    unset($documentArray['document_type_id']);
                }
                
                // Filter out columns that don't exist in the items table
                $filteredData = array_intersect_key($documentArray, array_flip($itemColumns));
                
                // Insert into items table
                try {
                    DB::table('items')->insert($filteredData);
                    $importCount++;
                } catch (\Exception $e) {
                    $this->error("Error importing document ID {$documentArray['id']} to items table: " . $e->getMessage());
                    $errorCount++;
                }
            }
            
            $this->info("Successfully imported {$importCount} documents to items table.");
            if ($errorCount > 0) {
                $this->warn("{$errorCount} documents failed to import.");
            }
            
        } catch (\Exception $e) {
            $this->error("Error importing documents: " . $e->getMessage());
        }
    }
}
