<?php

namespace App\Console\Commands;

use App\Models\DocumentImage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class UpdateDocumentImagePathsFull extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-document-image-paths-full {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update image paths in document_images table to match the full path format used in document_files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting image path update process to full paths...');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Get all document images
        $images = DocumentImage::all();
        $this->info('Found ' . $images->count() . ' document images.');

        $updatedCount = 0;
        $errorCount = 0;

        foreach ($images as $image) {
            $currentPath = $image->image_path;

            // Skip if the path is empty
            if (empty($currentPath)) {
                $this->warn('Empty path found for image ID: ' . $image->id);
                continue;
            }

            // Check if the path already starts with '/storage/'
            if (str_starts_with($currentPath, '/storage/')) {
                $this->line('Image ID ' . $image->id . ' already has correct full path format: ' . $currentPath);
                continue;
            }

            // If the path starts with 'images/', remove it to get just the filename
            if (str_starts_with($currentPath, 'images/')) {
                $fileName = substr($currentPath, 7); // Remove 'images/' prefix
            } else {
                $fileName = $currentPath;
            }

            // Create the new full path
            $newPath = '/storage/images/' . $fileName;

            // Check if the file exists in storage
            $storageExists = Storage::disk('public')->exists('images/' . $fileName);

            if (!$storageExists) {
                $this->warn('Image file not found in storage: ' . $fileName);
                $errorCount++;
                continue;
            }

            $this->info('Updating image ID ' . $image->id . ': ' . $currentPath . ' -> ' . $newPath);

            if (!$dryRun) {
                try {
                    $image->image_path = $newPath;
                    $image->save();
                    $updatedCount++;
                } catch (\Exception $e) {
                    $this->error('Error updating image ID ' . $image->id . ': ' . $e->getMessage());
                    $errorCount++;
                }
            } else {
                $updatedCount++;
            }
        }

        $this->info('Image path update process completed.');
        $this->info('Updated: ' . $updatedCount . ' images.');
        $this->info('Errors: ' . $errorCount . ' images.');
    }
}
