<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class UpdateFileSizes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-file-sizes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update file sizes for all item files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting file size update process...');

        // Get all item files
        $files = \App\Models\ItemFile::all();
        $this->info("Found " . $files->count() . " files to process.");

        $updatedCount = 0;
        $errorCount = 0;
        $bar = $this->output->createProgressBar($files->count());
        $bar->start();

        foreach ($files as $file) {
            try {
                // Skip if file_size is already set and not 0
                if ($file->file_size > 0) {
                    $this->comment("Skipping file ID {$file->id} - already has file_size: {$file->file_size}");
                    $bar->advance();
                    continue;
                }

                // Extract the actual path without /storage/ prefix
                $filePath = $file->file_path;
                $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $filePath);

                // Ensure the path starts with 'files/'
                if (!str_starts_with($pathWithoutStorage, 'files/')) {
                    $pathWithoutStorage = 'files/' . $pathWithoutStorage;
                }

                // Try to get file size from public storage
                $fileSize = 0;
                $publicPath = public_path('storage/' . $pathWithoutStorage);

                if (file_exists($publicPath)) {
                    $fileSize = filesize($publicPath);
                    $this->comment("Got file size from public path for file ID {$file->id}: {$fileSize} bytes");
                }
                // Try storage path if public path doesn't exist
                else {
                    $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                    if (file_exists($storagePath)) {
                        $fileSize = filesize($storagePath);
                        $this->comment("Got file size from storage path for file ID {$file->id}: {$fileSize} bytes");
                    }
                    // Try with the original path as a fallback
                    else {
                        // Try with the original path
                        $originalPath = public_path(ltrim($filePath, '/'));
                        if (file_exists($originalPath)) {
                            $fileSize = filesize($originalPath);
                            $this->comment("Got file size from original path for file ID {$file->id}: {$fileSize} bytes");
                        }
                        // Try with storage path using the original path
                        else {
                            $originalStoragePath = storage_path('app/public/' . ltrim(str_replace('/storage/', '', $filePath), '/'));
                            if (file_exists($originalStoragePath)) {
                                $fileSize = filesize($originalStoragePath);
                                $this->comment("Got file size from original storage path for file ID {$file->id}: {$fileSize} bytes");
                            }
                            // Try with documents path as a fallback
                            else {
                                $documentsPath = str_replace('files/', 'documents/', $pathWithoutStorage);
                                $documentsPublicPath = public_path('storage/' . $documentsPath);
                                if (file_exists($documentsPublicPath)) {
                                    $fileSize = filesize($documentsPublicPath);
                                    $this->comment("Got file size from documents public path for file ID {$file->id}: {$fileSize} bytes");
                                }
                                else {
                                    $documentsStoragePath = storage_path('app/public/' . $documentsPath);
                                    if (file_exists($documentsStoragePath)) {
                                        $fileSize = filesize($documentsStoragePath);
                                        $this->comment("Got file size from documents storage path for file ID {$file->id}: {$fileSize} bytes");
                                    }
                                    else {
                                        $this->error("Could not find file for file ID {$file->id} at path: {$filePath}");
                                        $bar->advance();
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                }

                // Update the file_size field
                if ($fileSize > 0) {
                    $file->file_size = $fileSize;
                    $file->save();
                    $updatedCount++;
                    $this->comment("Updated file ID {$file->id} with file_size: {$fileSize} bytes");
                }
            } catch (\Exception $e) {
                $errorCount++;
                $this->error("Error processing file ID {$file->id}: " . $e->getMessage());
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        $this->info("Update summary:");
        $this->info("Total files processed: " . $files->count());
        $this->info("Files updated: {$updatedCount}");
        $this->info("Errors: {$errorCount}");

        return 0;
    }
}
