<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FixDoublePublicPath extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:fix-double-public';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix files stored in public/public path instead of public';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to fix double public paths...');
        
        // Check if the incorrect path exists
        $incorrectPath = storage_path('app/public/public');
        if (!File::exists($incorrectPath)) {
            $this->info('No incorrect path found at: ' . $incorrectPath);
            return;
        }
        
        // Get all directories in the incorrect path
        $directories = File::directories($incorrectPath);
        $this->info('Found ' . count($directories) . ' directories in incorrect path');
        
        $moved = 0;
        $errors = 0;
        
        // Process each directory
        foreach ($directories as $directory) {
            $dirName = basename($directory);
            $this->info('Processing directory: ' . $dirName);
            
            // Get all files in the directory
            $files = File::allFiles($directory);
            $this->info('Found ' . count($files) . ' files in ' . $dirName);
            
            foreach ($files as $file) {
                // Get the relative path from the incorrect base
                $relativePath = str_replace($incorrectPath . '/', '', $file->getPathname());
                
                // Determine the correct path
                $correctPath = storage_path('app/public/' . $relativePath);
                
                // Ensure the directory exists
                $correctDir = dirname($correctPath);
                if (!File::exists($correctDir)) {
                    File::makeDirectory($correctDir, 0755, true);
                }
                
                try {
                    // Copy the file to the correct location
                    File::copy($file->getPathname(), $correctPath);
                    
                    // Also copy to the public directory
                    $publicPath = public_path('storage/' . $relativePath);
                    $publicDir = dirname($publicPath);
                    
                    if (!File::exists($publicDir)) {
                        File::makeDirectory($publicDir, 0755, true);
                    }
                    
                    File::copy($file->getPathname(), $publicPath);
                    
                    $this->info('Moved: ' . $relativePath);
                    $moved++;
                } catch (\Exception $e) {
                    $this->error('Error moving ' . $relativePath . ': ' . $e->getMessage());
                    Log::error('Error moving file: ' . $e->getMessage());
                    $errors++;
                }
            }
        }
        
        $this->info('Completed moving files:');
        $this->info('- Successfully moved: ' . $moved);
        $this->info('- Errors: ' . $errors);
        
        // Ask if user wants to delete the incorrect directory
        if ($moved > 0 && $this->confirm('Do you want to delete the incorrect directory?')) {
            try {
                File::deleteDirectory($incorrectPath);
                $this->info('Deleted incorrect directory: ' . $incorrectPath);
            } catch (\Exception $e) {
                $this->error('Error deleting directory: ' . $e->getMessage());
            }
        }
        
        // Update database records if needed
        if ($moved > 0 && $this->confirm('Do you want to update database records to use the correct paths?')) {
            $this->call('storage:fix-database-paths');
        }
    }
}
