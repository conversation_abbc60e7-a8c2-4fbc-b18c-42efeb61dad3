<?php

namespace App\Console\Commands;

use App\Models\Document;
use App\Models\DocumentImage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class FixDocumentImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-document-images {--dry-run : Show what would be done without making changes} {--scan-storage : Scan storage directory for images}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix document images by scanning the storage directory and updating the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting document images fix process...');
        $dryRun = $this->option('dry-run');
        $scanStorage = $this->option('scan-storage');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Check if document_images table exists
        if (!Schema::hasTable('document_images')) {
            $this->error('document_images table does not exist. Creating it...');

            if (!$dryRun) {
                Schema::create('document_images', function ($table) {
                    $table->id();
                    $table->unsignedBigInteger('document_id');
                    $table->string('image_path');
                    $table->integer('sort_order')->default(0);
                    $table->timestamps();

                    $table->foreign('document_id')->references('id')->on('documents')->onDelete('cascade');
                });

                $this->info('document_images table created successfully.');
            }
        }

        // Get all documents
        $documents = Document::all();
        $this->info('Found ' . $documents->count() . ' documents.');

        // Get all images in storage
        $storageImages = [];
        if ($scanStorage) {
            $this->info('Scanning storage directory for images...');
            $storageImages = $this->scanStorageDirectory();
            $this->info('Found ' . count($storageImages) . ' images in storage.');
        }

        $processedCount = 0;

        // Process each document
        foreach ($documents as $document) {
            $this->info('Processing document ID: ' . $document->id);

            // Check if document has images
            $images = $document->images;

            if ($images->count() > 0) {
                $this->info('Document has ' . $images->count() . ' images.');

                // Check if images exist in storage
                foreach ($images as $image) {
                    $imagePath = $image->image_path;
                    $storagePath = storage_path('app/public/images/' . $imagePath);

                    if (!file_exists($storagePath)) {
                        $this->warn('Image does not exist in storage: ' . $imagePath);

                        // Try to find a matching image in storage
                        if ($scanStorage) {
                            $matchingImage = $this->findMatchingImage($imagePath, $storageImages);

                            if ($matchingImage) {
                                $this->info('Found matching image in storage: ' . $matchingImage);

                                if (!$dryRun) {
                                    $image->image_path = $matchingImage;
                                    $image->save();
                                    $this->info('Updated image path to: ' . $matchingImage);
                                } else {
                                    $this->info('Would update image path to: ' . $matchingImage);
                                }
                            }
                        }
                    } else {
                        $this->info('Image exists in storage: ' . $imagePath);
                    }
                }
            } else {
                $this->warn('Document has no images.');

                // If scanning storage, try to find images for this document
                if ($scanStorage) {
                    $documentId = $document->id;
                    $documentIdentifier = $document->identifier_no;

                    // Try to find images for this document
                    $matchingImages = $this->findImagesForDocument($documentId, $documentIdentifier, $storageImages);

                    if (count($matchingImages) > 0) {
                        $this->info('Found ' . count($matchingImages) . ' potential images for document.');

                        if (!$dryRun) {
                            foreach ($matchingImages as $index => $imagePath) {
                                $documentImage = new DocumentImage();
                                $documentImage->document_id = $documentId;
                                $documentImage->image_path = $imagePath;
                                $documentImage->sort_order = $index;
                                $documentImage->save();

                                $this->info('Added image to document: ' . $imagePath);
                            }
                        } else {
                            foreach ($matchingImages as $imagePath) {
                                $this->info('Would add image to document: ' . $imagePath);
                            }
                        }
                    }
                }
            }

            $processedCount++;

            // Show progress
            if ($processedCount % 10 === 0) {
                $this->info('Processed ' . $processedCount . ' of ' . $documents->count() . ' documents.');
            }
        }

        $this->info('Document images fix process completed. Processed ' . $processedCount . ' documents.');
    }

    /**
     * Scan the storage directory for images
     */
    private function scanStorageDirectory()
    {
        $images = [];
        $storagePath = storage_path('app/public/images');

        if (!is_dir($storagePath)) {
            $this->error('Storage directory does not exist: ' . $storagePath);
            return $images;
        }

        $files = scandir($storagePath);

        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $filePath = $storagePath . '/' . $file;

            if (is_file($filePath)) {
                $extension = pathinfo($file, PATHINFO_EXTENSION);

                if (in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif'])) {
                    $images[] = $file;
                }
            }
        }

        return $images;
    }

    /**
     * Find a matching image in storage
     */
    private function findMatchingImage($imagePath, $storageImages)
    {
        // Get the filename without path
        $filename = basename($imagePath);

        // Check for exact match
        if (in_array($filename, $storageImages)) {
            return $filename;
        }

        // Check for case-insensitive match
        foreach ($storageImages as $storageImage) {
            if (strtolower($filename) === strtolower($storageImage)) {
                return $storageImage;
            }
        }

        // Check for similar match (e.g. same name but different extension)
        $filenameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);

        foreach ($storageImages as $storageImage) {
            $storageFilenameWithoutExt = pathinfo($storageImage, PATHINFO_FILENAME);

            if ($filenameWithoutExt === $storageFilenameWithoutExt) {
                return $storageImage;
            }
        }

        return null;
    }

    /**
     * Find images for a document
     */
    private function findImagesForDocument($documentId, $documentIdentifier, $storageImages)
    {
        $matchingImages = [];

        // If document identifier is empty, use document ID
        $searchTerm = $documentIdentifier ?: $documentId;

        if (empty($searchTerm)) {
            return $matchingImages;
        }

        // Convert to string and remove special characters
        $searchTerm = (string) $searchTerm;
        $searchTerm = preg_replace('/[^a-zA-Z0-9]/', '', $searchTerm);

        foreach ($storageImages as $storageImage) {
            $imageNameWithoutExt = pathinfo($storageImage, PATHINFO_FILENAME);
            $imageNameWithoutExt = preg_replace('/[^a-zA-Z0-9]/', '', $imageNameWithoutExt);

            // Check if image name contains document ID or identifier
            if (stripos($imageNameWithoutExt, $searchTerm) !== false) {
                $matchingImages[] = $storageImage;
            }
        }

        return $matchingImages;
    }
}
