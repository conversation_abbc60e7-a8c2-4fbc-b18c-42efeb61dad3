<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\Document;
use App\Models\Category;
use App\Models\DocumentImage;

class ImportManuscriptData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:manuscript-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import data from manuscript table to documents table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of manuscript data...');

        // Check if manuscript table exists
        if (!Schema::hasTable('manuscript')) {
            $this->error('Manuscript table does not exist!');
            return 1;
        }

        // Get all manuscripts
        $manuscripts = DB::table('manuscript')->get();
        $this->info('Found ' . count($manuscripts) . ' manuscripts to import.');

        // Create progress bar
        $bar = $this->output->createProgressBar(count($manuscripts));
        $bar->start();

        // Import each manuscript
        foreach ($manuscripts as $manuscript) {
            // Find or create category
            $categoryName = $manuscript->category ?? 'Uncategorized';
            $category = Category::firstOrCreate(
                ['name' => $categoryName],
                ['description' => 'Imported from manuscript table']
            );

            // Create document
            $document = Document::create([
                'identifier_no' => $manuscript->identifier_no ?? null,
                'title' => $manuscript->title ?? 'Untitled Document',
                'other_title1' => $manuscript->other_title1 ?? null,
                'other_title2' => $manuscript->other_title2 ?? null,
                'other_title3' => $manuscript->other_title3 ?? null,
                'description' => $manuscript->description ?? null,
                'brief' => $manuscript->brief ?? null,
                'material' => $manuscript->material ?? null,
                'category_id' => $category->id,
                'quantity' => $manuscript->quantity ?? null,
                'year' => $manuscript->year ?? null,
                'language' => $manuscript->language ?? null,
                'script' => $manuscript->script ?? null,
                'creator' => $manuscript->creator ?? null,
                'author' => $manuscript->author ?? null,
                'contributor' => $manuscript->contributor ?? null,
                'location' => $manuscript->location ?? null,
                'country' => $manuscript->country ?? null,
                'province' => $manuscript->provinces ?? null,
                'district' => $manuscript->districts ?? null,
                'latitude' => $manuscript->latitude ?? null,
                'longitude' => $manuscript->longitude ?? null,
                'remark' => $manuscript->remark ?? null,
                'manuscript_condition' => $manuscript->manuscript_condition ?? null,
                'views' => $manuscript->views ?? 0,
                'document_type' => $this->determineDocumentType($manuscript),
                'file_type' => $this->determineFileType($manuscript),
            ]);

            // Import images
            if (!empty($manuscript->image)) {
                $images = explode(',', $manuscript->image);
                foreach ($images as $index => $image) {
                    DocumentImage::create([
                        'document_id' => $document->id,
                        'image_path' => trim($image),
                        'sort_order' => $index + 1,
                    ]);
                }

                // Set first image as document image_path
                if (!empty($images[0])) {
                    $document->image_path = trim($images[0]);
                    $document->save();
                }
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);
        $this->info('Import completed successfully!');

        return 0;
    }

    /**
     * Determine document type based on manuscript data
     */
    private function determineDocumentType($manuscript)
    {
        if (empty($manuscript->material)) {
            return 'unknown';
        }

        $material = strtolower($manuscript->material);

        if (strpos($material, 'ใบลาน') !== false) {
            return 'palm-leaf';
        } elseif (strpos($material, 'พับสา') !== false) {
            return 'folded-paper';
        } elseif (strpos($material, 'จารึก') !== false) {
            return 'inscription';
        } elseif (strpos($material, 'หนังสือ') !== false || strpos($material, 'คัมภีร์') !== false) {
            return 'manuscript';
        } elseif (strpos($material, 'audio') !== false || strpos($material, 'เสียง') !== false) {
            return 'audio';
        } elseif (strpos($material, 'video') !== false || strpos($material, 'วิดีโอ') !== false) {
            return 'video';
        }

        return 'other';
    }

    /**
     * Determine file type based on manuscript data
     */
    private function determineFileType($manuscript)
    {
        $material = strtolower($manuscript->material ?? '');

        if (strpos($material, 'audio') !== false || strpos($material, 'เสียง') !== false) {
            return 'audio';
        } elseif (strpos($material, 'video') !== false || strpos($material, 'วิดีโอ') !== false) {
            return 'video';
        } elseif (strpos($material, 'pdf') !== false || strpos($material, 'document') !== false) {
            return 'pdf';
        }

        // Default to image
        return 'image';
    }
}
