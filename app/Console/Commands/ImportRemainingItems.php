<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ImportRemainingItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-remaining-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import remaining items from test database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to import remaining items from test database...');

        try {
            // Get all documents from test database
            $documents = DB::connection('test')->select("SELECT * FROM documents");
            
            if (empty($documents)) {
                $this->warn('No documents found in test database.');
                return 0;
            }
            
            $this->info('Found ' . count($documents) . ' documents in test database.');
            
            // Get existing items
            $existingItemIds = DB::table('items')->pluck('id')->toArray();
            
            // Get columns from items table
            $itemColumns = Schema::getColumnListing('items');
            
            // Import each document that doesn't exist yet
            $importCount = 0;
            foreach ($documents as $document) {
                // Convert document to array
                $documentArray = (array) $document;
                
                // Skip if item already exists
                if (in_array($documentArray['id'], $existingItemIds)) {
                    continue;
                }
                
                // Rename document_type_id to item_type_id if it exists
                if (isset($documentArray['document_type_id'])) {
                    $documentArray['item_type_id'] = $documentArray['document_type_id'];
                    unset($documentArray['document_type_id']);
                }
                
                // Filter out columns that don't exist in the items table
                $filteredData = array_intersect_key($documentArray, array_flip($itemColumns));
                
                // Insert into items table
                try {
                    DB::table('items')->insert($filteredData);
                    $importCount++;
                    $this->line("Imported document ID {$documentArray['id']} to items table.");
                } catch (\Exception $e) {
                    $this->error("Error importing document ID {$documentArray['id']}: " . $e->getMessage());
                }
            }
            
            $this->info("Successfully imported {$importCount} additional documents to items table.");
            
        } catch (\Exception $e) {
            $this->error('Error importing documents: ' . $e->getMessage());
            return 1;
        }

        $this->info('Import completed successfully!');
        return 0;
    }
}
