<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CleanupDocumentsFolder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:documents-folder {--force : Force cleanup without checking database references} {--hours=1 : Hours to keep files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Aggressively clean up files in the documents-all folder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = $this->option('hours');
        $force = $this->option('force');

        $this->info("Cleaning up documents-all folder. Files older than {$hours} hours will be removed.");

        if ($force) {
            $this->warn("FORCE MODE ENABLED: Files will be deleted without checking database references!");
        }

        // Get the cutoff time
        $cutoffTime = Carbon::now()->subHours($hours);

        // Directory to clean
        $directory = 'documents-all';
        $path = public_path($directory);

        if (!File::exists($path)) {
            $this->error("Directory {$directory} does not exist.");
            return 1;
        }

        $files = File::files($path);
        $this->info("Found " . count($files) . " files in {$directory}");

        $totalRemoved = 0;
        $totalKept = 0;

        foreach ($files as $file) {
            $filename = $file->getFilename();
            $filePath = $directory . '/' . $filename;
            $fileModTime = Carbon::createFromTimestamp($file->getMTime());

            // Check if file is older than cutoff time
            if ($fileModTime->lt($cutoffTime)) {
                // If force mode is enabled or file is not referenced in database
                if ($force || !$this->isFileReferencedInDatabase($filePath)) {
                    // Delete the file
                    File::delete($file->getPathname());
                    $this->info("Deleted: {$filename}");
                    $totalRemoved++;

                    // Log the deletion
                    Log::info("Deleted file from documents-all: {$filePath}");
                } else {
                    $this->line("Kept referenced file: {$filename}");
                    $totalKept++;
                }
            } else {
                $this->line("Kept recent file: {$filename} (modified " . $fileModTime->diffForHumans() . ")");
                $totalKept++;
            }
        }

        $this->info("Cleanup complete. Removed {$totalRemoved} files, kept {$totalKept} files.");
        return 0;
    }

    /**
     * Check if a file is referenced in the database
     * Checks file_path and image_path columns in documents table, document_images table,
     * settings table, and users table
     */
    private function isFileReferencedInDatabase($filePath)
    {
        // Normalize the file path for comparison
        $normalizedPath = $filePath;
        $basename = basename($filePath);

        // Check in documents table file_path column with exact path
        $documentFilePathCount = \DB::table('documents')
            ->where('file_path', $normalizedPath)
            ->count();

        if ($documentFilePathCount > 0) {
            $this->line("File is referenced in documents.file_path: {$normalizedPath}");
            return true;
        }

        // Check in documents table image_path column with basename
        $documentImagePathCount = \DB::table('documents')
            ->where('image_path', $basename)
            ->count();

        if ($documentImagePathCount > 0) {
            $this->line("File is referenced in documents.image_path: {$basename}");
            return true;
        }

        // Check in document_images table
        $imageCount = \DB::table('document_images')
            ->where('image_path', $basename)
            ->count();

        if ($imageCount > 0) {
            $this->line("File is referenced in document_images table: {$basename}");
            return true;
        }

        // Check in settings table (for site_logo, hero_image, favicon)
        $settingsCount = \DB::table('settings')
            ->whereIn('key', ['site_logo', 'hero_image', 'favicon'])
            ->where('value', $basename)
            ->count();

        if ($settingsCount > 0) {
            $this->line("File is referenced in settings table: {$basename}");
            return true;
        }

        // Check in users table profile_image column
        $usersCount = \DB::table('users')
            ->where('profile_image', $basename)
            ->count();

        if ($usersCount > 0) {
            $this->line("File is referenced in users.profile_image: {$basename}");
            return true;
        }

        return false;
    }
}
