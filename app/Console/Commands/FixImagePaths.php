<?php

namespace App\Console\Commands;

use App\Models\Document;
use App\Models\DocumentImage;
use App\Models\Category;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class FixImagePaths extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-image-paths {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix image paths in the database to ensure they are correctly formatted';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting image path fix process...');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Fix document images
        $this->fixDocumentImages($dryRun);

        // Check if documents table has image_path column
        if (Schema::hasColumn('documents', 'image_path')) {
            // Fix document image_path field
            $this->fixDocumentImagePaths($dryRun);
        } else {
            $this->info('Documents table does not have image_path column. Skipping...');
        }

        // Fix category images
        $this->fixCategoryImages($dryRun);

        $this->info('Image path fix process completed.');
    }

    /**
     * Fix document_images table paths
     */
    private function fixDocumentImages($dryRun)
    {
        $this->info('Fixing document_images table...');

        $images = DocumentImage::all();
        $count = 0;

        foreach ($images as $image) {
            $path = $image->image_path;

            // Skip if path is empty
            if (empty($path)) {
                continue;
            }

            // Check if the path needs fixing
            if (str_starts_with($path, 'storage/') || str_starts_with($path, '/storage/')) {
                $newPath = preg_replace('/^(\/)?storage\/images\//', '', $path);

                $this->line("Would change: {$path} -> {$newPath}");

                if (!$dryRun) {
                    $image->image_path = $newPath;
                    $image->save();
                }

                $count++;
            }
        }

        $this->info("Found {$count} document images to fix.");
    }

    /**
     * Fix documents table image_path field
     */
    private function fixDocumentImagePaths($dryRun)
    {
        $this->info('Fixing documents table image_path field...');

        $documents = Document::whereNotNull('image_path')->get();
        $count = 0;

        foreach ($documents as $document) {
            $path = $document->getRawOriginal('image_path'); // Get the raw value, not the accessor

            // Skip if path is empty
            if (empty($path)) {
                continue;
            }

            // Check if the path needs fixing
            if (str_starts_with($path, 'storage/') || str_starts_with($path, '/storage/')) {
                $newPath = preg_replace('/^(\/)?storage\/images\//', '', $path);

                $this->line("Would change document {$document->id}: {$path} -> {$newPath}");

                if (!$dryRun) {
                    $document->image_path = $newPath;
                    $document->save();
                }

                $count++;
            }
        }

        $this->info("Found {$count} document image paths to fix.");
    }

    /**
     * Fix categories table image_path field
     */
    private function fixCategoryImages($dryRun)
    {
        $this->info('Fixing categories table image_path field...');

        $categories = Category::whereNotNull('image_path')->get();
        $count = 0;

        foreach ($categories as $category) {
            $path = $category->getRawOriginal('image_path'); // Get the raw value, not the accessor

            // Skip if path is empty or is the default
            if (empty($path) || $path === 'defaults/category-default.svg') {
                continue;
            }

            // Check if the path needs fixing
            if (str_starts_with($path, 'storage/') || str_starts_with($path, '/storage/')) {
                $newPath = preg_replace('/^(\/)?storage\/images\//', '', $path);

                $this->line("Would change category {$category->id}: {$path} -> {$newPath}");

                if (!$dryRun) {
                    $category->image_path = $newPath;
                    $category->save();
                }

                $count++;
            }
        }

        $this->info("Found {$count} category image paths to fix.");
    }
}
