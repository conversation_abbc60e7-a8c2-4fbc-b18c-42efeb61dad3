<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;

class ImportThaiAddressData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-thai-address-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Thai address data from uatthaphon/laravel-thai-address package';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to import Thai address data...');

        // Step 1: Backup existing provinces data
        $this->backupProvinces();

        // Step 2: Import provinces data from CSV
        $this->importProvinces();

        // Step 3: Update items table to use new province IDs
        $this->updateItemsProvinceIds();

        $this->info('Thai address data imported successfully!');
        return 0;
    }

    /**
     * Backup existing provinces data
     */
    private function backupProvinces()
    {
        $this->info('Backing up existing provinces data...');

        // Create backup table if it doesn't exist
        if (!Schema::hasTable('provinces_backup')) {
            Schema::create('provinces_backup', function ($table) {
                $table->bigIncrements('id');
                $table->string('name_th');
                $table->string('name_en');
                $table->unsignedBigInteger('geography_id')->nullable();
                $table->string('code', 10)->nullable();
                $table->timestamps();
            });

            $this->info('Created provinces_backup table.');
        } else {
            // Clear backup table
            DB::table('provinces_backup')->truncate();
            $this->info('Cleared provinces_backup table.');
        }

        // Copy data to backup table
        $count = DB::table('provinces')->count();
        DB::statement('INSERT INTO provinces_backup SELECT * FROM provinces');
        $this->info("Backed up {$count} provinces.");
    }

    /**
     * Import provinces data from CSV
     */
    private function importProvinces()
    {
        $this->info('Importing provinces data from CSV...');

        // Path to CSV file
        $csvPath = base_path('vendor/uatthaphon/laravel-thai-address/src/database/csv/thailand_provinces.csv');

        if (!File::exists($csvPath)) {
            $this->error("CSV file not found at: {$csvPath}");
            return;
        }

        // Temporarily disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Clear provinces table
        DB::table('provinces')->truncate();
        $this->info('Cleared provinces table.');

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        // Read CSV file
        $file = fopen($csvPath, 'r');
        $header = fgetcsv($file); // Skip header row
        $count = 0;

        while (($row = fgetcsv($file)) !== false) {
            // Skip header row if it's the first row
            if ($count === 0 && $row[0] === 'id') {
                $count++;
                continue;
            }

            $id = $row[0];
            $code = $row[1];
            $name_th = $row[2];
            $name_en = $row[3];

            // Insert province
            DB::table('provinces')->insert([
                'id' => $id,
                'name_th' => $name_th,
                'name_en' => $name_en,
                'geography_id' => null, // We'll update this later if needed
                'code' => $code,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $count++;
        }

        fclose($file);
        $this->info("Imported {$count} provinces.");

        // Update sequence if using PostgreSQL
        // DB::statement("SELECT setval('provinces_id_seq', (SELECT MAX(id) FROM provinces))");
    }

    /**
     * Update items table to use new province IDs
     */
    private function updateItemsProvinceIds()
    {
        $this->info('Updating items table to use new province IDs...');

        // Create a mapping of old province codes to new province IDs
        $oldProvinces = DB::table('provinces_backup')->pluck('id', 'code')->toArray();
        $newProvinces = DB::table('provinces')->pluck('id', 'code')->toArray();

        // Get all items with province_id or province
        $items = DB::table('items')
            ->where(function($query) {
                $query->whereNotNull('province_id')
                      ->orWhereNotNull('province');
            })
            ->get();

        $updatedCount = 0;
        $notFoundCount = 0;

        foreach ($items as $item) {
            $provinceCode = $item->province;
            $newProvinceId = null;

            // If we have a province code directly, use it
            if ($provinceCode && isset($newProvinces[$provinceCode])) {
                $newProvinceId = $newProvinces[$provinceCode];
            }
            // Otherwise try to find the province code from the old province ID
            elseif ($item->province_id) {
                $oldProvinceId = $item->province_id;

                // Find the province code from the old province ID
                foreach ($oldProvinces as $code => $id) {
                    if ($id == $oldProvinceId && isset($newProvinces[$code])) {
                        $newProvinceId = $newProvinces[$code];
                        break;
                    }
                }
            }

            if ($newProvinceId) {
                // Update the item with the new province ID
                DB::table('items')->where('id', $item->id)->update(['province_id' => $newProvinceId]);
                $updatedCount++;
            } else {
                $this->warn("Could not find new province ID for item ID {$item->id} (province: {$item->province}, old province ID: {$item->province_id})");
                $notFoundCount++;
            }
        }

        $this->info("Updated province_id for {$updatedCount} items.");
        $this->info("{$notFoundCount} items could not be updated.");
    }
}
