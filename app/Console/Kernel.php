<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run the general temp files cleanup command daily
        $schedule->command('cleanup:temp-files')->dailyAt('01:00');

        // Run the documents folder cleanup command every 6 hours
        $schedule->command('cleanup:documents-folder --hours=6')->everyFourHours();

        // Run the documents-all folder cleanup command every 2 hours
        $schedule->command('cleanup:documents-all --hours=6')->everyTwoHours();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
