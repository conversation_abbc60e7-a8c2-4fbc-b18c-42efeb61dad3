<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class MaintenanceHelper
{
    /**
     * ตรวจสอบว่าระบบอยู่ในโหมดบำรุงรักษาหรือไม่
     *
     * @return bool
     */
    public static function isInMaintenanceMode()
    {
        $maintenanceMode = Setting::where('key', 'maintenance_mode')->first();
        return $maintenanceMode && $maintenanceMode->value === 'true';
    }

    /**
     * ตรวจสอบว่าเส้นทางปัจจุบันได้รับการยกเว้นจากโหมดบำรุงรักษาหรือไม่
     *
     * @param string $path
     * @return bool
     */
    public static function isExemptFromMaintenance($path)
    {
        // ตรวจสอบว่าผู้ใช้ล็อกอินหรือไม่
        if (auth()->check()) {
            return true; // ผู้ใช้ที่ล็อกอินแล้วสามารถเข้าถึงได้ทุกหน้า
        }

        // ตรวจสอบเส้นทางที่ยกเว้น
        $exemptPaths = [
            'admin', 'admin/*', 'login', 'logout',
            'css/*', 'js/*', 'images/*', 'fonts/*', 'storage/*'
        ];

        foreach ($exemptPaths as $exemptPath) {
            if (fnmatch($exemptPath, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * ส่งคืนการตอบสนองหน้าบำรุงรักษา
     *
     * @return \Illuminate\Http\Response
     */
    public static function getMaintenanceResponse()
    {
        $maintenanceMessage = Setting::where('key', 'maintenance_message')->first();
        $message = $maintenanceMessage ? $maintenanceMessage->value : 'ระบบกำลังอยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่ในภายหลัง';

        return response()->view('errors.maintenance', ['message' => $message], 503);
    }

    /**
     * ตรวจสอบโหมดบำรุงรักษาและส่งคืนการตอบสนองหน้าบำรุงรักษาถ้าจำเป็น
     *
     * @param string $path
     * @return \Illuminate\Http\Response|null
     */
    public static function checkMaintenance($path)
    {
        if (self::isInMaintenanceMode() && !self::isExemptFromMaintenance($path)) {
            return self::getMaintenanceResponse();
        }

        return null;
    }
}
