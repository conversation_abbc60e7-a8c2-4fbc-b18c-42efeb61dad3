<?php

namespace App\Observers;

use App\Models\Item;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ItemObserver
{
    /**
     * Handle the Item "deleted" event.
     */
    public function deleted(Item $item): void
    {
        Log::info('Item deleted, cleaning up associated files', ['item_id' => $item->id]);

        // ลบรูปภาพทั้งหมดที่เกี่ยวข้อง
        foreach ($item->images as $image) {
            $this->deleteImageFile($image);
        }

        // ลบไฟล์ทั้งหมดที่เกี่ยวข้อง
        foreach ($item->files as $file) {
            $this->deleteItemFile($file);
        }

        // ลบไฟล์เก่าที่อาจเก็บใน field ของ item โดยตรง (สำหรับความเข้ากันได้กับโค้ดเก่า)
        if (!empty($item->file_path)) {
            $this->deleteFileByPath($item->file_path);
        }

        if (!empty($item->image_path)) {
            $this->deleteFileByPath($item->image_path);
        }

        // ลองลบไฟล์ในโฟลเดอร์ต่างๆ ที่อาจมีการอัพโหลดไว้
        $this->tryDeleteFilesInFolders($item);

        Log::info('Finished cleaning up files for deleted item', ['item_id' => $item->id]);
    }

    /**
     * ลองลบไฟล์ในโฟลเดอร์ต่างๆ ที่อาจมีการอัพโหลดไว้
     */
    private function tryDeleteFilesInFolders(Item $item): void
    {
        // รายการโฟลเดอร์ที่อาจมีไฟล์อัพโหลด
        $folders = ['images', 'files', 'documents', 'audio', 'video'];

        // รูปแบบไฟล์ที่อาจมีการอัพโหลด (timestamp_filename)
        $patterns = [
            // รูปแบบไฟล์ที่มี timestamp นำหน้า
            '/^\d+_.*$/',
        ];

        foreach ($folders as $folder) {
            try {
                // ดึงรายการไฟล์ในโฟลเดอร์
                $files = Storage::disk('public')->files($folder);

                foreach ($files as $file) {
                    $filename = basename($file);

                    // ตรวจสอบว่าไฟล์ตรงกับรูปแบบหรือไม่
                    foreach ($patterns as $pattern) {
                        if (preg_match($pattern, $filename)) {
                            // ตรวจสอบว่าไฟล์นี้เกี่ยวข้องกับ item นี้หรือไม่
                            // โดยตรวจสอบจาก timestamp ในชื่อไฟล์
                            $parts = explode('_', $filename, 2);
                            if (count($parts) > 1) {
                                $timestamp = $parts[0];

                                // ถ้า timestamp ใกล้เคียงกับเวลาที่สร้าง item (ภายใน 1 ชั่วโมง)
                                $itemCreatedAt = strtotime($item->created_at);
                                $fileTimestamp = intval($timestamp);

                                if (abs($itemCreatedAt - $fileTimestamp) < 3600) {
                                    // ลบไฟล์
                                    Storage::disk('public')->delete($file);
                                    Log::info('Deleted related file by timestamp pattern', [
                                        'item_id' => $item->id,
                                        'file' => $file,
                                        'item_created_at' => $itemCreatedAt,
                                        'file_timestamp' => $fileTimestamp
                                    ]);
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error checking files in folder', [
                    'folder' => $folder,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * ลบไฟล์รูปภาพจาก storage
     */
    private function deleteImageFile($image): void
    {
        if (empty($image->image_path)) {
            return;
        }

        $fileName = $this->extractFileName($image->image_path);
        // สร้าง path สำหรับรูปภาพ
        $filePath = 'images/' . $fileName;

        try {
            // ลองลบไฟล์จาก storage/public
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted image file from storage/public', ['path' => $filePath]);
            } else {
                Log::warning('Image file not found in storage/public', ['path' => $filePath]);

                // ลองลบจาก public/storage
                $publicPath = public_path('storage/' . $filePath);
                if (file_exists($publicPath)) {
                    unlink($publicPath);
                    Log::info('Deleted image file from public/storage', ['path' => $publicPath]);
                } else {
                    Log::warning('Image file not found in public/storage', ['path' => $publicPath]);

                    // ลองลบจาก storage/app/public
                    $storagePath = storage_path('app/public/' . $filePath);
                    if (file_exists($storagePath)) {
                        unlink($storagePath);
                        Log::info('Deleted image file from storage/app/public', ['path' => $storagePath]);
                    } else {
                        Log::warning('Image file not found in storage/app/public', ['path' => $storagePath]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error deleting image file', [
                'path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * ลบไฟล์จาก storage
     */
    private function deleteItemFile($file): void
    {
        if (empty($file->file_path)) {
            return;
        }

        $fileName = $this->extractFileName($file->file_path);
        // สร้าง path ตามประเภทไฟล์
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (in_array($extension, ['pdf'])) {
            $filePath = 'files/' . $fileName;
        } elseif (in_array($extension, ['mp3', 'wav', 'ogg'])) {
            $filePath = 'files/' . $fileName;
        } elseif (in_array($extension, ['mp4', 'webm', 'avi', 'mov'])) {
            $filePath = 'files/' . $fileName;
        } else {
            $filePath = 'files/' . $fileName;
        }

        try {
            // ลองลบไฟล์จาก storage/public
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted file from storage/public', ['path' => $filePath]);
            } else {
                Log::warning('File not found in storage/public', ['path' => $filePath]);

                // ลองลบจาก public/storage
                $publicPath = public_path('storage/' . $filePath);
                if (file_exists($publicPath)) {
                    unlink($publicPath);
                    Log::info('Deleted file from public/storage', ['path' => $publicPath]);
                } else {
                    Log::warning('File not found in public/storage', ['path' => $publicPath]);

                    // ลองลบจาก storage/app/public
                    $storagePath = storage_path('app/public/' . $filePath);
                    if (file_exists($storagePath)) {
                        unlink($storagePath);
                        Log::info('Deleted file from storage/app/public', ['path' => $storagePath]);
                    } else {
                        Log::warning('File not found in storage/app/public', ['path' => $storagePath]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error deleting file', [
                'path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * ลบไฟล์จาก path
     */
    private function deleteFileByPath($path): void
    {
        if (empty($path)) {
            return;
        }

        $fileName = $this->extractFileName($path);
        // สร้าง path ตามประเภทไฟล์
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])) {
            $filePath = 'images/' . $fileName;
        } else {
            $filePath = 'files/' . $fileName;
        }

        try {
            // ลองลบไฟล์จาก storage/public
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted file from storage/public by path', ['path' => $filePath]);
            } else {
                Log::warning('File not found in storage/public by path', ['path' => $filePath]);

                // ลองลบจาก public/storage
                $publicPath = public_path('storage/' . $filePath);
                if (file_exists($publicPath)) {
                    unlink($publicPath);
                    Log::info('Deleted file from public/storage by path', ['path' => $publicPath]);
                } else {
                    Log::warning('File not found in public/storage by path', ['path' => $publicPath]);

                    // ลองลบจาก storage/app/public
                    $storagePath = storage_path('app/public/' . $filePath);
                    if (file_exists($storagePath)) {
                        unlink($storagePath);
                        Log::info('Deleted file from storage/app/public by path', ['path' => $storagePath]);
                    } else {
                        Log::warning('File not found in storage/app/public by path', ['path' => $storagePath]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error deleting file by path', [
                'path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * แยกเฉพาะชื่อไฟล์จาก path
     */
    private function extractFileName($path): string
    {
        // ลบ / ที่อยู่ข้างหน้า
        $path = ltrim($path, '/');

        // ลบ storage/ ที่อยู่ข้างหน้า (ถ้ามี)
        if (strpos($path, 'storage/') === 0) {
            $path = substr($path, 8);
        }

        // ลบ public/ ที่อยู่ข้างหน้า (ถ้ามี)
        if (strpos($path, 'public/') === 0) {
            $path = substr($path, 7);
        }

        // ลบโฟลเดอร์ทั้งหมดและเก็บเฉพาะชื่อไฟล์
        return basename($path);
    }
}
