<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Country;
use App\Models\Geography;

class Province extends Model
{
    protected $fillable = [
        'code',
        'name_th',
        'name_en',
        'country_code',
        'geography_id',
    ];

    /**
     * Get the country that owns the province.
     */
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_code', 'code');
    }

    /**
     * Get the geography that owns the province.
     */
    public function geography()
    {
        return $this->belongsTo(Geography::class, 'geography_id', 'id');
    }

    /**
     * Get the amphures (districts) for the province.
     */
    public function amphures()
    {
        return $this->hasMany(Amphure::class);
    }
}
