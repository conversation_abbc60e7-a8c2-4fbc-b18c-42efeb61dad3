<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $fillable = [
        'name',
        'description',
        'image_path',
        'code',
        'parent_id',
    ];

    /**
     * Get the image path with default fallback
     *
     * @return string
     */
    public function getImagePathAttribute($value)
    {
        // If value is empty, return default image path
        if (empty($value)) {
            // Return a simple path that will be processed by get_image_url()
            // which handles subfolder installations correctly
            return 'defaults/category-default.svg';
        }
        return $value;
    }

    public function items()
    {
        return $this->hasMany(Item::class);
    }

    // For backward compatibility
    public function documents()
    {
        return $this->items();
    }
}
