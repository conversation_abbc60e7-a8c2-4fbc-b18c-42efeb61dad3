<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Country;
use App\Models\Province;

class Geography extends Model
{
    protected $fillable = [
        'name',
        'country_code',
    ];

    /**
     * Get the country that owns the geography.
     */
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_code', 'code');
    }

    /**
     * Get the provinces for the geography.
     */
    public function provinces()
    {
        return $this->hasMany(Province::class);
    }
}
