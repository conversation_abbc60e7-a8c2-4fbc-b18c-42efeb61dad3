<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    protected $fillable = [
        'name',
        'code',
    ];

    /**
     * Get the documents for the country.
     */
    public function documents()
    {
        return $this->hasMany(Document::class, 'country', 'code');
    }

    /**
     * Get the items for the country.
     */
    public function items()
    {
        return $this->hasMany(Item::class, 'country', 'code');
    }


}
