<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Script extends Model
{
    protected $fillable = [
        'id',
        'name',
        'description',
    ];

    /**
     * Get the items for this script
     */
    public function items()
    {
        return $this->hasMany(Item::class);
    }

    /**
     * For backward compatibility - redirects to items()
     */
    public function documents()
    {
        return $this->items();
    }
}
