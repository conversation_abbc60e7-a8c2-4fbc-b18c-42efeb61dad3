<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Amphure extends Model
{
    protected $fillable = [
        'code',
        'name_th',
        'name_en',
        'province_id',
    ];

    /**
     * Get the province that owns the amphure.
     */
    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    /**
     * Get the districts for the amphure.
     */
    public function districts()
    {
        return $this->hasMany(District::class, 'amphure_id');
    }
}
