<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class District extends Model
{
    protected $table = 'districts';

    // Disable timestamps since they don't exist in the table
    public $timestamps = false;

    // Set primary key type to string
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'zip_code',
        'name_th',
        'name_en',
        'amphure_id'
    ];

    /**
     * Get the amphure that owns the district.
     */
    public function amphure()
    {
        return $this->belongsTo(Amphure::class, 'amphure_id', 'id');
    }
}
