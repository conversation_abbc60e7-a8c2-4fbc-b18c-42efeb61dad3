<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemType extends Model
{
    protected $fillable = [
        'id',
        'name',
        'description',
        'icon_class',
    ];

    public function items()
    {
        return $this->hasMany(Item::class);
    }
    
    /**
     * For backward compatibility with old code
     */
    public function documents()
    {
        return $this->hasMany(Item::class, 'item_type_id');
    }
}
