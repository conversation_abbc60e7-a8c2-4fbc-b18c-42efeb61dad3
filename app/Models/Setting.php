<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'display_name',
        'description'
    ];

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // Try to get from cache first
        if (Cache::has('setting_' . $key)) {
            return Cache::get('setting_' . $key);
        }

        // If not in cache, get from database
        $setting = self::where('key', $key)->first();

        if ($setting) {
            // Store in cache for future use
            Cache::put('setting_' . $key, $setting->value, now()->addDay());
            return $setting->value;
        }

        return $default;
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @param string|null $display_name
     * @param string|null $description
     * @return Setting
     */
    public static function set($key, $value, $type = 'text', $display_name = null, $description = null)
    {
        $setting = self::firstOrNew(['key' => $key]);
        $setting->value = $value;
        $setting->type = $type;

        if ($display_name) {
            $setting->display_name = $display_name;
        }

        if ($description) {
            $setting->description = $description;
        }

        $setting->save();

        // Update cache
        Cache::put('setting_' . $key, $value, now()->addDay());

        return $setting;
    }
}
