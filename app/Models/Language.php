<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Language extends Model
{
    protected $fillable = [
        'id',
        'name',
        'code',
        'description',
    ];

    /**
     * Get the items for this language
     */
    public function items()
    {
        return $this->hasMany(Item::class);
    }

    /**
     * For backward compatibility - redirects to items()
     */
    public function documents()
    {
        return $this->items();
    }
}
