<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Province;

class Item extends Model
{
    protected $fillable = [
        'title',
        'description',
        'year',
        'item_type_id', // Changed from document_type_id
        'file_path',
        'image_path',
        'category_id',
        'identifier_no',
        'other_title1',
        'other_title2',
        'other_title3',
        'material_id',
        'quantity',
        'language_id',
        'script_id',
        'creator',
        'author',
        'contributor',
        'location',
        'country',
        'province', // Added province field
        'latitude',
        'longitude',
        'brief',
        'remark',
        'manuscript_condition',
        'views',
        'file_type',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the images for the item
     */
    public function images()
    {
        return $this->hasMany(ItemImage::class)->orderBy('sort_order');
    }

    /**
     * Get the files for the item
     */
    public function files()
    {
        return $this->hasMany(ItemFile::class)->orderBy('sort_order');
    }

    /**
     * Get the first image of the item
     */
    public function getFirstImageAttribute()
    {
        // First try to get an image from the images relationship
        $image = $this->images()->orderBy('sort_order', 'asc')->first();
        if ($image) {
            // Return filename only (image_path contains just the filename)
            return $image->image_path;
        }

        // If no image is found, return the standard default image
        return 'defaults/item-default.svg';
    }

    /**
     * Get the main image of the item (with is_main = true)
     */
    public function mainImage()
    {
        return $this->hasOne(ItemImage::class)->where('is_main', true);
    }

    /**
     * Get the main file of the item (with is_main = true)
     */
    public function mainFile()
    {
        return $this->hasOne(ItemFile::class)->where('is_main', true);
    }

    /**
     * Get the main file path attribute
     */
    public function getMainFileAttribute()
    {
        $mainFile = $this->files()->where('is_main', true)->first();
        if ($mainFile) {
            // Return filename only (file_path contains just the filename)
            return $mainFile->file_path;
        }

        // ถ้าไม่มีไฟล์หลัก ใช้ file_path ของรายการ
        if (!empty($this->file_path)) {
            // Return filename only
            return basename($this->file_path);
        }

        // ถ้าไม่มีไฟล์เลย ให้ใช้ไฟล์ default
        return 'defaults/no-file-available.svg';
    }

    /**
     * Check if the item has any files
     *
     * @return bool
     */
    public function hasFiles()
    {
        return $this->files()->count() > 0 || !empty($this->file_path);
    }

    /**
     * Get the image path with default fallback - compatibility method
     * This method is kept for backward compatibility with code that might still
     * reference the old image_path field
     */
    public function getImagePathAttribute($value)
    {
        // First try to get the main image
        $mainImage = $this->images()->where('is_main', true)->first();
        if ($mainImage) {
            return $mainImage->image_path;
        }

        // If no main image, try the stored value
        if (!empty($value)) {
            return $value;
        }

        // Last resort, fall back to first_image
        return $this->first_image;
    }

    /**
     * Get the file type of the item
     *
     * @return string The file type (image, audio, video, pdf, document)
     */
    public function getFileType()
    {
        // ตรวจสอบไฟล์หลักจากตาราง item_files ก่อน
        $mainFile = $this->files()->where('is_main', true)->first();
        if ($mainFile) {
            $extension = strtolower(pathinfo($mainFile->file_path, PATHINFO_EXTENSION));

            if (in_array($extension, ['pdf'])) {
                return 'pdf';
            } elseif (in_array($extension, ['mp3', 'wav', 'ogg'])) {
                return 'audio';
            } elseif (in_array($extension, ['mp4', 'webm', 'avi', 'mov'])) {
                return 'video';
            } elseif (in_array($extension, ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'])) {
                return 'document';
            }
        }

        // ถ้าไม่มีไฟล์หลัก ตรวจสอบจาก file_type ของรายการ
        if ($this->file_type) {
            return $this->file_type;
        }

        // For backward compatibility with old document_type field
        if (isset($this->document_type)) {
            if ($this->document_type === 'audio') {
                return 'audio';
            } elseif (in_array($this->document_type, ['video', 'video-file'])) {
                return 'video';
            } elseif (in_array($this->document_type, ['pdf', 'document', 'manuscript'])) {
                return 'pdf';
            }
        }

        // Default to image
        return 'image';
    }

    /**
     * Get the item type that owns the item
     */
    public function itemType()
    {
        return $this->belongsTo(ItemType::class);
    }

    /**
     * For backward compatibility with old code
     */
    public function documentType()
    {
        return $this->belongsTo(ItemType::class, 'item_type_id');
    }

    /**
     * Get the material that owns the item
     */
    public function material()
    {
        return $this->belongsTo(Material::class);
    }

    /**
     * Get the language that owns the item
     */
    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    /**
     * Get the script that owns the item
     */
    public function script()
    {
        return $this->belongsTo(Script::class);
    }

    /**
     * Get the country that owns the item.
     */
    public function countryRelation()
    {
        return $this->belongsTo(Country::class, 'country', 'code');
    }

    /**
     * Get the province that owns the item.
     */
    public function provinceRelation()
    {
        return $this->belongsTo(Province::class, 'province', 'code');
    }

    /**
     * Get the province name from the province code.
     */
    public function getProvinceNameAttribute()
    {
        // ถ้ามีค่า province เป็น 'MM' ให้แสดงเป็นประเทศเมียนมาร์
        if ($this->province === 'MM') {
            return 'ประเทศเมียนมาร์';
        }

        // ถ้ามีค่า province ให้ดึงข้อมูลจากตาราง provinces โดยใช้ code
        if ($this->province) {
            $province = Province::where('code', $this->province)->first();
            if ($province) {
                return $province->name_th;
            }
            // ถ้าไม่พบข้อมูลใน provinces ให้แสดงค่า province เดิม
            return $this->province;
        }

        return null;
    }

    /**
     * Set the description attribute and strip HTML tags
     */
    public function setDescriptionAttribute($value)
    {
        $this->attributes['description'] = strip_tags($value);
    }

    /**
     * Set the brief attribute and strip HTML tags
     */
    public function setBriefAttribute($value)
    {
        $this->attributes['brief'] = strip_tags($value);
    }

    /**
     * Set the remark attribute and strip HTML tags
     */
    public function setRemarkAttribute($value)
    {
        $this->attributes['remark'] = strip_tags($value);
    }
}
