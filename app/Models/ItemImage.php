<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemImage extends Model
{
    protected $fillable = [
        'item_id',
        'image_path',
        'image_name',
        'image_type',
        'sort_order',
        'is_main'
    ];

    // ปิดการใช้งาน timestamps เพื่อให้สามารถบันทึกข้อมูลได้โดยไม่ต้องมี created_at และ updated_at
    public $timestamps = true;

    protected $casts = [
        'is_main' => 'boolean'
    ];

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    /**
     * For backward compatibility with old code
     */
    public function document()
    {
        return $this->belongsTo(Item::class, 'item_id');
    }
}
