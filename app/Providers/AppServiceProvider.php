<?php

namespace App\Providers;

use App\Models\Item;
use App\Observers\ItemObserver;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Request;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers
        Item::observe(ItemObserver::class);

        // Use Bootstrap 4 pagination
        Paginator::useBootstrap();

        // Force HTTPS in production
        if (config('app.env') === 'production') {
            URL::forceScheme('https');
        }

        // Get subfolder directly from env or config
        $basePath = env('APP_SUBFOLDER') ?: config('app.subfolder', '');

        // Set the application URL with the correct base path
        if (!empty($basePath)) {
            URL::forceRootUrl(config('app.url') . '/' . $basePath);

            // Configure Livewire for subfolder installation
            config(['livewire.path' => $basePath]);
        }
        
        // Fix asset URLs to work with subfolder installations
        URL::macro('asset', function ($path) use ($basePath) {
            $path = ltrim($path, '/');
            $secure = config('app.env') === 'production';

            // If we're in a subfolder, make sure the path is correct
            if (!empty($basePath)) {
                // If the path already includes the base path, don't add it again
                if (strpos($path, $basePath) !== 0) {
                    return url($path, [], $secure);
                }
            }

            return url($path, [], $secure);
        });

        URL::macro('secureAsset', function ($path) use ($basePath) {
            $path = ltrim($path, '/');

            // If we're in a subfolder, make sure the path is correct
            if (!empty($basePath)) {
                // If the path already includes the base path, don't add it again
                if (strpos($path, $basePath) !== 0) {
                    return url($path, [], true);
                }
            }

            return url($path, [], true);
        });
    }

    /**
     * Get the base path for subfolder installations
     *
     * @return string
     */
    private function getBasePath()
    {
        // Check if APP_SUBFOLDER is set in .env
        $configSubfolder = env('APP_SUBFOLDER');
        if (!empty($configSubfolder)) {
            return trim($configSubfolder, '/');
        }

        // Fallback to detecting from script name
        $scriptName = Request::server('SCRIPT_NAME', '');
        $basePath = '';

        // If the script name contains a path, extract it
        if (!empty($scriptName)) {
            $scriptPath = dirname($scriptName);
            if ($scriptPath !== '/' && $scriptPath !== '\\') {
                $basePath = trim($scriptPath, '/');
            }
        }

        return $basePath;
    }
}
