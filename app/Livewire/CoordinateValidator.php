<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Log;

class CoordinateValidator extends Component
{
    public $latitude = '';
    public $longitude = '';
    public $isValidLatitude = null;
    public $isValidLongitude = null;
    public $latitudeMessage = '';
    public $longitudeMessage = '';

    public function mount($latitude = null, $longitude = null)
    {
        $this->latitude = $latitude;
        $this->longitude = $longitude;

        // Validate initial values if provided
        if ($this->latitude !== null && $this->latitude !== '') {
            $this->validateLatitude();
        }

        if ($this->longitude !== null && $this->longitude !== '') {
            $this->validateLongitude();
        }
    }

    public function updatedLatitude()
    {
        $this->validateLatitude();
        // Reset longitude validation when latitude changes
        $this->isValidLongitude = null;
        $this->longitudeMessage = '';
    }

    public function updatedLongitude()
    {
        $this->validateLongitude();
        // Reset latitude validation when longitude changes
        $this->isValidLatitude = null;
        $this->latitudeMessage = '';
    }

    protected function validateLatitude()
    {
        $this->isValidLatitude = null;
        $this->latitudeMessage = '';

        if ($this->latitude === null || $this->latitude === '') {
            return;
        }

        // Remove any spaces
        $this->latitude = trim($this->latitude);

        // Check if it's a valid number
        if (!is_numeric($this->latitude)) {
            $this->isValidLatitude = false;
            $this->latitudeMessage = 'ละติจูดต้องเป็นตัวเลขเท่านั้น';
            return;
        }

        // Convert to float for validation
        $lat = (float) $this->latitude;

        // Check if it's within valid range (-90 to 90)
        if ($lat < -90 || $lat > 90) {
            $this->isValidLatitude = false;
            $this->latitudeMessage = 'ละติจูดต้องอยู่ระหว่าง -90 ถึง 90';
            return;
        }

        // Valid latitude
        $this->isValidLatitude = true;
        $this->latitudeMessage = 'ละติจูดถูกต้อง';
    }

    protected function validateLongitude()
    {
        $this->isValidLongitude = null;
        $this->longitudeMessage = '';

        if ($this->longitude === null || $this->longitude === '') {
            return;
        }

        // Remove any spaces
        $this->longitude = trim($this->longitude);

        // Check if it's a valid number
        if (!is_numeric($this->longitude)) {
            $this->isValidLongitude = false;
            $this->longitudeMessage = 'ลองจิจูดต้องเป็นตัวเลขเท่านั้น';
            return;
        }

        // Convert to float for validation
        $lng = (float) $this->longitude;

        // Check if it's within valid range (-180 to 180)
        if ($lng < -180 || $lng > 180) {
            $this->isValidLongitude = false;
            $this->longitudeMessage = 'ลองจิจูดต้องอยู่ระหว่าง -180 ถึง 180';
            return;
        }

        // Valid longitude
        $this->isValidLongitude = true;
        $this->longitudeMessage = 'ลองจิจูดถูกต้อง';
    }

    public function render()
    {
        return view('livewire.coordinate-validator');
    }
}
