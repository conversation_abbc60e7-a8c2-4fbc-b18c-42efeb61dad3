<?php

namespace App\Livewire\Admin\Users;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;

class UserManager extends Component
{
    use WithPagination, WithFileUploads;

    protected $paginationTheme = 'bootstrap';

    // Form properties
    public $userId;
    public $name = '';
    public $email = '';
    public $password = '';
    public $password_confirmation = '';
    public $role = 'user';
    public $is_active = true;
    public $bio = '';
    public $profile_image;
    public $temp_profile_image;

    // UI state
    public $isCreating = false;
    public $isEditing = false;
    public $confirmingUserDeletion = false;
    public $searchTerm = '';
    public $page = 1;

    // Validation rules
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . ($this->userId ?? ''),
            'password' => $this->userId ? 'nullable|min:8|confirmed' : 'required|min:8|confirmed',
            'role' => 'required|in:admin,editor,user',
            'is_active' => 'boolean',
            'bio' => 'nullable|string|max:1000',
            'temp_profile_image' => 'nullable|image|max:1024', // 1MB max
        ];
    }

    // Custom validation messages
    protected function messages()
    {
        return [
            'name.required' => 'กรุณาระบุชื่อผู้ใช้งาน',
            'email.required' => 'กรุณาระบุอีเมล',
            'email.email' => 'รูปแบบอีเมลไม่ถูกต้อง',
            'email.unique' => 'อีเมลนี้ถูกใช้งานแล้ว',
            'password.required' => 'กรุณาระบุรหัสผ่าน',
            'password.min' => 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร',
            'password.confirmed' => 'ยืนยันรหัสผ่านไม่ตรงกัน',
            'role.required' => 'กรุณาเลือกบทบาท',
        ];
    }

    // Reset form
    public function resetForm()
    {
        $this->reset(['userId', 'name', 'email', 'password', 'password_confirmation', 'role', 'is_active', 'bio', 'temp_profile_image']);
        $this->resetValidation();
    }

    // Show create form
    public function create()
    {
        $this->resetForm();
        $this->is_active = true;
        $this->role = 'user';
        $this->isCreating = true;
        $this->isEditing = false;
    }

    // Store new user
    public function store()
    {
        try {
            $validatedData = $this->validate();

            // Debug information
            \Log::info('Creating user with data:', [
                'name' => $this->name,
                'email' => $this->email,
                'role' => $this->role,
                'is_active' => $this->is_active,
                'has_profile_image' => !empty($this->temp_profile_image),
                'bio_length' => strlen($this->bio ?? ''),
            ]);

            $userData = [
                'name' => $this->name,
                'email' => $this->email,
                'password' => Hash::make($this->password),
                'role' => $this->role, // Keep for backward compatibility
                'is_active' => $this->is_active ? 1 : 0, // Ensure it's stored as 1 or 0
                'bio' => $this->bio,
            ];

            // Handle profile image upload
            if ($this->temp_profile_image) {
                $imagePath = $this->temp_profile_image->store('profile-images', 'public');
                $userData['profile_image'] = $imagePath;
                \Log::info('Stored profile image at: ' . $imagePath);
            }

            $user = User::create($userData);
            \Log::info('User created with ID: ' . $user->id);

            // Assign role using spatie/laravel-permission
            $user->assignRole($this->role);
            \Log::info('Assigned role: ' . $this->role);

            $this->resetForm();
            $this->isCreating = false;
            $this->dispatch('user-saved', ['action' => 'created']);
        } catch (\Exception $e) {
            \Log::error('Error creating user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            $this->dispatch('user-error', ['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    // Show edit form
    public function edit(User $user)
    {
        $this->resetForm();
        $this->userId = $user->id;
        $this->name = $user->name;
        $this->email = $user->email;
        // Get role from spatie roles
        $this->role = $user->roles->first() ? $user->roles->first()->name : 'user';
        $this->is_active = $user->is_active;
        $this->bio = $user->bio;
        $this->profile_image = $user->profile_image;
        $this->isEditing = true;
        $this->isCreating = false;
    }

    // Update user
    public function update()
    {
        try {
            $validatedData = $this->validate();

            $user = User::findOrFail($this->userId);

            // Debug information
            \Log::info('Updating user with ID: ' . $user->id, [
                'name' => $this->name,
                'email' => $this->email,
                'role' => $this->role,
                'is_active' => $this->is_active,
                'has_profile_image' => !empty($this->temp_profile_image),
                'bio_length' => strlen($this->bio ?? ''),
            ]);

            $userData = [
                'name' => $this->name,
                'email' => $this->email,
                'role' => $this->role, // Keep for backward compatibility
                'is_active' => $this->is_active ? 1 : 0, // Ensure it's stored as 1 or 0
                'bio' => $this->bio,
            ];

            // Only update password if provided
            if ($this->password) {
                $userData['password'] = Hash::make($this->password);
                \Log::info('Updating password for user: ' . $user->id);
            }

            // Handle profile image upload
            if ($this->temp_profile_image) {
                // Delete old image if exists
                if ($user->profile_image) {
                    Storage::disk('public')->delete($user->profile_image);
                    \Log::info('Deleted old profile image: ' . $user->profile_image);
                }

                $imagePath = $this->temp_profile_image->store('profile-images', 'public');
                $userData['profile_image'] = $imagePath;
                \Log::info('Stored new profile image at: ' . $imagePath);
            }

            $user->update($userData);
            \Log::info('User updated successfully');

            // Update roles
            // First remove all current roles
            $user->syncRoles([]);
            // Then assign the new role
            $user->assignRole($this->role);
            \Log::info('Updated role to: ' . $this->role);

            $this->resetForm();
            $this->isEditing = false;
            $this->dispatch('user-saved', ['action' => 'updated']);
        } catch (\Exception $e) {
            \Log::error('Error updating user: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            $this->dispatch('user-error', ['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    // Confirm user deletion
    public function confirmDelete($userId)
    {
        $this->confirmingUserDeletion = $userId;
    }

    // Delete user
    public function delete()
    {
        // Prevent deleting yourself
        if ($this->confirmingUserDeletion == auth()->id()) {
            $this->dispatch('user-cannot-delete-self');
            $this->confirmingUserDeletion = false;
            return;
        }

        $user = User::findOrFail($this->confirmingUserDeletion);

        // Delete profile image if exists
        if ($user->profile_image) {
            Storage::disk('public')->delete($user->profile_image);
        }

        $user->delete();

        $this->confirmingUserDeletion = false;
        $this->dispatch('user-deleted');
    }

    // Cancel form
    public function cancel()
    {
        $this->resetForm();
        $this->isCreating = false;
        $this->isEditing = false;
    }

    public function updated($property)
    {
        // Reset pagination when search term changes
        if ($property === 'searchTerm') {
            $this->resetPage();
        }
    }

    public function render()
    {
        // Get the current page from the URL if available, otherwise use the stored page
        $page = request()->query('page', $this->page);

        // Debug user status
        \Log::info('User statuses in database:', [
            'users' => DB::table('users')->select('id', 'name', 'is_active')->get()->toArray()
        ]);

        $users = User::with('roles')
            ->when($this->searchTerm, function($query) {
                $query->where('name', 'like', '%' . $this->searchTerm . '%')
                    ->orWhere('email', 'like', '%' . $this->searchTerm . '%');
            })
            ->orderBy('name')
            ->paginate(10, ['*'], 'page', $page);

        // Update the URL with the current page
        if ($page != $users->currentPage()) {
            $this->page = $users->currentPage();
        }

        // Debug user status after query
        foreach ($users as $user) {
            \Log::info('User status after query:', [
                'id' => $user->id,
                'name' => $user->name,
                'is_active' => $user->is_active,
                'is_active_type' => gettype($user->is_active),
                'is_active_bool' => (bool)$user->is_active
            ]);
        }

        // Get all available roles
        $roles = \Spatie\Permission\Models\Role::all();

        return view('livewire.admin.users.user-manager', [
            'users' => $users,
            'availableRoles' => $roles
        ]);
    }
}
