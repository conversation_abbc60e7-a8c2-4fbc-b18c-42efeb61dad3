<?php

namespace App\Livewire\Admin\Items;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Language;
use Illuminate\Support\Str;

class ItemManager extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Search and filter properties
    public $search = '';
    public $categoryFilter = '';
    public $typeFilter = '';
    public $languageFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;

    // Confirmation for deletion
    public $confirmingItemDeletion = false;
    public $itemIdToDelete = null;
    
    // Multiple selection
    public $selectedItems = [];
    public $selectAll = false;

    // Listeners for events
    protected $listeners = [
        'refreshItems' => '$refresh',
        'deleteConfirmed' => 'deleteItem',
    ];

    // Reset pagination when search or filters change
    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedCategoryFilter()
    {
        $this->resetPage();
    }

    public function updatedTypeFilter()
    {
        $this->resetPage();
    }

    public function updatedLanguageFilter()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }
    
    // Toggle select all items
    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedItems = $this->getFilteredItems()->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedItems = [];
        }
    }
    
    // Get filtered items query (for select all functionality)
    private function getFilteredItems()
    {
        $query = Item::query();
        
        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function($q) {
                $q->where('title', 'like', "%{$this->search}%")
                  ->orWhere('description', 'like', "%{$this->search}%")
                  ->orWhere('identifier_no', 'like', "%{$this->search}%");
            });
        }

        // Apply category filter
        if (!empty($this->categoryFilter)) {
            $query->where('category_id', $this->categoryFilter);
        }

        // Apply item type filter
        if (!empty($this->typeFilter)) {
            $query->where('item_type_id', $this->typeFilter);
        }

        // Apply language filter
        if (!empty($this->languageFilter)) {
            $query->where('language_id', $this->languageFilter);
        }
        
        return $query;
    }

    // Sort items by field
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    // Confirm item deletion
    public function confirmItemDeletion($id)
    {
        $this->confirmingItemDeletion = true;
        $this->itemIdToDelete = $id;

        $this->dispatch('show-delete-confirmation');
    }
    
    // Confirm multiple items deletion
    public function confirmMultipleItemsDeletion()
    {
        if (empty($this->selectedItems)) {
            return;
        }
        
        $this->dispatch('show-multiple-delete-confirmation');
    }

    // Delete item
    public function deleteItem()
    {
        $item = Item::findOrFail($this->itemIdToDelete);
        $item->delete();

        $this->confirmingItemDeletion = false;
        $this->itemIdToDelete = null;

        $this->dispatch('item-deleted', ['message' => 'รายการถูกลบแล้ว']);
    }
    
    // Delete multiple items
    public function deleteMultipleItems()
    {
        $count = count($this->selectedItems);
        
        if ($count > 0) {
            Item::whereIn('id', $this->selectedItems)->delete();
            $this->selectedItems = [];
            $this->selectAll = false;
            
            $this->dispatch('items-deleted', ['message' => "ลบรายการ $count รายการแล้ว"]);
        }
    }

    // Cancel deletion
    public function cancelItemDeletion()
    {
        $this->confirmingItemDeletion = false;
        $this->itemIdToDelete = null;
    }

    public function render()
    {
        // Get categories, item types, and languages for filters
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();

        // Build query with filters
        $query = Item::with(['category', 'itemType', 'language']);

        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function($q) {
                $q->where('title', 'like', "%{$this->search}%")
                  ->orWhere('description', 'like', "%{$this->search}%")
                  ->orWhere('identifier_no', 'like', "%{$this->search}%");
            });
        }

        // Apply category filter
        if (!empty($this->categoryFilter)) {
            $query->where('category_id', $this->categoryFilter);
        }

        // Apply item type filter
        if (!empty($this->typeFilter)) {
            $query->where('item_type_id', $this->typeFilter);
        }

        // Apply language filter
        if (!empty($this->languageFilter)) {
            $query->where('language_id', $this->languageFilter);
        }

        // Apply sorting
        $query->orderBy($this->sortField, $this->sortDirection);

        // Get paginated results
        $items = $query->paginate($this->perPage);
        
        // Update selectAll status based on current page items
        if ($items->count() > 0) {
            $this->selectAll = count($this->selectedItems) > 0 && 
                count(array_diff($items->pluck('id')->map(fn($id) => (string) $id)->toArray(), $this->selectedItems)) === 0;
        } else {
            $this->selectAll = false;
        }

        return view('livewire.admin.items.item-manager', [
            'items' => $items,
            'categories' => $categories,
            'itemTypes' => $itemTypes,
            'languages' => $languages,
        ]);
    }
}
