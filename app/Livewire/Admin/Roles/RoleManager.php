<?php

namespace App\Livewire\Admin\Roles;

use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleManager extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Form properties
    public $roleId;
    public $name = '';
    public $selectedPermissions = [];

    // UI state
    public $isCreating = false;
    public $isEditing = false;
    public $confirmingRoleDeletion = false;
    public $searchTerm = '';
    public $page = 1;

    // Validation rules
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:roles,name,' . ($this->roleId ?? ''),
            'selectedPermissions' => 'required|array|min:1',
        ];
    }

    // Custom validation messages
    protected function messages()
    {
        return [
            'name.required' => 'กรุณาระบุชื่อบทบาท',
            'name.unique' => 'ชื่อบทบาทนี้มีอยู่แล้ว',
            'selectedPermissions.required' => 'กรุณาเลือกสิทธิ์อย่างน้อย 1 รายการ',
            'selectedPermissions.min' => 'กรุณาเลือกสิทธิ์อย่างน้อย 1 รายการ',
        ];
    }

    // Reset form
    public function resetForm()
    {
        $this->reset(['roleId', 'name', 'selectedPermissions']);
        $this->resetValidation();
    }

    // Show create form
    public function create()
    {
        $this->resetForm();
        $this->isCreating = true;
        $this->isEditing = false;
    }

    // Store new role
    public function store()
    {
        try {
            $validatedData = $this->validate();

            // Create role
            $role = Role::create(['name' => $this->name]);

            // Assign permissions
            $role->syncPermissions($this->selectedPermissions);

            $this->resetForm();
            $this->isCreating = false;
            $this->dispatch('role-saved', ['action' => 'created']);
        } catch (\Exception $e) {
            $this->dispatch('role-error', ['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    // Show edit form
    public function edit(Role $role)
    {
        $this->resetForm();
        $this->roleId = $role->id;
        $this->name = $role->name;
        $this->selectedPermissions = $role->permissions->pluck('name')->toArray();
        $this->isEditing = true;
        $this->isCreating = false;
    }

    // Update role
    public function update()
    {
        try {
            $validatedData = $this->validate();

            $role = Role::findOrFail($this->roleId);

            // Update role name
            $role->update(['name' => $this->name]);

            // Sync permissions
            $role->syncPermissions($this->selectedPermissions);

            $this->resetForm();
            $this->isEditing = false;
            $this->dispatch('role-saved', ['action' => 'updated']);
        } catch (\Exception $e) {
            $this->dispatch('role-error', ['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    // Confirm role deletion
    public function confirmDelete($roleId)
    {
        $this->confirmingRoleDeletion = $roleId;
    }

    // Delete role
    public function delete()
    {
        try {
            $role = Role::findOrFail($this->confirmingRoleDeletion);

            // Check if role is in use
            if ($role->users->count() > 0) {
                $this->dispatch('role-error', ['message' => 'ไม่สามารถลบบทบาทนี้ได้เนื่องจากมีผู้ใช้งานที่ใช้บทบาทนี้อยู่']);
                $this->confirmingRoleDeletion = false;
                return;
            }

            // Delete role
            $role->delete();

            $this->confirmingRoleDeletion = false;
            $this->dispatch('role-deleted');
        } catch (\Exception $e) {
            $this->dispatch('role-error', ['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    // Cancel form
    public function cancel()
    {
        $this->resetForm();
        $this->isCreating = false;
        $this->isEditing = false;
    }

    public function updated($property)
    {
        // Reset pagination when search term changes
        if ($property === 'searchTerm') {
            $this->resetPage();
        }
    }

    public function render()
    {
        // Get the current page from the URL if available, otherwise use the stored page
        $page = request()->query('page', $this->page);

        $roles = Role::with(['permissions', 'users'])
            ->when($this->searchTerm, function($query) {
                $query->where('name', 'like', '%' . $this->searchTerm . '%');
            })
            ->orderBy('name')
            ->paginate(10, ['*'], 'page', $page);

        // Update the URL with the current page
        if ($page != $roles->currentPage()) {
            $this->page = $roles->currentPage();
        }

        $permissions = Permission::orderBy('name')->get();

        return view('livewire.admin.roles.role-manager', [
            'roles' => $roles,
            'allPermissions' => $permissions
        ])->layout('layouts.admin');
    }
}
