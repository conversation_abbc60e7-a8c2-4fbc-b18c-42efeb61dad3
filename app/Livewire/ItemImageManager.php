<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Item;
use App\Models\ItemImage;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ItemImageManager extends Component
{
    use WithFileUploads;

    public $item;
    public $images = [];
    public $newImages = [];
    public $tempImages = [];
    public $isUploading = false;
    public $uploadProgress = 0;
    public $successMessage = '';
    public $errorMessage = '';
    public $uploadedFileData = null;

    protected $listeners = [
        'refreshImages' => 'refreshImages',
        'imageUploaded' => 'handleImageUploaded',
        'handleUppyFileUploaded' => 'handleUppyFileUploaded',
        'uppyUploadSuccess' => 'handleUppyUploadSuccess',
        'uploadImage' => 'handleUploadImage',
        'handleUppyUpload' => 'handleUppyUpload',
        'forceRefresh' => 'forceRefresh'
    ];

    public function mount($item = null)
    {
        $this->item = $item;

        // Initialize tempImages as empty array if not already set
        if (!is_array($this->tempImages)) {
            $this->tempImages = [];
        }

        if ($this->item) {
            $this->refreshImages();

            // ตรวจสอบว่ามีรูปภาพที่เกี่ยวข้องกับรายการนี้หรือไม่
            Log::info('ItemImageManager mounted with item', [
                'item_id' => $this->item->id,
                'images_count' => $this->item->images()->count()
            ]);

            // ถ้ามีรูปภาพ ให้ดึงข้อมูลมาแสดง
            if ($this->item->images()->count() > 0) {
                Log::info('Item has images, loading from database');
                $this->images = $this->item->images()->orderBy('sort_order')->get();
            }
        } else {
            // Even if we don't have an item, we might have temporary images
            $this->refreshImages();
        }

        Log::info('ItemImageManager mounted', [
            'hasItem' => !is_null($this->item),
            'tempImagesCount' => count($this->tempImages),
            'imagesCount' => $this->images->count()
        ]);

        // Emit an event to notify the frontend that images have been updated
        // In Livewire v3, dispatch() is used for both component and browser events
        $this->dispatch('imagesUpdated', [
            'tempImages' => $this->tempImages
        ]);
    }

    public function forceRefresh()
    {
        Log::info('Force refreshing ItemImageManager component');
        $this->refreshImages();
        $this->dispatch('imagesUpdated', [
            'tempImages' => $this->tempImages
        ]);
    }

    public function refreshImages()
    {
        if ($this->item) {
            $this->item->refresh();
            $this->images = $this->item->images()->orderBy('sort_order')->get();
            Log::info('Refreshed images from database. Count: ' . $this->images->count());

            // ตรวจสอบว่ามีรูปภาพในฐานข้อมูลหรือไม่
            if ($this->images->count() === 0) {
                Log::info('No images found in database for item ' . $this->item->id);

                // ตรวจสอบว่ามีรูปภาพชั่วคราวหรือไม่
                if (count($this->tempImages) > 0) {
                    Log::info('Found temporary images, saving to database');

                    // บันทึกรูปภาพชั่วคราวลงฐานข้อมูล
                    foreach ($this->tempImages as $index => $imageData) {
                        try {
                            // ตรวจสอบว่ามีข้อมูลที่จำเป็นหรือไม่
                            if (empty($imageData['path'])) {
                                Log::warning('Missing required path for image');
                                continue;
                            }

                            // สร้างข้อมูลรูปภาพใหม่
                            $itemImage = new ItemImage();
                            $itemImage->item_id = $this->item->id;
                            $itemImage->image_path = $imageData['path'] ?? '';
                            $itemImage->image_name = $imageData['name'] ?? ($imageData['file_name'] ?? '');
                            $itemImage->image_type = $imageData['type'] ?? ($imageData['file_type'] ?? '');
                            $itemImage->sort_order = $index;
                            $itemImage->is_main = ($index === 0);

                            // บันทึกข้อมูล
                            $itemImage->save();

                            Log::info('Saved temporary image to database', [
                                'image_id' => $itemImage->id,
                                'item_id' => $this->item->id
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Error saving temporary image to database: ' . $e->getMessage());
                        }
                    }

                    // ดึงข้อมูลรูปภาพใหม่
                    $this->images = $this->item->images()->orderBy('sort_order')->get();
                    Log::info('Refreshed images after saving temp images. Count: ' . $this->images->count());

                    // ล้างข้อมูลรูปภาพชั่วคราว
                    $this->tempImages = [];
                }
            }
        } else {
            // Use temporary images if we don't have an item
            $this->images = collect($this->tempImages);
            Log::info('Using temporary images. Count: ' . $this->images->count(), ['tempImages' => $this->tempImages]);
        }

        // Log the current images for debugging
        Log::info('Final images collection. Count: ' . $this->images->count());

        // Emit an event to notify the frontend that images have been updated
        // In Livewire v3, dispatch() is used for both component and browser events
        $this->dispatch('imagesUpdated', [
            'tempImages' => $this->tempImages
        ]);
    }

    public function handleUppyUploadSuccess()
    {
        Log::info('Received Uppy upload success event in ItemImageManager');

        if (!$this->uploadedFileData) {
            Log::error('No uploadedFileData available in ItemImageManager');
            return;
        }

        $fileData = $this->uploadedFileData;
        Log::info('Processing fileData in ItemImageManager', ['fileData' => $fileData]);

        // Check if this is an image file
        $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
        $isImage = strpos($fileType, 'image/') === 0 || isset($fileData['is_image']) && $fileData['is_image'] === true;

        if (!$isImage) {
            Log::info('Skipping non-image file in ItemImageManager: ' . $fileType);
            return;
        }

        // Process the file data
        $this->handleUppyFileUploaded($fileData);

        // Refresh the images
        $this->refreshImages();

        // Reset the uploaded file data
        $this->uploadedFileData = null;
    }

    public function handleUppyUpload($fileData)
    {
        Log::info('Received handleUppyUpload in ItemImageManager', ['fileData' => $fileData]);

        // Set the uploaded file data
        $this->uploadedFileData = $fileData;

        // ตรวจสอบว่าเป็นรูปภาพหรือไม่อย่างละเอียด
        $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
        $fileExtension = $fileData['extension'] ?? '';
        $detectedType = $fileData['detected_type'] ?? '';
        $selectedType = $fileData['selected_type'] ?? '';
        $folder = $fileData['folder'] ?? '';

        // ตรวจสอบจากหลายปัจจัย
        $isImageByType = strpos($fileType, 'image/') === 0;
        $isImageByExt = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
        $isImageByFlag = isset($fileData['is_image']) && $fileData['is_image'] === true;
        $isImageByDetected = $detectedType === 'image';
        $isImageBySelected = $selectedType === 'image';
        $isImageByFolder = $folder === 'images';

        $isImage = $isImageByType || $isImageByExt || $isImageByFlag || $isImageByDetected || $isImageBySelected || $isImageByFolder;

        Log::info('File type detection in ItemImageManager:', [
            'fileName' => $fileData['name'] ?? 'unknown',
            'fileType' => $fileType,
            'fileExtension' => $fileExtension,
            'folder' => $folder,
            'isImageByType' => $isImageByType,
            'isImageByExt' => $isImageByExt,
            'isImageByFlag' => $isImageByFlag,
            'isImageByDetected' => $isImageByDetected,
            'isImageBySelected' => $isImageBySelected,
            'isImageByFolder' => $isImageByFolder,
            'finalDecision' => $isImage ? 'IS IMAGE' : 'NOT IMAGE'
        ]);

        if (!$isImage) {
            Log::info('Skipping non-image file in ItemImageManager: ' . $fileType);
            return [
                'success' => false,
                'message' => 'Not an image file'
            ];
        }

        // ตรวจสอบว่า path ของไฟล์อยู่ในโฟลเดอร์ images หรือไม่
        $filePath = $fileData['path'] ?? '';
        if (!empty($filePath) && !strpos($filePath, '/images/')) {
            // ปรับปรุง path ให้อยู่ในโฟลเดอร์ images
            $pathParts = explode('/', $filePath);
            $fileName = end($pathParts);
            $fileData['path'] = 'storage/images/' . $fileName;

            Log::info('Adjusted image path to images folder:', [
                'original_path' => $filePath,
                'new_path' => $fileData['path']
            ]);
        }

        try {
            // Extract file information
            $fileName = $fileData['name'] ?? '';
            $relativePath = $fileData['path'] ?? '';

            // Standardize the path format
            $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $relativePath);

            // If we have an item, save directly to the database
            if ($this->item) {
                // Create item image record
                $currentSortOrder = $this->item->images()->max('sort_order') + 1;
                $isFirstImage = $this->item->images()->count() === 0;

                // Create the image record
                $image = ItemImage::create([
                    'item_id' => $this->item->id,
                    'image_name' => $fileName,
                    'image_path' => $relativePath,
                    'image_type' => $fileData['type'] ?? 'image/jpeg',
                    'sort_order' => $currentSortOrder,
                    'is_main' => $isFirstImage, // Set as main if it's the first image
                ]);

                Log::info('Created image record in database', [
                    'image_id' => $image->id,
                    'item_id' => $this->item->id,
                    'is_main' => $isFirstImage
                ]);

                // Refresh the images
                $this->refreshImages();

                // Emit an event to notify the frontend that images have been updated
                $this->dispatch('imagesUpdated');

                return [
                    'success' => true,
                    'message' => 'Image saved to database successfully',
                    'id' => $image->id
                ];
            } else {
                // For new items without an ID yet, store in temporary array
                // Create a temporary image object
                $tempImage = [
                    'id' => 'temp_' . uniqid(),
                    'name' => $fileName,
                    'path' => $relativePath,
                    'type' => $fileData['type'] ?? 'image/jpeg',
                    'extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION),
                    'size' => $fileData['size'] ?? 0,
                    'is_image' => true,
                    // ข้อมูลที่จำเป็นสำหรับ ItemImage
                    'image_name' => $fileName,
                    'image_path' => $relativePath,
                    'image_type' => $fileData['type'] ?? 'image/jpeg', // ใช้ MIME type แทน extension
                    'sort_order' => count($this->tempImages) + 1,
                    'is_main' => count($this->tempImages) === 0, // Set as main if it's the first image
                    'created_at' => now()->toDateTimeString(),
                    'url' => asset('storage/' . $pathWithoutStorage)
                ];

                // Add to temporary images
                $this->tempImages[] = $tempImage;

                Log::info('Added temporary image in handleUppyUpload', [
                    'tempImage' => $tempImage,
                    'tempImagesCount' => count($this->tempImages)
                ]);

                // Store in session for backup
                try {
                    $sessionTempImages = session('temp_images', []);
                    $sessionTempImages[] = $tempImage;
                    session(['temp_images' => $sessionTempImages]);

                    Log::info('Stored temporary image in session', [
                        'session_temp_images_count' => count($sessionTempImages)
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error storing temporary image in session: ' . $e->getMessage());
                }

                // Refresh the images
                $this->refreshImages();

                // Emit an event to notify the frontend that images have been updated
                $this->dispatch('imagesUpdated', [
                    'tempImages' => $this->tempImages
                ]);

                return [
                    'success' => true,
                    'message' => 'Image uploaded successfully'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error in handleUppyUpload: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function setUploadedFileData($fileData)
    {
        Log::info('Setting uploadedFileData in ItemImageManager', ['fileData' => $fileData]);
        $this->uploadedFileData = $fileData;

        // Debug current tempImages before processing
        Log::info('Current tempImages before processing', ['count' => count($this->tempImages), 'tempImages' => $this->tempImages]);

        // Process the uploaded file
        $this->handleUppyUploadSuccess();

        // Debug tempImages after processing
        Log::info('Updated tempImages after processing', ['count' => count($this->tempImages), 'tempImages' => $this->tempImages]);

        // Force refresh the view
        $this->dispatch('imagesUpdated');
    }

    public function handleUploadImage($params = null)
    {
        Log::info('Received uploadImage event in ItemImageManager', ['params' => $params]);

        // Extract fileData from params if provided
        $fileData = null;
        if (is_array($params) && isset($params['fileData'])) {
            $fileData = $params['fileData'];
        }

        // If no fileData in params, check if it was passed directly
        if (!$fileData && $params && !is_array($params)) {
            $fileData = $params;
        }

        // If still no fileData, check if it was set in the component
        if (!$fileData && $this->uploadedFileData) {
            $fileData = $this->uploadedFileData;
        }

        if (!$fileData) {
            Log::error('No fileData received in handleUploadImage');
            return;
        }

        // Set the uploaded file data and process it
        $this->uploadedFileData = $fileData;
        $this->handleUppyUploadSuccess();

        // Force refresh the view
        $this->dispatch('imagesUpdated');
    }

    public function handleUppyFileUploaded($fileData)
    {
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            Log::info('Received file data in ItemImageManager component:', $fileData);

            // ตรวจสอบว่าเป็นรูปภาพหรือไม่อย่างละเอียด
            $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
            $fileExtension = $fileData['extension'] ?? '';
            $detectedType = $fileData['detected_type'] ?? '';
            $selectedType = $fileData['selected_type'] ?? '';
            $folder = $fileData['folder'] ?? '';

            // ตรวจสอบจากหลายปัจจัย
            $isImageByType = strpos($fileType, 'image/') === 0;
            $isImageByExt = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
            $isImageByFlag = isset($fileData['is_image']) && $fileData['is_image'] === true;
            $isImageByDetected = $detectedType === 'image';
            $isImageBySelected = $selectedType === 'image';
            $isImageByFolder = $folder === 'images';

            $isImage = $isImageByType || $isImageByExt || $isImageByFlag || $isImageByDetected || $isImageBySelected || $isImageByFolder;

            Log::info('File type detection in handleUppyFileUploaded:', [
                'fileName' => $fileData['name'] ?? 'unknown',
                'fileType' => $fileType,
                'fileExtension' => $fileExtension,
                'folder' => $folder,
                'isImageByType' => $isImageByType,
                'isImageByExt' => $isImageByExt,
                'isImageByFlag' => $isImageByFlag,
                'isImageByDetected' => $isImageByDetected,
                'isImageBySelected' => $isImageBySelected,
                'isImageByFolder' => $isImageByFolder,
                'finalDecision' => $isImage ? 'IS IMAGE' : 'NOT IMAGE'
            ]);

            if (!$isImage) {
                Log::info('Skipping non-image file in ItemImageManager: ' . $fileType);
                return [
                    'success' => false,
                    'message' => 'Not an image file'
                ];
            }

            // ตรวจสอบว่า path ของไฟล์อยู่ในโฟลเดอร์ images หรือไม่
            $filePath = $fileData['path'] ?? '';
            if (!empty($filePath) && !strpos($filePath, '/images/')) {
                // ปรับปรุง path ให้อยู่ในโฟลเดอร์ images
                $pathParts = explode('/', $filePath);
                $fileName = end($pathParts);
                $fileData['path'] = 'storage/images/' . $fileName;

                Log::info('Adjusted image path to images folder in handleUppyFileUploaded:', [
                    'original_path' => $filePath,
                    'new_path' => $fileData['path']
                ]);
            }

            if (!isset($fileData['path']) || !isset($fileData['name'])) {
                Log::error('Invalid file data received: missing path or name');
                throw new \Exception('Invalid file data received');
            }

            $relativePath = $fileData['path'];
            $fileName = $fileData['name'];

            Log::info('Processing image in Livewire: ' . $fileName . ' with path: ' . $relativePath);

            // Standardize the path format
            $standardPath = $relativePath;

            // Extract the actual path without /storage/ prefix
            $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $standardPath);

            // Remove 'public/' prefix if present to prevent double 'public' in the path
            if (strpos($pathWithoutStorage, 'public/') === 0) {
                $pathWithoutStorage = substr($pathWithoutStorage, 7); // Remove 'public/'
                Log::info('Removed public/ prefix from path: ' . $pathWithoutStorage);
            }

            // The path should already be correct since we're uploading directly to the final location
            // Just ensure it starts with 'images/' for consistency
            if (!str_starts_with($pathWithoutStorage, 'images/')) {
                Log::info('Path does not start with images/: ' . $pathWithoutStorage);
                // No need to modify the path as it should already be in the correct location
            }

            // Check if the file exists in the public storage directory
            $fullPath = public_path('storage/' . $pathWithoutStorage);

            Log::info('Standardized path: ' . $standardPath);
            Log::info('Path without storage prefix: ' . $pathWithoutStorage);
            Log::info('Checking if file exists at: ' . $fullPath);

            if (!file_exists($fullPath)) {
                Log::error('File does not exist at path: ' . $fullPath);

                // Try alternative paths for debugging
                $alt1 = storage_path('app/public/' . $pathWithoutStorage);
                $alt2 = storage_path('app/public/public/' . $pathWithoutStorage); // Check the incorrect path with double 'public'

                Log::info('Checking alternative path 1: ' . $alt1 . ' exists: ' . (file_exists($alt1) ? 'Yes' : 'No'));
                Log::info('Checking alternative path 2: ' . $alt2 . ' exists: ' . (file_exists($alt2) ? 'Yes' : 'No'));

                // Try to copy from the correct storage path
                if (file_exists($alt1)) {
                    try {
                        $storageDir = dirname($pathWithoutStorage);
                        if (!file_exists(public_path('storage/' . $storageDir))) {
                            mkdir(public_path('storage/' . $storageDir), 0755, true);
                        }

                        copy($alt1, $fullPath);
                        Log::info('Copied file from storage/app/public to public/storage path: ' . $fullPath);
                    } catch (\Exception $e) {
                        Log::error('Failed to copy file from storage/app/public: ' . $e->getMessage());
                    }
                }
                // If not found in the correct path, try the incorrect path
                else if (file_exists($alt2)) {
                    try {
                        // First, ensure the correct storage directory exists
                        $storageDir = dirname($pathWithoutStorage);
                        $correctStoragePath = storage_path('app/public/' . $storageDir);
                        if (!file_exists($correctStoragePath)) {
                            mkdir($correctStoragePath, 0755, true);
                        }

                        // Copy from incorrect to correct storage path
                        $correctPath = storage_path('app/public/' . $pathWithoutStorage);
                        copy($alt2, $correctPath);
                        Log::info('Copied file from incorrect path to correct storage path: ' . $correctPath);

                        // Then copy to public path
                        if (!file_exists(public_path('storage/' . $storageDir))) {
                            mkdir(public_path('storage/' . $storageDir), 0755, true);
                        }

                        copy($correctPath, $fullPath);
                        Log::info('Copied file to public path: ' . $fullPath);
                    } catch (\Exception $e) {
                        Log::error('Failed to copy file from incorrect path: ' . $e->getMessage());
                    }
                }
            } else {
                Log::info('File exists at path: ' . $fullPath . ' with size: ' . filesize($fullPath) . ' bytes');
            }

            // If the image was already saved to item_images by the controller, we don't need to create it again
            if (isset($fileData['id'])) {
                $image = ItemImage::find($fileData['id']);
                if ($image) {
                    Log::info('Image already exists in database with ID: ' . $image->id);
                    $this->successMessage = 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว';
                    $this->refreshImages();
                    $this->dispatch('imagesUpdated');
                    return [
                        'success' => true,
                        'message' => 'Image already exists'
                    ];
                }
            }

            // If we don't have an item yet (create page), just store in temporary array
            if (!$this->item) {
                // Store in temporary array for the UI
                $tempImage = [
                    'id' => 'temp_' . uniqid(),
                    'name' => $fileName,
                    'path' => $relativePath,
                    'type' => $fileData['type'] ?? 'image/jpeg',
                    'extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION),
                    'size' => $fileData['size'] ?? 0,
                    'is_image' => true,
                    'image_name' => $fileName,
                    'image_path' => $relativePath,
                    'image_type' => $fileData['type'] ?? 'image/jpeg',
                    'sort_order' => count($this->tempImages) + 1,
                    'is_main' => count($this->tempImages) === 0,
                    'created_at' => now()->toDateTimeString(),
                    'url' => asset('storage/' . $pathWithoutStorage)
                ];

                $this->tempImages[] = $tempImage;
                $this->successMessage = 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว';

                // Store in session for backup
                try {
                    $sessionTempImages = session('temp_images', []);
                    $sessionTempImages[] = $tempImage;
                    session(['temp_images' => $sessionTempImages]);

                    Log::info('Stored temporary image in session from handleUppyFileUploaded', [
                        'session_temp_images_count' => count($sessionTempImages)
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error storing temporary image in session from handleUppyFileUploaded: ' . $e->getMessage());
                }

                // Refresh images to update the view
                $this->refreshImages();

                // Emit an event to notify the frontend
                $this->dispatch('imagesUpdated', [
                    'tempImages' => $this->tempImages
                ]);

                return [
                    'success' => true,
                    'message' => 'Temporary image stored successfully'
                ];
            }

            // Create item image record if we have an item
            $currentSortOrder = $this->item->images()->max('sort_order') + 1;

            // Check if this is the first image
            $isFirstImage = $this->item->images()->count() === 0;

            // Create the image record
            $image = ItemImage::create([
                'item_id' => $this->item->id,
                'image_name' => $fileName,
                'image_path' => $relativePath,
                'image_type' => $fileData['type'] ?? 'image/jpeg', // ใช้ MIME type แทน extension
                'sort_order' => $currentSortOrder,
                'is_main' => $isFirstImage, // Set as main if it's the first image
            ]);

            // If this is the first image, log it
            if ($isFirstImage) {
                Log::info('Set first image as main: ' . $image->id);
            }

            $this->successMessage = 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

            return [
                'success' => true,
                'message' => 'Image uploaded successfully'
            ];

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ: ' . $e->getMessage();

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function handleImageUploaded()
    {
        $this->refreshImages();

        // Ensure we have a main image set if there are images
        if ($this->images->count() > 0 && !$this->images->where('is_main', true)->count()) {
            // No main image set, set the first one as main
            $firstImage = $this->images->first();
            $firstImage->is_main = true;
            $firstImage->save();

            // No need to update item.image_path anymore
            // We're using is_main field in item_images table instead

            Log::info('Auto-set main image during refresh: ' . $firstImage->id);

            // Refresh images again to get updated data
            $this->images = $this->item->images()->orderBy('sort_order')->get();
        }
    }

    public function deleteImage($imageId)
    {
        try {
            // ตรวจสอบว่าเป็นรูปภาพชั่วคราวหรือไม่
            if (strpos($imageId, 'temp_') === 0) {
                // เป็นรูปภาพชั่วคราว ให้ลบจาก tempImages
                Log::info('Deleting temporary image: ' . $imageId);

                // ค้นหารูปภาพในอาร์เรย์ tempImages
                $tempImageIndex = null;
                foreach ($this->tempImages as $index => $tempImage) {
                    if (($tempImage['id'] ?? '') === $imageId) {
                        $tempImageIndex = $index;
                        break;
                    }
                }

                if ($tempImageIndex !== null) {
                    // ดึงข้อมูลรูปภาพก่อนลบ
                    $tempImage = $this->tempImages[$tempImageIndex];
                    Log::info('Found temporary image to delete', ['tempImage' => $tempImage]);

                    // ลบไฟล์จาก storage ถ้ามี path
                    if (!empty($tempImage['path'])) {
                        $filePath = $tempImage['path'];
                        Log::info('Attempting to delete temporary image file: ' . $filePath);

                        // Standardize the path format
                        $filePath = ltrim($filePath, '/');
                        if (strpos($filePath, 'storage/') === 0) {
                            $filePath = substr($filePath, 8); // Remove 'storage/'
                        }

                        // Remove 'public/' prefix if present
                        if (strpos($filePath, 'public/') === 0) {
                            $filePath = substr($filePath, 7); // Remove 'public/'
                        }

                        Log::info('Standardized file path for deletion: ' . $filePath);

                        // ลองลบไฟล์จาก storage
                        $deleted = false;

                        // Try to delete from public storage
                        if (Storage::disk('public')->exists($filePath)) {
                            Storage::disk('public')->delete($filePath);
                            Log::info('Deleted temporary image from public disk: ' . $filePath);
                            $deleted = true;
                        }

                        // Also check with 'public/' prefix (for legacy paths)
                        if (Storage::disk('public')->exists('public/' . $filePath)) {
                            Storage::disk('public')->delete('public/' . $filePath);
                            Log::info('Deleted temporary image from public disk with public/ prefix: public/' . $filePath);
                            $deleted = true;
                        }

                        // Check in the public directory
                        $publicPath = public_path('storage/' . $filePath);
                        if (file_exists($publicPath)) {
                            unlink($publicPath);
                            Log::info('Deleted temporary image from public path: ' . $publicPath);
                            $deleted = true;
                        }

                        // Check in the storage directory
                        $storagePath = storage_path('app/public/' . $filePath);
                        if (file_exists($storagePath)) {
                            unlink($storagePath);
                            Log::info('Deleted temporary image from storage path: ' . $storagePath);
                            $deleted = true;
                        }

                        if (!$deleted) {
                            Log::warning('Could not find temporary image file to delete. Tried multiple paths for: ' . $tempImage['path']);
                        }
                    }

                    // ลบรูปภาพออกจากอาร์เรย์
                    array_splice($this->tempImages, $tempImageIndex, 1);
                    Log::info('Removed temporary image from array. Remaining: ' . count($this->tempImages));

                    // อัพเดท session
                    session(['temp_images' => $this->tempImages]);
                    Log::info('Updated session with new tempImages array');

                    // อัพเดทหน้าจอ
                    $this->refreshImages();
                    $this->successMessage = 'รูปภาพถูกลบเรียบร้อยแล้ว';

                    // แจ้งเตือน frontend
                    $this->dispatch('imagesUpdated', [
                        'tempImages' => $this->tempImages
                    ]);

                    return;
                } else {
                    Log::warning('Temporary image not found in tempImages array: ' . $imageId);
                    $this->errorMessage = 'ไม่พบรูปภาพที่ต้องการลบ';
                    return;
                }
            }

            // ถ้าไม่ใช่รูปภาพชั่วคราว ให้ลบจากฐานข้อมูล
            $image = ItemImage::findOrFail($imageId);

            // Check if this is the main image
            if ($image->is_main) {
                // Find another image to set as main
                $newMainImage = $this->item->images()
                    ->where('id', '!=', $image->id)
                    ->orderBy('sort_order')
                    ->first();

                if ($newMainImage) {
                    // Set new main image
                    $newMainImage->is_main = true;
                    $newMainImage->save();

                    // No need to update item.image_path anymore
                    // We're using is_main field in item_images table instead
                }
            }

            // Delete the file from storage
            // Image path may be in format '/storage/images/filename.jpg'
            $filePath = $image->image_path;
            Log::info('Attempting to delete image file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            Log::info('Standardized file path for deletion: ' . $filePath);

            // Check and delete from public storage
            $deleted = false;

            // Try to delete from public storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted image from public disk: ' . $filePath);
                $deleted = true;
            }

            // Also check with 'public/' prefix (for legacy paths)
            if (Storage::disk('public')->exists('public/' . $filePath)) {
                Storage::disk('public')->delete('public/' . $filePath);
                Log::info('Deleted image from public disk with public/ prefix: public/' . $filePath);
                $deleted = true;
            }

            // Check in the public directory
            $publicPath = public_path('storage/' . $filePath);
            if (file_exists($publicPath)) {
                unlink($publicPath);
                Log::info('Deleted image from public path: ' . $publicPath);
                $deleted = true;
            }

            // Check in the storage directory
            $storagePath = storage_path('app/public/' . $filePath);
            if (file_exists($storagePath)) {
                unlink($storagePath);
                Log::info('Deleted image from storage path: ' . $storagePath);
                $deleted = true;
            }

            // Check in the incorrect storage directory (with double public)
            $incorrectStoragePath = storage_path('app/public/public/' . $filePath);
            if (file_exists($incorrectStoragePath)) {
                unlink($incorrectStoragePath);
                Log::info('Deleted image from incorrect storage path: ' . $incorrectStoragePath);
                $deleted = true;
            }

            if (!$deleted) {
                Log::warning('Could not find image file to delete. Tried multiple paths for: ' . $image->image_path);
            }

            // Delete the record
            $image->delete();

            $this->successMessage = 'รูปภาพถูกลบเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการลบรูปภาพ: ' . $e->getMessage();
        }
    }

    public function setMainImage($imageId)
    {
        try {
            $image = ItemImage::findOrFail($imageId);

            // Reset all images to not be main
            $this->item->images()->update(['is_main' => false]);

            // Set this image as the main image
            $image->is_main = true;

            // No need to update item.image_path anymore
            // We're using is_main field in item_images table instead

            // Update sort order to make this image first
            $image->sort_order = 0;
            $image->save();

            // Re-order other images
            $this->item->images()
                ->where('id', '!=', $image->id)
                ->get()
                ->each(function ($img, $index) {
                    $img->sort_order = $index + 1;
                    $img->save();
                });

            $this->successMessage = 'รูปภาพหลักถูกตั้งค่าเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

            Log::info('Set main image: ' . $image->id . ' for item: ' . $this->item->id);

        } catch (\Exception $e) {
            Log::error('Error setting main image: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการตั้งค่ารูปภาพหลัก: ' . $e->getMessage();
        }
    }

    public function updateImageOrder($orderedIds)
    {
        try {
            foreach ($orderedIds as $index => $id) {
                ItemImage::where('id', $id)->update(['sort_order' => $index]);
            }

            $this->successMessage = 'ลำดับรูปภาพถูกอัปเดตเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการจัดเรียงรูปภาพ: ' . $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.item-image-manager');
    }
}
