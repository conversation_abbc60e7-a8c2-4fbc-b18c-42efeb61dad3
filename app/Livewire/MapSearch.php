<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Document;
use App\Models\Category;
use App\Models\DocumentType;
use App\Models\Province;
use App\Models\Language;
use App\Models\Script;

class MapSearch extends Component
{
    public $searchTerm = '';
    public $categoryIds = [];
    public $documentTypeIds = [];
    public $provinceIds = [];
    public $languageIds = [];
    public $scriptIds = [];
    public $documents = [];
    public $filteredDocuments = [];

    // Querystring parameters
    protected $queryString = [
        'searchTerm' => ['except' => ''],
        'categoryIds' => ['except' => []],
        'documentTypeIds' => ['except' => []],
        'provinceIds' => ['except' => []],
        'languageIds' => ['except' => []],
        'scriptIds' => ['except' => []],
    ];

    public function mount($documents)
    {
        $this->documents = $documents;
        $this->filteredDocuments = $documents;

        // Initialize from query parameters if available
        $this->searchTerm = request()->query('searchTerm', $this->searchTerm);

        // Handle array parameters
        $this->categoryIds = is_array(request()->query('categoryIds'))
            ? request()->query('categoryIds')
            : $this->categoryIds;

        $this->documentTypeIds = is_array(request()->query('documentTypeIds'))
            ? request()->query('documentTypeIds')
            : $this->documentTypeIds;

        $this->provinceIds = is_array(request()->query('provinceIds'))
            ? request()->query('provinceIds')
            : $this->provinceIds;

        $this->languageIds = is_array(request()->query('languageIds'))
            ? request()->query('languageIds')
            : $this->languageIds;

        $this->scriptIds = is_array(request()->query('scriptIds'))
            ? request()->query('scriptIds')
            : $this->scriptIds;

        // For backward compatibility with old URLs
        if ($categoryId = request()->query('category_id')) {
            $this->categoryIds[] = $categoryId;
        }

        if ($documentTypeId = request()->query('document_type_id')) {
            $this->documentTypeIds[] = $documentTypeId;
        }

        if ($provinceId = request()->query('province')) {
            $this->provinceIds[] = $provinceId;
        }

        if ($languageId = request()->query('language_id')) {
            $this->languageIds[] = $languageId;
        }

        if ($scriptId = request()->query('script_id')) {
            $this->scriptIds[] = $scriptId;
        }

        // กรองข้อมูลเริ่มต้นตามพารามิเตอร์ URL (ถ้ามี)
        if ($this->hasFilters()) {
            $this->filterDocuments();
        }
    }

    // ตรวจสอบว่ามีการใช้ตัวกรองหรือไม่
    private function hasFilters()
    {
        return !empty($this->searchTerm) ||
               !empty($this->categoryIds) ||
               !empty($this->documentTypeIds) ||
               !empty($this->provinceIds) ||
               !empty($this->languageIds) ||
               !empty($this->scriptIds);
    }

    public function updated($property)
    {
        if (in_array($property, ['searchTerm', 'categoryIds', 'documentTypeIds', 'provinceIds', 'languageIds', 'scriptIds'])) {
            $this->filterDocuments();
        }
    }

    public function filterDocuments()
    {
        try {
            // แปลงค่า array ให้เป็น array จริงๆ (ไม่ใช่ string)
            $this->provinceIds = is_array($this->provinceIds) ? $this->provinceIds : [];
            $this->languageIds = is_array($this->languageIds) ? $this->languageIds : [];
            $this->scriptIds = is_array($this->scriptIds) ? $this->scriptIds : [];
            $this->categoryIds = is_array($this->categoryIds) ? $this->categoryIds : [];
            $this->documentTypeIds = is_array($this->documentTypeIds) ? $this->documentTypeIds : [];

            // แปลงค่า string เป็น integer สำหรับ ID ที่เป็นตัวเลข
            $this->languageIds = array_map(function($id) { return is_numeric($id) ? (int)$id : $id; }, $this->languageIds);
            $this->scriptIds = array_map(function($id) { return is_numeric($id) ? (int)$id : $id; }, $this->scriptIds);
            $this->categoryIds = array_map(function($id) { return is_numeric($id) ? (int)$id : $id; }, $this->categoryIds);
            $this->documentTypeIds = array_map(function($id) { return is_numeric($id) ? (int)$id : $id; }, $this->documentTypeIds);

            $this->filteredDocuments = collect($this->documents)->filter(function ($document) {
                // กรองตามคำค้นหา
                $matchesSearch = empty($this->searchTerm) ||
                                 stripos($document['title'], $this->searchTerm) !== false ||
                                 stripos($document['description'] ?? '', $this->searchTerm) !== false ||
                                 stripos($document['location'] ?? '', $this->searchTerm) !== false;

                // กรองตามหมวดหมู่
                $matchesCategory = empty($this->categoryIds);
                if (!$matchesCategory && isset($document['category_id'])) {
                    $matchesCategory = in_array((int)$document['category_id'], $this->categoryIds);
                }

                // กรองตามประเภทเอกสาร
                $matchesDocumentType = empty($this->documentTypeIds);
                if (!$matchesDocumentType && isset($document['document_type_id'])) {
                    $matchesDocumentType = in_array((int)$document['document_type_id'], $this->documentTypeIds);
                }

                // กรองตามจังหวัด
                $matchesProvince = empty($this->provinceIds);
                if (!$matchesProvince && isset($document['province'])) {
                    $matchesProvince = in_array($document['province'], $this->provinceIds);
                }

                // กรองตามภาษา
                $matchesLanguage = empty($this->languageIds);
                if (!$matchesLanguage && isset($document['language_id'])) {
                    $matchesLanguage = in_array((int)$document['language_id'], $this->languageIds);
                }

                // กรองตามอักษร
                $matchesScript = empty($this->scriptIds);
                if (!$matchesScript && isset($document['script_id'])) {
                    $matchesScript = in_array((int)$document['script_id'], $this->scriptIds);
                }

                return $matchesSearch && $matchesCategory && $matchesDocumentType && $matchesProvince && $matchesLanguage && $matchesScript;
            })->values()->all();

            // ส่งข้อมูลที่กรองแล้วไปยัง JavaScript
            $this->dispatch('documentsFiltered', ['documents' => $this->filteredDocuments]);
        } catch (\Exception $e) {
            // ส่งข้อมูลว่างไปยัง JavaScript เพื่อแสดงว่าไม่พบข้อมูล
            $this->filteredDocuments = [];
            $this->dispatch('documentsFiltered', ['documents' => []]);
        }
    }

    // ฟังก์ชันสลับเลือกจังหวัดทั้งหมด
    public function toggleAllProvinces()
    {
        // โหลดข้อมูลจังหวัดก่อน
        $provinces = Province::orderBy('name_th')->get();

        if (empty($this->provinceIds)) {
            // ถ้าไม่มีจังหวัดที่เลือกอยู่ ให้เลือกทั้งหมด
            $this->provinceIds = $provinces->pluck('code')->toArray();
        } else {
            // ถ้ามีจังหวัดที่เลือกอยู่แล้ว ให้ยกเลิกการเลือกทั้งหมด
            $this->provinceIds = [];
        }
        $this->filterDocuments();
    }

    // ฟังก์ชันสลับเลือกภาษาทั้งหมด
    public function toggleAllLanguages()
    {
        // โหลดข้อมูลภาษาก่อน
        $languages = Language::orderBy('name')->get();

        if (empty($this->languageIds)) {
            // ถ้าไม่มีภาษาที่เลือกอยู่ ให้เลือกทั้งหมด
            $this->languageIds = $languages->pluck('id')->toArray();
        } else {
            // ถ้ามีภาษาที่เลือกอยู่แล้ว ให้ยกเลิกการเลือกทั้งหมด
            $this->languageIds = [];
        }
        $this->filterDocuments();
    }

    // ฟังก์ชันสลับเลือกอักษรทั้งหมด
    public function toggleAllScripts()
    {
        // โหลดข้อมูลอักษรก่อน
        $scripts = Script::orderBy('name')->get();

        if (empty($this->scriptIds)) {
            // ถ้าไม่มีอักษรที่เลือกอยู่ ให้เลือกทั้งหมด
            $this->scriptIds = $scripts->pluck('id')->toArray();
        } else {
            // ถ้ามีอักษรที่เลือกอยู่แล้ว ให้ยกเลิกการเลือกทั้งหมด
            $this->scriptIds = [];
        }
        $this->filterDocuments();
    }

    // ฟังก์ชันสลับเลือกหมวดหมู่ทั้งหมด
    public function toggleAllCategories()
    {
        // โหลดข้อมูลหมวดหมู่ก่อน
        $categories = Category::orderBy('name')->get();

        if (empty($this->categoryIds)) {
            // ถ้าไม่มีหมวดหมู่ที่เลือกอยู่ ให้เลือกทั้งหมด
            $this->categoryIds = $categories->pluck('id')->toArray();
        } else {
            // ถ้ามีหมวดหมู่ที่เลือกอยู่แล้ว ให้ยกเลิกการเลือกทั้งหมด
            $this->categoryIds = [];
        }
        $this->filterDocuments();
    }

    // ฟังก์ชันสลับเลือกประเภทเอกสารทั้งหมด
    public function toggleAllDocumentTypes()
    {
        // โหลดข้อมูลประเภทเอกสารก่อน
        $documentTypes = DocumentType::orderBy('name')->get();

        if (empty($this->documentTypeIds)) {
            // ถ้าไม่มีประเภทเอกสารที่เลือกอยู่ ให้เลือกทั้งหมด
            $this->documentTypeIds = $documentTypes->pluck('id')->toArray();
        } else {
            // ถ้ามีประเภทเอกสารที่เลือกอยู่แล้ว ให้ยกเลิกการเลือกทั้งหมด
            $this->documentTypeIds = [];
        }
        $this->filterDocuments();
    }

    public function resetFilters()
    {
        try {
            $this->reset(['searchTerm', 'categoryIds', 'documentTypeIds', 'provinceIds', 'languageIds', 'scriptIds']);
            $this->filteredDocuments = $this->documents;

            // ส่งข้อมูลทั้งหมดไปยัง JavaScript
            $this->dispatch('documentsFiltered', ['documents' => $this->documents]);

            // เรียกใช้ฟังก์ชัน resetMapView ใน JavaScript
            $this->dispatch('resetMap');
        } catch (\Exception $e) {
            // เรียกใช้ฟังก์ชัน resetMapView ใน JavaScript
            $this->dispatch('resetMap');
        }
    }

    // ตัวแปรสำหรับเก็บข้อมูล

    public function render()
    {
        $categories = Category::orderBy('name')->get();
        $documentTypes = DocumentType::orderBy('name')->get();
        $provinces = Province::orderBy('name_th')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();

        return view('livewire.map-search', [
            'categories' => $categories,
            'documentTypes' => $documentTypes,
            'provinces' => $provinces,
            'languages' => $languages,
            'scripts' => $scripts,
            'totalDocuments' => count($this->documents),
            'filteredCount' => count($this->filteredDocuments)
        ]);
    }
}
