<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Document;
use App\Models\Category;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;
use App\Models\DocumentType;

class DocumentsList extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Filter parameters
    public $search = '';
    public $categoryIds = [];
    public $documentTypeIds = [];
    public $materialIds = [];
    public $languageIds = [];
    public $scriptIds = [];
    public $yearFrom = '';
    public $yearTo = '';
    public $sortBy = 'newest';
    public $perPage = 12;
    public $viewMode = 'grid'; // 'grid' or 'table'

    // Querystring parameters
    protected $queryString = [
        'search' => ['except' => ''],
        'categoryIds' => ['except' => []],
        'documentTypeIds' => ['except' => []],
        'materialIds' => ['except' => []],
        'languageIds' => ['except' => []],
        'scriptIds' => ['except' => []],
        'yearFrom' => ['except' => ''],
        'yearTo' => ['except' => ''],
        'sortBy' => ['except' => 'newest'],
        'perPage' => ['except' => 12],
        'viewMode' => ['except' => 'grid'],
    ];

    public function mount()
    {
        // Initialize from query parameters if available
        $this->search = request()->query('search', $this->search);

        // Handle array parameters
        $this->categoryIds = is_array(request()->query('categoryIds'))
            ? request()->query('categoryIds')
            : $this->categoryIds;

        $this->documentTypeIds = is_array(request()->query('documentTypeIds'))
            ? request()->query('documentTypeIds')
            : $this->documentTypeIds;

        $this->materialIds = is_array(request()->query('materialIds'))
            ? request()->query('materialIds')
            : $this->materialIds;

        $this->languageIds = is_array(request()->query('languageIds'))
            ? request()->query('languageIds')
            : $this->languageIds;

        $this->scriptIds = is_array(request()->query('scriptIds'))
            ? request()->query('scriptIds')
            : $this->scriptIds;

        $this->yearFrom = request()->query('year_from', $this->yearFrom);
        $this->yearTo = request()->query('year_to', $this->yearTo);
        $this->sortBy = request()->query('sort', $this->sortBy);
        $this->perPage = request()->query('per_page', $this->perPage);
        $this->viewMode = request()->query('view', $this->viewMode);

        // For backward compatibility with old URLs
        if ($categoryId = request()->query('category_id')) {
            $this->categoryIds[] = $categoryId;
        }

        if ($documentTypeId = request()->query('document_type_id')) {
            $this->documentTypeIds[] = $documentTypeId;
        }

        if ($materialId = request()->query('material_id')) {
            $this->materialIds[] = $materialId;
        }

        if ($languageId = request()->query('language_id')) {
            $this->languageIds[] = $languageId;
        }

        if ($scriptId = request()->query('script_id')) {
            $this->scriptIds[] = $scriptId;
        }
    }

    public function updated($property)
    {
        if (in_array($property, [
            'search', 'categoryIds', 'documentTypeIds', 'materialIds',
            'languageIds', 'scriptIds', 'yearFrom', 'yearTo', 'sortBy', 'perPage'
        ])) {
            $this->resetPage();
        }
    }

    public function resetFilters()
    {
        $this->reset([
            'search', 'categoryIds', 'documentTypeIds', 'materialIds',
            'languageIds', 'scriptIds', 'yearFrom', 'yearTo'
        ]);
        $this->resetPage();
    }

    public function setViewMode($mode)
    {
        $this->viewMode = $mode;
    }

    public function setPerPage($perPage)
    {
        $this->perPage = $perPage;
        $this->resetPage();
    }

    public function setSortBy($sortBy)
    {
        $this->sortBy = $sortBy;
        $this->resetPage();
    }

    public function render()
    {
        // Build query
        $query = Document::with(['category', 'images', 'documentType', 'material', 'language', 'script']);

        // Apply search filter
        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', "%{$this->search}%")
                  ->orWhere('description', 'like', "%{$this->search}%")
                  ->orWhere('identifier_no', 'like', "%{$this->search}%")
                  ->orWhere('other_title1', 'like', "%{$this->search}%")
                  ->orWhere('other_title2', 'like', "%{$this->search}%")
                  ->orWhere('other_title3', 'like', "%{$this->search}%");
            });
        }

        // Apply category filter
        if (!empty($this->categoryIds)) {
            $query->whereIn('category_id', $this->categoryIds);
        }

        // Apply document type filter
        if (!empty($this->documentTypeIds)) {
            $query->whereIn('document_type_id', $this->documentTypeIds);
        }

        // Apply material filter
        if (!empty($this->materialIds)) {
            $query->whereIn('material_id', $this->materialIds);
        }

        // Apply language filter
        if (!empty($this->languageIds)) {
            $query->whereIn('language_id', $this->languageIds);
        }

        // Apply script filter
        if (!empty($this->scriptIds)) {
            $query->whereIn('script_id', $this->scriptIds);
        }

        // Apply year range filter
        if ($this->yearFrom) {
            $query->where('year', '>=', $this->yearFrom);
        }

        if ($this->yearTo) {
            $query->where('year', '<=', $this->yearTo);
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'a-z':
                $query->orderBy('title', 'asc');
                break;
            case 'z-a':
                $query->orderBy('title', 'desc');
                break;
            case 'year-asc':
                $query->orderBy('year', 'asc');
                break;
            case 'year-desc':
                $query->orderBy('year', 'desc');
                break;
            default: // newest
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Get documents with pagination
        $documents = $query->paginate($this->perPage);

        // Get filter options
        $categories = Category::orderBy('name')->get();
        $documentTypes = DocumentType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();

        return view('livewire.documents-list', [
            'documents' => $documents,
            'categories' => $categories,
            'documentTypes' => $documentTypes,
            'materials' => $materials,
            'languages' => $languages,
            'scripts' => $scripts,
            'totalResults' => $documents->total()
        ]);
    }
}
