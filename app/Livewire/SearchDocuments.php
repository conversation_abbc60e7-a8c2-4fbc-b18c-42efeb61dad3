<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Document;
use App\Models\Category;
use App\Models\DocumentType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;

class SearchDocuments extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Search parameters
    public $searchTerm = '';
    public $searchType = 'all';
    public $categoryIds = [];
    public $documentTypeIds = [];
    public $materialIds = [];
    public $languageIds = [];
    public $scriptIds = [];
    public $yearFrom = '';
    public $yearTo = '';
    public $sortBy = 'newest';

    // For tracking if search is being performed
    public $isSearching = false;

    // Querystring parameters
    protected $queryString = [
        'searchTerm' => ['except' => ''],
        'searchType' => ['except' => 'all'],
        'categoryIds' => ['except' => []],
        'documentTypeIds' => ['except' => []],
        'materialIds' => ['except' => []],
        'languageIds' => ['except' => []],
        'scriptIds' => ['except' => []],
        'yearFrom' => ['except' => ''],
        'yearTo' => ['except' => ''],
        'sortBy' => ['except' => 'newest'],
    ];

    public function mount($searchTerm = null)
    {
        // รับค่าคำค้นหาจากพารามิเตอร์ (ถ้ามี)
        if ($searchTerm) {
            $this->searchTerm = $searchTerm;
        }

        // Check if any search parameters are set
        $this->isSearching = $this->searchTerm ||
                            !empty($this->categoryIds) ||
                            !empty($this->documentTypeIds) ||
                            !empty($this->materialIds) ||
                            !empty($this->languageIds) ||
                            !empty($this->scriptIds) ||
                            $this->yearFrom ||
                            $this->yearTo;
    }

    public function updated($property)
    {
        if (in_array($property, [
            'searchTerm', 'searchType',
            'categoryIds', 'documentTypeIds', 'materialIds', 'languageIds', 'scriptIds',
            'yearFrom', 'yearTo', 'sortBy'
        ])) {
            $this->resetPage();
            $this->isSearching = $this->searchTerm ||
                                !empty($this->categoryIds) ||
                                !empty($this->documentTypeIds) ||
                                !empty($this->materialIds) ||
                                !empty($this->languageIds) ||
                                !empty($this->scriptIds) ||
                                $this->yearFrom ||
                                $this->yearTo;
        }
    }

    public function resetFilters()
    {
        $this->reset([
            'searchTerm', 'searchType',
            'categoryIds', 'documentTypeIds', 'materialIds', 'languageIds', 'scriptIds',
            'yearFrom', 'yearTo', 'sortBy'
        ]);
        $this->isSearching = false;
        $this->resetPage();
    }

    public function render()
    {
        // Get all filter options
        $categories = Category::all();
        $documentTypes = DocumentType::all();
        $materials = Material::all();
        $languages = Language::all();
        $scripts = Script::all();

        // Initialize query
        $query = Document::query();

        // Apply search term filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                switch ($this->searchType) {
                    case 'title':
                        $q->where('title', 'like', "%{$this->searchTerm}%");
                        break;
                    case 'identifier':
                        $q->where('identifier_no', 'like', "%{$this->searchTerm}%");
                        break;
                    case 'all':
                    default:
                        $q->where('title', 'like', "%{$this->searchTerm}%")
                          ->orWhere('description', 'like', "%{$this->searchTerm}%")
                          ->orWhere('identifier_no', 'like', "%{$this->searchTerm}%")
                          ->orWhere('other_title1', 'like', "%{$this->searchTerm}%")
                          ->orWhere('other_title2', 'like', "%{$this->searchTerm}%")
                          ->orWhere('other_title3', 'like', "%{$this->searchTerm}%");
                        break;
                }
            });
        }

        // Apply category filter (multi-select)
        if (!empty($this->categoryIds)) {
            $query->whereIn('category_id', $this->categoryIds);
        }

        // Apply document type filter (multi-select)
        if (!empty($this->documentTypeIds)) {
            $query->whereIn('document_type_id', $this->documentTypeIds);
        }

        // Apply material filter (multi-select)
        if (!empty($this->materialIds)) {
            $query->whereIn('material_id', $this->materialIds);
        }

        // Apply language filter (multi-select)
        if (!empty($this->languageIds)) {
            $query->whereIn('language_id', $this->languageIds);
        }

        // Apply script filter (multi-select)
        if (!empty($this->scriptIds)) {
            $query->whereIn('script_id', $this->scriptIds);
        }

        // Apply year range filter
        if ($this->yearFrom) {
            $query->where('year', '>=', $this->yearFrom);
        }

        if ($this->yearTo) {
            $query->where('year', '<=', $this->yearTo);
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'a-z':
                $query->orderBy('title', 'asc');
                break;
            case 'z-a':
                $query->orderBy('title', 'desc');
                break;
            case 'year-asc':
                $query->orderBy('year', 'asc');
                break;
            case 'year-desc':
                $query->orderBy('year', 'desc');
                break;
            default: // newest
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Get results with pagination
        $documents = $query->with(['category', 'documentType', 'material', 'language', 'script'])->paginate(12);
        $totalResults = $documents->total();

        return view('livewire.search-documents', [
            'documents' => $documents,
            'categories' => $categories,
            'documentTypes' => $documentTypes,
            'materials' => $materials,
            'languages' => $languages,
            'scripts' => $scripts,
            'totalResults' => $totalResults
        ]);
    }
}
