<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Models\Document;
use App\Models\DocumentFile;
use App\Models\DocumentImage;
use Illuminate\Support\Str;

class DocumentFileUploader extends Component
{
    use WithFileUploads;

    public $document;
    public $documentId;
    public $files = [];
    public $uploadedFiles = [];
    public $fileType = 'document'; // Default file type: document, image, audio, video
    public $isUploading = false;
    public $progress = 0;
    public $successMessage = '';
    public $errorMessage = '';
    public $displayType = 'all'; // 'all', 'images', 'documents'

    protected $listeners = [
        'refreshFiles' => '$refresh',
        'fileUploaded' => 'handleFileUploaded',
        'uppyFileUploaded' => 'handleUppyFileUploaded',
        'uppyFileDeleted' => 'handleUppyFileDeleted'
    ];

    public function mount($documentId = null, $displayType = 'all')
    {
        $this->documentId = $documentId;
        $this->displayType = $displayType;

        if ($documentId) {
            $this->document = Document::find($documentId);
            $this->refreshFiles();
        }
    }

    /**
     * Handle file uploaded from Uppy
     */
    public function handleUppyFileUploaded($data)
    {
        try {
            Log::info('Uppy file uploaded event received in Livewire', $data);

            // Clear any previous messages
            $this->successMessage = '';
            $this->errorMessage = '';

            // Add success message
            $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';

            // Get file type from data if available
            if (isset($data['fileType'])) {
                $this->fileType = $data['fileType'];
            }

            // Refresh files to show the newly uploaded file
            $this->refreshFiles();

            // Return a response to prevent errors
            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Error in handleUppyFileUploaded: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Handle file deleted from Uppy
     */
    public function handleUppyFileDeleted($data)
    {
        Log::info('Uppy file deleted event received in Livewire', $data);

        // Clear any previous messages
        $this->successMessage = '';
        $this->errorMessage = '';

        // Add success message
        $this->successMessage = 'ไฟล์ถูกลบเรียบร้อยแล้ว';

        // Refresh files to update the file list
        $this->refreshFiles();
    }

    public function refreshFiles()
    {
        if ($this->document) {
            // Load document files and images
            $this->document->load(['files', 'images']);

            // Filter files based on displayType
            if ($this->displayType === 'images') {
                // Only show images
                $this->document->files = $this->document->files->filter(function($file) {
                    $extension = strtolower($file->file_extension);
                    return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg']);
                });
            } elseif ($this->displayType === 'documents') {
                // Show documents, audio, video (non-images)
                $this->document->files = $this->document->files->filter(function($file) {
                    $extension = strtolower($file->file_extension);
                    return !in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg']);
                });
            }
        }
    }

    public function updatedFiles()
    {
        $this->validate([
            'files.*' => 'file|max:51200', // 50MB max
        ]);

        $this->isUploading = true;
        $this->progress = 0;
        $this->successMessage = '';
        $this->errorMessage = '';
    }

    public function upload()
    {
        $this->validate([
            'files.*' => 'file|max:51200', // 50MB max
        ]);

        $this->isUploading = true;
        $this->progress = 0;
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            foreach ($this->files as $file) {
                $originalName = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                $mimeType = $file->getMimeType();
                $fileCategory = $this->determineFileCategory($mimeType, $extension);

                // Generate a unique filename
                $fileName = time() . '_' . Str::random(10) . '_' . $originalName;

                // Store the file
                $path = $file->storeAs("public/{$fileCategory}", $fileName);

                // Create the public URL
                $url = "/storage/{$fileCategory}/{$fileName}";

                // Add to uploaded files
                $this->uploadedFiles[] = [
                    'name' => $originalName,
                    'path' => $path,
                    'url' => $url,
                    'mime_type' => $mimeType,
                    'extension' => $extension,
                    'category' => $fileCategory
                ];

                // If document exists, create the appropriate record
                if ($this->document) {
                    if ($fileCategory === 'images') {
                        // For images, create a DocumentImage record
                        $sortOrder = DocumentImage::where('document_id', $this->document->id)->max('sort_order') + 1 ?? 1;

                        $documentImage = DocumentImage::create([
                            'document_id' => $this->document->id,
                            'image_path' => $fileName,
                            'sort_order' => $sortOrder
                        ]);

                        // If no main image is set, set this as the main image
                        $hasMainImage = DocumentImage::where('document_id', $this->document->id)
                            ->where('is_main', true)
                            ->exists();

                        if (!$hasMainImage) {
                            $documentImage->is_main = true;
                            $documentImage->save();
                        }
                    } else {
                        // For other files, create a DocumentFile record
                        $documentFile = DocumentFile::create([
                            'document_id' => $this->document->id,
                            'file_path' => $url,
                            'file_type' => $extension,
                            'file_name' => $originalName,
                            'file_extension' => $extension,
                            'sort_order' => 1,
                            'is_main' => 1
                        ]);

                        // Update the document model for backward compatibility
                        $this->document->file_path = $url;
                        $this->document->file_type = $extension;
                        $this->document->save();
                    }
                }

                $this->progress = 100;
            }

            $this->successMessage = count($this->files) . ' ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';
            $this->files = []; // Clear the files array
            $this->refreshFiles();

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์: ' . $e->getMessage();
        }

        $this->isUploading = false;
    }

    public function deleteFile($fileId, $type = 'file')
    {
        try {
            if ($type === 'image') {
                $file = DocumentImage::find($fileId);
                if ($file) {
                    // Delete the file from storage
                    $filePath = 'public/images/' . $file->image_path;
                    if (Storage::exists($filePath)) {
                        Storage::delete($filePath);
                    }

                    // If this was the main image, update the main image flag
                    if ($file->is_main) {
                        // Find another image to set as main, or set to null
                        $newMainImage = DocumentImage::where('document_id', $this->document->id)
                            ->where('id', '!=', $file->id)
                            ->orderBy('sort_order')
                            ->first();

                        if ($newMainImage) {
                            $newMainImage->is_main = true;
                            $newMainImage->save();
                        }
                    }

                    // Delete the record
                    $file->delete();
                    $this->successMessage = 'ลบไฟล์รูปภาพเรียบร้อยแล้ว';
                }
            } else {
                $file = DocumentFile::find($fileId);
                if ($file) {
                    // Delete the file from storage
                    if (file_exists(public_path($file->file_path))) {
                        unlink(public_path($file->file_path));
                    }

                    // Delete the record
                    $file->delete();

                    // Update the document
                    if ($this->document && $this->document->file_path === $file->file_path) {
                        $this->document->file_path = null;
                        $this->document->file_type = null;
                        $this->document->save();
                    }

                    $this->successMessage = 'ลบไฟล์เรียบร้อยแล้ว';
                }
            }

            $this->refreshFiles();

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการลบไฟล์: ' . $e->getMessage();
        }
    }


    private function determineFileCategory($mimeType, $extension)
    {
        // Image files
        if (strpos($mimeType, 'image/') === 0) {
            return 'images';
        }

        // Document files (PDF)
        if ($mimeType === 'application/pdf' || $extension === 'pdf') {
            return 'documents';
        }

        // Audio files
        if (strpos($mimeType, 'audio/') === 0 || in_array($extension, ['mp3', 'wav', 'ogg'])) {
            return 'audio';
        }

        // Video files
        if (strpos($mimeType, 'video/') === 0 || in_array($extension, ['mp4', 'webm', 'mov', 'avi'])) {
            return 'video';
        }

        // Default to documents for other file types
        return 'documents';
    }

    public function render()
    {
        return view('livewire.document-file-uploader');
    }
}
