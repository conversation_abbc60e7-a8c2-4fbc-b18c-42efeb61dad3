<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Item;
use App\Models\ItemFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ItemFileManager extends Component
{
    use WithFileUploads;

    public $item;
    public $itemId;
    public $files = [];
    public $itemFiles = [];
    public $isUploading = false;
    public $uploadProgress = 0;
    public $successMessage = '';
    public $errorMessage = '';
    public $uploadedFileData = null;
    public $tempFiles = [];

    protected $listeners = [
        'refreshFiles' => 'refreshFiles',
        'fileUploaded' => 'handleFileUploaded',
        'uppyFileUploaded' => 'handleUppyFileUploaded',
        'uppyUploadSuccess' => 'handleUppyUploadSuccess',
        'uploadFile' => 'handleUploadFile',
        'handleUppyUpload' => 'handleUppyUpload',
        'forceRefresh' => 'forceRefresh'
    ];

    public function mount($item = null)
    {
        $this->item = $item;
        $this->itemId = $item ? $item->id : null;

        // Initialize tempFiles as empty array if not already set
        if (!is_array($this->tempFiles)) {
            $this->tempFiles = [];
        }

        if ($this->item) {
            $this->refreshFiles();

            // ตรวจสอบว่ามีไฟล์ที่เกี่ยวข้องกับรายการนี้หรือไม่
            Log::info('ItemFileManager mounted with item', [
                'item_id' => $this->item->id,
                'files_count' => $this->item->files()->count()
            ]);

            // ถ้ามีไฟล์ ให้ดึงข้อมูลมาแสดง
            if ($this->item->files()->count() > 0) {
                Log::info('Item has files, loading from database');
                $this->itemFiles = $this->item->files()->orderBy('sort_order')->get();
            }
        } else {
            // Even if we don't have an item, we might have temporary files
            $this->refreshFiles();
        }

        Log::info('ItemFileManager mounted', [
            'hasItem' => !is_null($this->item),
            'tempFilesCount' => count($this->tempFiles),
            'itemFilesCount' => $this->itemFiles->count()
        ]);

        // Emit an event to notify the frontend that files have been updated
        // In Livewire v3, dispatch() is used for both component and browser events
        $this->dispatch('filesUpdated', [
            'tempFiles' => $this->tempFiles
        ]);

        // ลงทะเบียน listeners สำหรับ events
        $this->listeners = [
            'handleFileUpload' => 'handleFileUpload',
            'uppyFileUploaded' => 'handleUppyUpload',
            'refreshFiles' => 'refreshFiles'
        ];
    }

    public function handleFileUpload($data)
    {
        Log::info('Received handleFileUpload event in ItemFileManager', ['data' => $data]);

        if (isset($data['fileData']) && is_array($data['fileData'])) {
            return $this->handleUppyUpload($data['fileData']);
        }

        return [
            'success' => false,
            'message' => 'Invalid file data format'
        ];
    }

    public function forceRefresh()
    {
        Log::info('Force refreshing ItemFileManager component');
        $this->refreshFiles();
        $this->dispatch('filesUpdated', [
            'tempFiles' => $this->tempFiles
        ]);
    }

    public function refreshFiles()
    {
        if ($this->item) {
            $this->item->refresh();
            $this->itemFiles = $this->item->files()->orderBy('sort_order')->get();

            // ตรวจสอบว่ามีไฟล์ในฐานข้อมูลหรือไม่
            if ($this->itemFiles->count() === 0) {
                Log::info('No files found in database for item ' . $this->item->id);

                // ตรวจสอบว่ามีไฟล์ชั่วคราวหรือไม่
                if (count($this->tempFiles) > 0) {
                    Log::info('Found temporary files, saving to database');

                    // บันทึกไฟล์ชั่วคราวลงฐานข้อมูล
                    foreach ($this->tempFiles as $index => $fileData) {
                        try {
                            // ตรวจสอบว่ามีข้อมูลที่จำเป็นหรือไม่
                            if (empty($fileData['path'])) {
                                Log::warning('Missing required path for file');
                                continue;
                            }

                            // สร้างข้อมูลไฟล์ใหม่
                            $itemFile = new ItemFile();
                            $itemFile->item_id = $this->item->id;
                            $itemFile->file_path = $fileData['path'] ?? '';
                            $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                            $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                            $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                            $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                            $itemFile->sort_order = $index;
                            $itemFile->is_main = ($index === 0);

                            // บันทึกข้อมูล
                            $itemFile->save();

                            Log::info('Saved temporary file to database', [
                                'file_id' => $itemFile->id,
                                'item_id' => $this->item->id
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Error saving temporary file to database: ' . $e->getMessage());
                        }
                    }

                    // ดึงข้อมูลไฟล์ใหม่
                    $this->itemFiles = $this->item->files()->orderBy('sort_order')->get();
                    Log::info('Refreshed files after saving temp files. Count: ' . $this->itemFiles->count());

                    // ล้างข้อมูลไฟล์ชั่วคราว
                    $this->tempFiles = [];
                }
            }
        } else {
            // Use temporary files if we don't have an item
            $this->itemFiles = collect($this->tempFiles);
        }

        // Log the current files for debugging
        Log::info('Refreshed files. Count: ' . $this->itemFiles->count());

        // Emit an event to notify the frontend that files have been updated
        // In Livewire v3, dispatch() is used for both component and browser events
        $this->dispatch('filesUpdated', [
            'tempFiles' => $this->tempFiles
        ]);
    }

    public function handleUppyUploadSuccess()
    {
        Log::info('Received Uppy upload success event in ItemFileManager');

        if (!$this->uploadedFileData) {
            Log::error('No uploadedFileData available in ItemFileManager');
            return;
        }

        $fileData = $this->uploadedFileData;
        Log::info('Processing fileData in ItemFileManager', ['fileData' => $fileData]);

        // Check if this is a document file (not an image)
        $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
        $isImage = strpos($fileType, 'image/') === 0 || (isset($fileData['is_image']) && $fileData['is_image'] === true);

        if ($isImage) {
            Log::info('Skipping image file in ItemFileManager: ' . $fileType);
            return;
        }

        // Process the file data
        $this->handleUppyFileUploaded($fileData);

        // Refresh the files
        $this->refreshFiles();

        // Reset the uploaded file data
        $this->uploadedFileData = null;
    }

    public function handleUppyUpload($fileData)
    {
        Log::info('Received handleUppyUpload in ItemFileManager', ['fileData' => $fileData]);

        // ตรวจสอบว่า $fileData เป็น array หรือไม่
        if (!is_array($fileData)) {
            Log::error('Invalid fileData format in handleUppyUpload: not an array', ['fileData' => $fileData]);
            return [
                'success' => false,
                'message' => 'Invalid file data format'
            ];
        }

        // Set the uploaded file data
        $this->uploadedFileData = $fileData;

        // ตรวจสอบว่าเป็นรูปภาพหรือไม่อย่างละเอียด
        $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
        $fileExtension = $fileData['extension'] ?? '';
        $detectedType = $fileData['detected_type'] ?? '';
        $selectedType = $fileData['selected_type'] ?? '';
        $folder = $fileData['folder'] ?? '';

        // ตรวจสอบจากหลายปัจจัย
        $isImageByType = strpos($fileType, 'image/') === 0;
        $isImageByExt = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
        $isImageByFlag = isset($fileData['is_image']) && $fileData['is_image'] === true;
        $isImageByDetected = $detectedType === 'image';
        $isImageBySelected = $selectedType === 'image';
        $isImageByFolder = $folder === 'images';

        // ให้ความสำคัญกับ is_image flag มากที่สุด เนื่องจากมีการตรวจสอบในฝั่ง JavaScript แล้ว
        // แต่ถ้าไม่มี is_image flag ให้ตรวจสอบจากปัจจัยอื่น
        $isImage = $isImageByFlag || (!isset($fileData['is_image']) && ($isImageByType || $isImageByExt));

        Log::info('File type detection in ItemFileManager:', [
            'fileName' => $fileData['name'] ?? 'unknown',
            'fileType' => $fileType,
            'fileExtension' => $fileExtension,
            'folder' => $folder,
            'isImageByType' => $isImageByType,
            'isImageByExt' => $isImageByExt,
            'isImageByFlag' => $isImageByFlag,
            'isImageByDetected' => $isImageByDetected,
            'isImageBySelected' => $isImageBySelected,
            'isImageByFolder' => $isImageByFolder,
            'finalDecision' => $isImage ? 'IS IMAGE' : 'NOT IMAGE'
        ]);

        // ถ้าเป็นรูปภาพ ให้ข้ามไป (เพราะนี่คือ ItemFileManager)
        if ($isImage) {
            Log::info('Skipping image file in ItemFileManager: ' . $fileType);
            return [
                'success' => false,
                'message' => 'Not a document file'
            ];
        }

        // ไม่ต้องปรับ path แล้ว เพราะไฟล์จะถูกอัพโหลดไปยังโฟลเดอร์ที่ถูกต้องตามประเภทแล้ว
        $filePath = $fileData['path'] ?? '';
        Log::info('File path from upload:', [
            'original_path' => $filePath
        ]);

        try {
            // Get file path and name
            $relativePath = $fileData['path'] ?? '';
            $fileName = $fileData['name'] ?? '';

            // Standardize the path format
            $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $relativePath);

            // The path should already be correct since we're uploading directly to the final location
            // Just ensure it starts with the appropriate folder prefix for consistency
            if (!str_starts_with($pathWithoutStorage, 'files/') &&
                !str_starts_with($pathWithoutStorage, 'documents/') &&
                !str_starts_with($pathWithoutStorage, 'audio/') &&
                !str_starts_with($pathWithoutStorage, 'video/')) {
                Log::info('Path does not start with expected folder prefix: ' . $pathWithoutStorage);
                // No need to modify the path as it should already be in the correct location
            }

            // Get the actual file size if available
            $fileSize = $fileData['size'] ?? 0;

            // If file size is not provided or is 0, try to get it from the file system
            if ($fileSize == 0) {
                // Try to get file size from public storage
                $publicPath = public_path('storage/' . $pathWithoutStorage);
                if (file_exists($publicPath)) {
                    $fileSize = filesize($publicPath);
                    Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                }
                // Try storage path if public path doesn't exist
                else {
                    $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                    if (file_exists($storagePath)) {
                        $fileSize = filesize($storagePath);
                        Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                    }
                }
            }

            // If we have an item, save directly to the database
            if ($this->item) {
                // Create item file record
                $currentSortOrder = $this->item->files()->max('sort_order') + 1;
                $isFirstFile = $this->item->files()->count() === 0;

                // Create the file record - ใช้ชื่อไฟล์ต้นฉบับ
                $file = ItemFile::create([
                    'item_id' => $this->item->id,
                    'file_name' => $fileData['original_name'] ?? $fileName, // ใช้ชื่อไฟล์ต้นฉบับถ้ามี
                    'file_path' => $relativePath,
                    'file_type' => $fileType,
                    'file_extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION),
                    'file_size' => $fileSize,
                    'sort_order' => $currentSortOrder,
                    'is_main' => $isFirstFile, // Set as main if it's the first file
                ]);

                Log::info('Created file record in database', [
                    'file_id' => $file->id,
                    'item_id' => $this->item->id,
                    'is_main' => $isFirstFile
                ]);

                // Refresh the files
                $this->refreshFiles();

                // Emit an event to notify the frontend that files have been updated
                $this->dispatch('filesUpdated');

                return [
                    'success' => true,
                    'message' => 'File saved to database successfully',
                    'id' => $file->id
                ];
            } else {
                // Create a temporary file object for new items without an ID yet
                $tempFile = [
                    'id' => 'temp_' . uniqid(),
                    'name' => $fileData['original_name'] ?? $fileName, // ใช้ชื่อไฟล์ต้นฉบับถ้ามี
                    'path' => $relativePath,
                    'type' => $fileType,
                    'extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION),
                    'size' => $fileSize,
                    'is_image' => false,
                    // ข้อมูลที่จำเป็นสำหรับ ItemFile
                    'file_name' => $fileData['original_name'] ?? $fileName, // ใช้ชื่อไฟล์ต้นฉบับถ้ามี
                    'file_path' => $relativePath,
                    'file_type' => $fileType, // ใช้ MIME type
                    'file_extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION), // เพิ่ม field นี้เพื่อให้ตรงกับที่ ItemFile ต้องการ
                    'mime_type' => $fileType,
                    'file_size' => $fileSize,
                    'sort_order' => count($this->tempFiles) + 1,
                    'is_main' => count($this->tempFiles) === 0, // Set as main if it's the first file
                    'created_at' => now()->toDateTimeString(),
                    'url' => asset('storage/' . $pathWithoutStorage)
                ];

                // Add to temporary files
                $this->tempFiles[] = $tempFile;

                Log::info('Added temporary file in handleUppyUpload', [
                    'tempFile' => $tempFile,
                    'tempFilesCount' => count($this->tempFiles)
                ]);

                // Store in session for backup
                try {
                    $sessionTempFiles = session('temp_files', []);
                    $sessionTempFiles[] = $tempFile;
                    session(['temp_files' => $sessionTempFiles]);

                    Log::info('Stored temporary file in session', [
                        'session_temp_files_count' => count($sessionTempFiles)
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error storing temporary file in session: ' . $e->getMessage());
                }

                // Refresh the files
                $this->refreshFiles();

                // Emit an event to notify the frontend that files have been updated
                $this->dispatch('filesUpdated', [
                    'tempFiles' => $this->tempFiles
                ]);

                return [
                    'success' => true,
                    'message' => 'File uploaded successfully'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error in handleUppyUpload: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function setUploadedFileData($fileData)
    {
        Log::info('Setting uploadedFileData in ItemFileManager', ['fileData' => $fileData]);
        $this->uploadedFileData = $fileData;

        // Debug current tempFiles before processing
        Log::info('Current tempFiles before processing', ['count' => count($this->tempFiles), 'tempFiles' => $this->tempFiles]);

        // Process the uploaded file
        $this->handleUppyUploadSuccess();

        // Debug tempFiles after processing
        Log::info('Updated tempFiles after processing', ['count' => count($this->tempFiles), 'tempFiles' => $this->tempFiles]);

        // Force refresh the view
        $this->dispatch('filesUpdated');
    }

    public function handleUploadFile($params = null)
    {
        Log::info('Received uploadFile event in ItemFileManager', ['params' => $params]);

        // Extract fileData from params if provided
        $fileData = null;
        if (is_array($params) && isset($params['fileData'])) {
            $fileData = $params['fileData'];
        }

        // If no fileData in params, check if it was passed directly
        if (!$fileData && $params && !is_array($params)) {
            $fileData = $params;
        }

        // If still no fileData, check if it was set in the component
        if (!$fileData && $this->uploadedFileData) {
            $fileData = $this->uploadedFileData;
        }

        if (!$fileData) {
            Log::error('No fileData received in handleUploadFile');
            return;
        }

        // Set the uploaded file data and process it
        $this->uploadedFileData = $fileData;
        $this->handleUppyUploadSuccess();

        // Force refresh the view
        $this->dispatch('filesUpdated');
    }

    public function handleUppyFileUploaded($fileData)
    {
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            Log::info('Uppy file uploaded event received in ItemFileManager', ['fileData' => $fileData]);

            // ตรวจสอบว่าไฟล์นี้มีอยู่ใน tempFiles แล้วหรือไม่
            $fileName = $fileData['name'] ?? $fileData['file_name'] ?? '';
            $filePath = $fileData['path'] ?? $fileData['file_path'] ?? '';

            // ตรวจสอบไฟล์ซ้ำโดยใช้ชื่อไฟล์หรือ path
            $fileExists = false;
            foreach ($this->tempFiles as $existingFile) {
                $existingName = $existingFile['name'] ?? $existingFile['file_name'] ?? '';
                $existingPath = $existingFile['path'] ?? $existingFile['file_path'] ?? '';

                if (($fileName && $existingName === $fileName) ||
                    ($filePath && $existingPath === $filePath)) {
                    $fileExists = true;
                    Log::info('File already exists in tempFiles, skipping', [
                        'fileName' => $fileName,
                        'filePath' => $filePath
                    ]);

                    return [
                        'success' => true,
                        'message' => 'File already exists'
                    ];
                }
            }

            // ตรวจสอบว่าเป็นรูปภาพหรือไม่อย่างละเอียด
            $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
            $fileExtension = $fileData['extension'] ?? '';
            $detectedType = $fileData['detected_type'] ?? '';
            $selectedType = $fileData['selected_type'] ?? '';
            $folder = $fileData['folder'] ?? '';

            // ตรวจสอบจากหลายปัจจัย
            $isImageByType = strpos($fileType, 'image/') === 0;
            $isImageByExt = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
            $isImageByFlag = isset($fileData['is_image']) && $fileData['is_image'] === true;
            $isImageByDetected = $detectedType === 'image';
            $isImageBySelected = $selectedType === 'image';
            $isImageByFolder = $folder === 'images';

            // ให้ความสำคัญกับ is_image flag มากที่สุด เนื่องจากมีการตรวจสอบในฝั่ง JavaScript แล้ว
            $isImage = $isImageByFlag;

            Log::info('File type detection in handleUppyFileUploaded:', [
                'fileName' => $fileData['name'] ?? 'unknown',
                'fileType' => $fileType,
                'fileExtension' => $fileExtension,
                'folder' => $folder,
                'isImageByType' => $isImageByType,
                'isImageByExt' => $isImageByExt,
                'isImageByFlag' => $isImageByFlag,
                'isImageByDetected' => $isImageByDetected,
                'isImageBySelected' => $isImageBySelected,
                'isImageByFolder' => $isImageByFolder,
                'finalDecision' => $isImage ? 'IS IMAGE' : 'NOT IMAGE'
            ]);

            // ถ้าเป็นรูปภาพ ให้ข้ามไป (เพราะนี่คือ ItemFileManager)
            if ($isImage) {
                Log::info('Skipping image file in ItemFileManager: ' . $fileType);
                return [
                    'success' => false,
                    'message' => 'Not a document file'
                ];
            }

            // ไม่ต้องปรับ path แล้ว เพราะไฟล์จะถูกอัพโหลดไปยังโฟลเดอร์ที่ถูกต้องตามประเภทแล้ว
            $filePath = $fileData['path'] ?? '';
            Log::info('File path from upload in handleUppyFileUploaded:', [
                'original_path' => $filePath
            ]);

            // If the file was already saved to item_files by the controller, we don't need to create it again
            if (isset($fileData['id'])) {
                $file = ItemFile::find($fileData['id']);
                if ($file) {
                    Log::info('File already exists in database with ID: ' . $file->id);
                    $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';
                    $this->refreshFiles();
                    return [
                        'success' => true,
                        'message' => 'File already exists'
                    ];
                }
            }

            // If we don't have an item yet (create page), store in temporary array
            if (!$this->item) {
                // Get file path and name
                $relativePath = $fileData['path'] ?? '';
                $fileName = $fileData['name'] ?? '';
                $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
                $fileExtension = $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION);

                // Standardize the path format
                $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $relativePath);

                // The path should already be correct since we're uploading directly to the final location
                // Just log the path for debugging
                Log::info('File uploaded to path: ' . $pathWithoutStorage);

                // Get the actual file size if available
                $fileSize = $fileData['size'] ?? 0;

                // If file size is not provided or is 0, try to get it from the file system
                if ($fileSize == 0) {
                    // Try to get file size from public storage
                    $publicPath = public_path('storage/' . $pathWithoutStorage);
                    if (file_exists($publicPath)) {
                        $fileSize = filesize($publicPath);
                        Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                    }
                    // Try storage path if public path doesn't exist
                    else {
                        $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                        if (file_exists($storagePath)) {
                            $fileSize = filesize($storagePath);
                            Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                        }
                    }
                }

                // Store in temporary array for the UI
                $tempFile = [
                    'id' => 'temp_' . uniqid(),
                    'name' => $fileData['original_name'] ?? $fileName, // ใช้ชื่อไฟล์ต้นฉบับถ้ามี
                    'path' => $relativePath,
                    'type' => $fileType,
                    'extension' => $fileExtension,
                    'size' => $fileSize,
                    'is_image' => false,
                    'file_name' => $fileData['original_name'] ?? $fileName, // ใช้ชื่อไฟล์ต้นฉบับถ้ามี
                    'file_path' => $relativePath,
                    'file_type' => $fileType,
                    'file_extension' => $fileExtension,
                    'file_size' => $fileSize,
                    'sort_order' => count($this->tempFiles) + 1,
                    'is_main' => count($this->tempFiles) === 0,
                    'created_at' => now()->toDateTimeString(),
                    'url' => asset('storage/' . $pathWithoutStorage)
                ];

                $this->tempFiles[] = $tempFile;
                $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';

                // Store in session for backup
                try {
                    $sessionTempFiles = session('temp_files', []);
                    $sessionTempFiles[] = $tempFile;
                    session(['temp_files' => $sessionTempFiles]);

                    Log::info('Stored temporary file in session from handleUppyFileUploaded', [
                        'session_temp_files_count' => count($sessionTempFiles)
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error storing temporary file in session from handleUppyFileUploaded: ' . $e->getMessage());
                }

                // Refresh files to update the view
                $this->refreshFiles();

                // Emit an event to notify the frontend
                $this->dispatch('filesUpdated', [
                    'tempFiles' => $this->tempFiles
                ]);

                return [
                    'success' => true,
                    'message' => 'Temporary file stored successfully'
                ];
            } else {
                // If we have an item, save directly to the database
                // Get file path and name
                $relativePath = $fileData['path'] ?? '';
                $fileName = $fileData['name'] ?? '';
                $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
                $fileExtension = $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION);

                // Get the actual file size if available
                $fileSize = $fileData['size'] ?? 0;

                // Create item file record
                $currentSortOrder = $this->item->files()->max('sort_order') + 1;
                $isFirstFile = $this->item->files()->count() === 0;

                // Create the file record - ใช้ชื่อไฟล์ต้นฉบับ
                $file = ItemFile::create([
                    'item_id' => $this->item->id,
                    'file_name' => $fileData['original_name'] ?? $fileName, // ใช้ชื่อไฟล์ต้นฉบับถ้ามี
                    'file_path' => $relativePath,
                    'file_type' => $fileType,
                    'file_extension' => $fileExtension,
                    'file_size' => $fileSize,
                    'sort_order' => $currentSortOrder,
                    'is_main' => $isFirstFile, // Set as main if it's the first file
                ]);

                Log::info('Created file record in database from handleUppyFileUploaded', [
                    'file_id' => $file->id,
                    'item_id' => $this->item->id,
                    'is_main' => $isFirstFile
                ]);

                $this->successMessage = 'ไฟล์ถูกบันทึกลงฐานข้อมูลเรียบร้อยแล้ว';
            }

            // Refresh files to show the newly uploaded file
            $this->refreshFiles();

            $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';

            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Error in handleUppyFileUploaded: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function handleFileUploaded()
    {
        $this->refreshFiles();
    }

    public function deleteFile($fileId)
    {
        try {
            // ตรวจสอบว่าเป็นไฟล์ชั่วคราวหรือไม่
            if (strpos($fileId, 'temp_') === 0) {
                // เป็นไฟล์ชั่วคราว ให้ลบจาก tempFiles
                Log::info('Deleting temporary file: ' . $fileId);

                // ค้นหาไฟล์ในอาร์เรย์ tempFiles
                $tempFileIndex = null;
                foreach ($this->tempFiles as $index => $tempFile) {
                    if (($tempFile['id'] ?? '') === $fileId) {
                        $tempFileIndex = $index;
                        break;
                    }
                }

                if ($tempFileIndex !== null) {
                    // ดึงข้อมูลไฟล์ก่อนลบ
                    $tempFile = $this->tempFiles[$tempFileIndex];
                    Log::info('Found temporary file to delete', ['tempFile' => $tempFile]);

                    // ลบไฟล์จาก storage ถ้ามี path
                    if (!empty($tempFile['path'])) {
                        $filePath = $tempFile['path'];
                        Log::info('Attempting to delete temporary file: ' . $filePath);

                        // Standardize the path format
                        $filePath = ltrim($filePath, '/');
                        if (strpos($filePath, 'storage/') === 0) {
                            $filePath = substr($filePath, 8); // Remove 'storage/'
                        }

                        // Remove 'public/' prefix if present
                        if (strpos($filePath, 'public/') === 0) {
                            $filePath = substr($filePath, 7); // Remove 'public/'
                        }

                        Log::info('Standardized file path for deletion: ' . $filePath);

                        // ลองลบไฟล์จาก storage
                        $deleted = false;

                        // Try to delete from public storage
                        if (Storage::disk('public')->exists($filePath)) {
                            Storage::disk('public')->delete($filePath);
                            Log::info('Deleted temporary file from public disk: ' . $filePath);
                            $deleted = true;
                        }

                        // Also check with 'public/' prefix (for legacy paths)
                        if (Storage::disk('public')->exists('public/' . $filePath)) {
                            Storage::disk('public')->delete('public/' . $filePath);
                            Log::info('Deleted temporary file from public disk with public/ prefix: public/' . $filePath);
                            $deleted = true;
                        }

                        // Check in the public directory
                        $publicPath = public_path('storage/' . $filePath);
                        if (file_exists($publicPath)) {
                            unlink($publicPath);
                            Log::info('Deleted temporary file from public path: ' . $publicPath);
                            $deleted = true;
                        }

                        // Check in the storage directory
                        $storagePath = storage_path('app/public/' . $filePath);
                        if (file_exists($storagePath)) {
                            unlink($storagePath);
                            Log::info('Deleted temporary file from storage path: ' . $storagePath);
                            $deleted = true;
                        }

                        if (!$deleted) {
                            Log::warning('Could not find temporary file to delete. Tried multiple paths for: ' . $tempFile['path']);
                        }
                    }

                    // ลบไฟล์ออกจากอาร์เรย์
                    array_splice($this->tempFiles, $tempFileIndex, 1);
                    Log::info('Removed temporary file from array. Remaining: ' . count($this->tempFiles));

                    // อัพเดท session
                    session(['temp_files' => $this->tempFiles]);
                    Log::info('Updated session with new tempFiles array');

                    // อัพเดทหน้าจอ
                    $this->refreshFiles();
                    $this->successMessage = 'ไฟล์ถูกลบเรียบร้อยแล้ว';

                    // แจ้งเตือน frontend
                    $this->dispatch('filesUpdated', [
                        'tempFiles' => $this->tempFiles
                    ]);

                    return;
                } else {
                    Log::warning('Temporary file not found in tempFiles array: ' . $fileId);
                    $this->errorMessage = 'ไม่พบไฟล์ที่ต้องการลบ';
                    return;
                }
            }

            // ถ้าไม่ใช่ไฟล์ชั่วคราว ให้ลบจากฐานข้อมูล
            $file = ItemFile::findOrFail($fileId);

            // Check if this is the main file
            $isMainFile = $file->is_main;

            // Delete the file from storage
            $filePath = $file->file_path;
            Log::info('Attempting to delete item file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            // Try to delete from storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted file from storage: ' . $filePath);
            }

            // Delete the record
            $file->delete();

            // No need to set another file as main since we now allow having no main file

            $this->successMessage = 'ไฟล์ถูกลบเรียบร้อยแล้ว';
            $this->refreshFiles();

        } catch (\Exception $e) {
            Log::error('Error deleting file: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการลบไฟล์: ' . $e->getMessage();
        }
    }

    public function setMainFile($fileId)
    {
        try {
            $file = ItemFile::findOrFail($fileId);

            // Check if this file is already the main file
            if ($file->is_main) {
                // If it's already main, unset it (no file will be main)
                $file->is_main = false;
                $file->save();
                $this->successMessage = 'ยกเลิกการตั้งค่าไฟล์หลักเรียบร้อยแล้ว';
            } else {
                // Reset all files to not be main
                ItemFile::where('item_id', $this->itemId)
                    ->update(['is_main' => false]);

                // Set the selected file as main
                $file->is_main = true;
                $file->save();

                $this->successMessage = 'ตั้งค่าไฟล์หลักเรียบร้อยแล้ว';
            }

            $this->refreshFiles();

        } catch (\Exception $e) {
            Log::error('Error setting main file: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการตั้งค่าไฟล์หลัก: ' . $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.item-file-manager');
    }
}
