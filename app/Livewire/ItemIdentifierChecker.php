<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Item;
use Illuminate\Support\Facades\Log;

class ItemIdentifierChecker extends Component
{
    public $identifier = '';
    public $itemId = null;
    public $isValid = null;
    public $message = '';
    public $isChecking = false;

    public function mount($itemId = null)
    {
        $this->itemId = $itemId;

        // If editing an existing item, load its identifier
        if ($itemId) {
            $item = Item::find($itemId);
            if ($item) {
                $this->identifier = $item->identifier_no;
                // Validate the current identifier
                if ($this->identifier) {
                    $this->checkIdentifier();
                }
            }
        }
    }

    public function updatedIdentifier()
    {
        $this->reset(['isValid', 'message']);

        if (empty($this->identifier)) {
            // If identifier is empty, set it as valid since it's optional
            $this->isValid = true;
            $this->message = 'รหัสรายการไม่จำเป็นต้องกรอก';
            return;
        }

        $this->isChecking = true;
        $this->checkIdentifier();
    }

    public function checkIdentifier()
    {
        try {
            if (empty($this->identifier)) {
                $this->isValid = true;
                $this->message = 'รหัสรายการไม่จำเป็นต้องกรอก';
                return;
            }

            // Check if identifier exists in database
            $query = Item::where('identifier_no', $this->identifier);

            // Exclude current item if editing
            if ($this->itemId) {
                $query->where('id', '!=', $this->itemId);
            }

            $exists = $query->exists();

            $this->isValid = !$exists;
            $this->message = $exists
                ? 'รหัสรายการนี้ถูกใช้งานแล้ว'
                : 'รหัสรายการนี้สามารถใช้งานได้';
        } catch (\Exception $e) {
            Log::error('Error checking identifier: ' . $e->getMessage());
            $this->isValid = false;
            $this->message = 'เกิดข้อผิดพลาดในการตรวจสอบรหัสรายการ';
        } finally {
            $this->isChecking = false;
        }
    }

    // Removed generateIdentifier method as requested

    public function render()
    {
        return view('livewire.item-identifier-checker');
    }
}
