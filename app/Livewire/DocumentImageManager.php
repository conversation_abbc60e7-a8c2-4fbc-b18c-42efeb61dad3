<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Document;
use App\Models\DocumentImage;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class DocumentImageManager extends Component
{
    use WithFileUploads;

    public $document;
    public $images = [];
    public $newImages = [];
    public $isUploading = false;
    public $uploadProgress = 0;
    public $successMessage = '';
    public $errorMessage = '';

    protected $listeners = [
        'refreshImages' => 'refreshImages',
        'imageUploaded' => 'handleImageUploaded',
        'handleUppyFileUploaded' => 'handleUppyFileUploaded'
    ];

    public function mount(Document $document)
    {
        $this->document = $document;
        $this->refreshImages();
    }

    public function refreshImages()
    {
        $this->document->refresh();
        $this->images = $this->document->images()->orderBy('sort_order')->get();
    }

    public function handleUppyFileUploaded($fileData)
    {
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            Log::info('Received file data in DocumentImageManager component:', $fileData);

            // Check if this is an image file
            $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
            $isImage = strpos($fileType, 'image/') === 0 || isset($fileData['is_image']) && $fileData['is_image'] === true;

            if (!$isImage) {
                Log::info('Skipping non-image file in DocumentImageManager: ' . $fileType);
                return [
                    'success' => false,
                    'message' => 'Not an image file'
                ];
            }

            if (!isset($fileData['path']) || !isset($fileData['name'])) {
                Log::error('Invalid file data received: missing path or name');
                throw new \Exception('Invalid file data received');
            }

            $relativePath = $fileData['path'];
            $fileName = $fileData['name'];

            Log::info('Processing image in Livewire: ' . $fileName . ' with path: ' . $relativePath);

            // Standardize the path format
            $standardPath = $relativePath;

            // Extract the actual path without /storage/ prefix
            $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $standardPath);

            // Remove 'public/' prefix if present to prevent double 'public' in the path
            if (strpos($pathWithoutStorage, 'public/') === 0) {
                $pathWithoutStorage = substr($pathWithoutStorage, 7); // Remove 'public/'
                Log::info('Removed public/ prefix from path: ' . $pathWithoutStorage);
            }

            // Check if the file exists in the public storage directory
            $fullPath = public_path('storage/' . $pathWithoutStorage);

            Log::info('Standardized path: ' . $standardPath);
            Log::info('Path without storage prefix: ' . $pathWithoutStorage);
            Log::info('Checking if file exists at: ' . $fullPath);

            if (!file_exists($fullPath)) {
                Log::error('File does not exist at path: ' . $fullPath);

                // Try alternative paths for debugging
                $alt1 = storage_path('app/public/' . $pathWithoutStorage);
                $alt2 = storage_path('app/public/public/' . $pathWithoutStorage); // Check the incorrect path with double 'public'

                Log::info('Checking alternative path 1: ' . $alt1 . ' exists: ' . (file_exists($alt1) ? 'Yes' : 'No'));
                Log::info('Checking alternative path 2: ' . $alt2 . ' exists: ' . (file_exists($alt2) ? 'Yes' : 'No'));

                // Try to copy from the correct storage path
                if (file_exists($alt1)) {
                    try {
                        $storageDir = dirname($pathWithoutStorage);
                        if (!file_exists(public_path('storage/' . $storageDir))) {
                            mkdir(public_path('storage/' . $storageDir), 0755, true);
                        }

                        copy($alt1, $fullPath);
                        Log::info('Copied file from storage/app/public to public/storage path: ' . $fullPath);
                    } catch (\Exception $e) {
                        Log::error('Failed to copy file from storage/app/public: ' . $e->getMessage());
                    }
                }
                // If not found in the correct path, try the incorrect path
                else if (file_exists($alt2)) {
                    try {
                        // First, ensure the correct storage directory exists
                        $storageDir = dirname($pathWithoutStorage);
                        $correctStoragePath = storage_path('app/public/' . $storageDir);
                        if (!file_exists($correctStoragePath)) {
                            mkdir($correctStoragePath, 0755, true);
                        }

                        // Copy from incorrect to correct storage path
                        $correctPath = storage_path('app/public/' . $pathWithoutStorage);
                        copy($alt2, $correctPath);
                        Log::info('Copied file from incorrect path to correct storage path: ' . $correctPath);

                        // Then copy to public path
                        if (!file_exists(public_path('storage/' . $storageDir))) {
                            mkdir(public_path('storage/' . $storageDir), 0755, true);
                        }

                        copy($correctPath, $fullPath);
                        Log::info('Copied file to public path: ' . $fullPath);
                    } catch (\Exception $e) {
                        Log::error('Failed to copy file from incorrect path: ' . $e->getMessage());
                    }
                }
            } else {
                Log::info('File exists at path: ' . $fullPath . ' with size: ' . filesize($fullPath) . ' bytes');
            }

            // If the image was already saved to document_images by the controller, we don't need to create it again
            if (isset($fileData['id'])) {
                $image = DocumentImage::find($fileData['id']);
                if ($image) {
                    Log::info('Image already exists in database with ID: ' . $image->id);
                    $this->successMessage = 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว';
                    $this->refreshImages();
                    $this->dispatch('imagesUpdated');
                    return [
                        'success' => true,
                        'message' => 'Image already exists'
                    ];
                }
            }

            // Create document image record
            $currentSortOrder = $this->document->images()->max('sort_order') + 1;

            // Check if this is the first image
            $isFirstImage = $this->document->images()->count() === 0;

            // Create the image record
            $image = DocumentImage::create([
                'document_id' => $this->document->id,
                'image_name' => $fileName,
                'image_path' => $relativePath,
                'image_type' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION),
                'sort_order' => $currentSortOrder,
                'is_main' => $isFirstImage, // Set as main if it's the first image
            ]);

            // If this is the first image, log it
            if ($isFirstImage) {
                Log::info('Set first image as main: ' . $image->id);
            }

            $this->successMessage = 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

            return [
                'success' => true,
                'message' => 'Image uploaded successfully'
            ];

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ: ' . $e->getMessage();

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function handleImageUploaded()
    {
        $this->refreshImages();

        // Ensure we have a main image set if there are images
        if ($this->images->count() > 0 && !$this->images->where('is_main', true)->count()) {
            // No main image set, set the first one as main
            $firstImage = $this->images->first();
            $firstImage->is_main = true;
            $firstImage->save();

            // No need to update document.image_path anymore
            // We're using is_main field in document_images table instead

            Log::info('Auto-set main image during refresh: ' . $firstImage->id);

            // Refresh images again to get updated data
            $this->images = $this->document->images()->orderBy('sort_order')->get();
        }
    }

    public function deleteImage($imageId)
    {
        try {
            $image = DocumentImage::findOrFail($imageId);

            // Check if this is the main image
            if ($image->is_main) {
                // Find another image to set as main
                $newMainImage = $this->document->images()
                    ->where('id', '!=', $image->id)
                    ->orderBy('sort_order')
                    ->first();

                if ($newMainImage) {
                    // Set new main image
                    $newMainImage->is_main = true;
                    $newMainImage->save();

                    // No need to update document.image_path anymore
                    // We're using is_main field in document_images table instead
                }
            }

            // Delete the file from storage
            // Image path may be in format '/storage/images/filename.jpg'
            $filePath = $image->image_path;
            Log::info('Attempting to delete image file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            Log::info('Standardized file path for deletion: ' . $filePath);

            // Check and delete from public storage
            $deleted = false;

            // Try to delete from public storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted image from public disk: ' . $filePath);
                $deleted = true;
            }

            // Also check with 'public/' prefix (for legacy paths)
            if (Storage::disk('public')->exists('public/' . $filePath)) {
                Storage::disk('public')->delete('public/' . $filePath);
                Log::info('Deleted image from public disk with public/ prefix: public/' . $filePath);
                $deleted = true;
            }

            // Check in the public directory
            $publicPath = public_path('storage/' . $filePath);
            if (file_exists($publicPath)) {
                unlink($publicPath);
                Log::info('Deleted image from public path: ' . $publicPath);
                $deleted = true;
            }

            // Check in the storage directory
            $storagePath = storage_path('app/public/' . $filePath);
            if (file_exists($storagePath)) {
                unlink($storagePath);
                Log::info('Deleted image from storage path: ' . $storagePath);
                $deleted = true;
            }

            // Check in the incorrect storage directory (with double public)
            $incorrectStoragePath = storage_path('app/public/public/' . $filePath);
            if (file_exists($incorrectStoragePath)) {
                unlink($incorrectStoragePath);
                Log::info('Deleted image from incorrect storage path: ' . $incorrectStoragePath);
                $deleted = true;
            }

            if (!$deleted) {
                Log::warning('Could not find image file to delete. Tried multiple paths for: ' . $image->image_path);
            }

            // Delete the record
            $image->delete();

            $this->successMessage = 'รูปภาพถูกลบเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการลบรูปภาพ: ' . $e->getMessage();
        }
    }

    public function setMainImage($imageId)
    {
        try {
            $image = DocumentImage::findOrFail($imageId);

            // Reset all images to not be main
            $this->document->images()->update(['is_main' => false]);

            // Set this image as the main image
            $image->is_main = true;

            // No need to update document.image_path anymore
            // We're using is_main field in document_images table instead

            // Update sort order to make this image first
            $image->sort_order = 0;
            $image->save();

            // Re-order other images
            $this->document->images()
                ->where('id', '!=', $image->id)
                ->get()
                ->each(function ($img, $index) {
                    $img->sort_order = $index + 1;
                    $img->save();
                });

            $this->successMessage = 'รูปภาพหลักถูกตั้งค่าเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

            Log::info('Set main image: ' . $image->id . ' for document: ' . $this->document->id);

        } catch (\Exception $e) {
            Log::error('Error setting main image: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการตั้งค่ารูปภาพหลัก: ' . $e->getMessage();
        }
    }

    public function updateImageOrder($orderedIds)
    {
        try {
            foreach ($orderedIds as $index => $id) {
                DocumentImage::where('id', $id)->update(['sort_order' => $index]);
            }

            $this->successMessage = 'ลำดับรูปภาพถูกอัปเดตเรียบร้อยแล้ว';
            $this->refreshImages();

            // Emit an event to notify the frontend that images have been updated
            $this->dispatch('imagesUpdated');

        } catch (\Exception $e) {
            $this->errorMessage = 'เกิดข้อผิดพลาดในการจัดเรียงรูปภาพ: ' . $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.document-image-manager');
    }
}
