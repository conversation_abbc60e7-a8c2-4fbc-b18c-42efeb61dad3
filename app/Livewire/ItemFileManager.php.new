<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Item;
use App\Models\ItemFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ItemFileManager extends Component
{
    use WithFileUploads;

    public $item;
    public $itemId;
    public $files = [];
    public $itemFiles = [];
    public $isUploading = false;
    public $uploadProgress = 0;
    public $successMessage = '';
    public $errorMessage = '';
    public $uploadedFileData = null;
    public $tempFiles = [];

    protected $listeners = [
        'refreshFiles' => 'refreshFiles',
        'fileUploaded' => 'handleFileUploaded',
        'uppyFileUploaded' => 'handleUppyFileUploaded',
        'uppyUploadSuccess' => 'handleUppyUploadSuccess',
        'uploadFile' => 'handleUploadFile',
        'handleUppyUpload' => 'handleUppyUpload'
    ];

    public function mount($item = null)
    {
        $this->item = $item;
        $this->itemId = $item ? $item->id : null;

        // Initialize tempFiles as empty array if not already set
        if (!is_array($this->tempFiles)) {
            $this->tempFiles = [];
        }

        if ($this->item) {
            $this->refreshFiles();
        } else {
            // Even if we don't have an item, we might have temporary files
            $this->refreshFiles();
        }

        Log::info('ItemFileManager mounted', [
            'hasItem' => !is_null($this->item),
            'tempFilesCount' => count($this->tempFiles),
            'itemFilesCount' => $this->itemFiles->count()
        ]);

        // Emit an event to notify the frontend that files have been updated
        // In Livewire v3, dispatch() is used for both component and browser events
        $this->dispatch('filesUpdated', [
            'tempFiles' => $this->tempFiles
        ]);
    }

    public function refreshFiles()
    {
        if ($this->item) {
            $this->item->refresh();
            $this->itemFiles = $this->item->files()->orderBy('sort_order')->get();
        } else {
            // Use temporary files if we don't have an item
            $this->itemFiles = collect($this->tempFiles);
        }

        // Log the current files for debugging
        Log::info('Refreshed files. Count: ' . $this->itemFiles->count());

        // Emit an event to notify the frontend that files have been updated
        // In Livewire v3, dispatch() is used for both component and browser events
        $this->dispatch('filesUpdated', [
            'tempFiles' => $this->tempFiles
        ]);
    }

    public function handleUppyUploadSuccess()
    {
        Log::info('Received Uppy upload success event in ItemFileManager');

        if (!$this->uploadedFileData) {
            Log::error('No uploadedFileData available in ItemFileManager');
            return;
        }

        $fileData = $this->uploadedFileData;
        Log::info('Processing fileData in ItemFileManager', ['fileData' => $fileData]);

        // Check if this is a document file (not an image)
        $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
        $isImage = strpos($fileType, 'image/') === 0 || (isset($fileData['is_image']) && $fileData['is_image'] === true);

        if ($isImage) {
            Log::info('Skipping image file in ItemFileManager: ' . $fileType);
            return;
        }

        // Process the file data
        $this->handleUppyFileUploaded($fileData);

        // Refresh the files
        $this->refreshFiles();

        // Reset the uploaded file data
        $this->uploadedFileData = null;
    }

    public function handleUppyUpload($fileData)
    {
        Log::info('Received handleUppyUpload in ItemFileManager', ['fileData' => $fileData]);

        // Set the uploaded file data
        $this->uploadedFileData = $fileData;

        // Check if this is a document file (not an image)
        $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
        $isImage = strpos($fileType, 'image/') === 0 || (isset($fileData['is_image']) && $fileData['is_image'] === true);

        if ($isImage) {
            Log::info('Skipping image file in ItemFileManager: ' . $fileType);
            return [
                'success' => false,
                'message' => 'Not a document file'
            ];
        }

        try {
            // Get file path and name
            $relativePath = $fileData['path'] ?? '';
            $fileName = $fileData['name'] ?? '';

            // Standardize the path format
            $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $relativePath);

            // The path should already be correct since we're uploading directly to the final location
            // Just ensure it starts with the appropriate folder prefix for consistency
            if (!str_starts_with($pathWithoutStorage, 'files/') &&
                !str_starts_with($pathWithoutStorage, 'documents/') &&
                !str_starts_with($pathWithoutStorage, 'audio/') &&
                !str_starts_with($pathWithoutStorage, 'video/')) {
                Log::info('Path does not start with expected folder prefix: ' . $pathWithoutStorage);
                // No need to modify the path as it should already be in the correct location
            }

            // Get the actual file size if available
            $fileSize = $fileData['size'] ?? 0;

            // If file size is not provided or is 0, try to get it from the file system
            if ($fileSize == 0) {
                // Try to get file size from public storage
                $publicPath = public_path('storage/' . $pathWithoutStorage);
                if (file_exists($publicPath)) {
                    $fileSize = filesize($publicPath);
                    Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                }
                // Try storage path if public path doesn't exist
                else {
                    $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                    if (file_exists($storagePath)) {
                        $fileSize = filesize($storagePath);
                        Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                    }
                }
            }

            // Create a temporary file object
            $tempFile = [
                'id' => 'temp_' . uniqid(),
                'name' => $fileName,
                'path' => $relativePath,
                'type' => $fileType,
                'extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION),
                'size' => $fileSize,
                'is_image' => false,
                // ข้อมูลที่จำเป็นสำหรับ ItemFile
                'file_name' => $fileName,
                'file_path' => $relativePath,
                'file_type' => $fileType, // ใช้ MIME type
                'file_extension' => $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION), // เพิ่ม field นี้เพื่อให้ตรงกับที่ ItemFile ต้องการ
                'mime_type' => $fileType,
                'file_size' => $fileSize,
                'sort_order' => count($this->tempFiles) + 1,
                'is_main' => count($this->tempFiles) === 0, // Set as main if it's the first file
                'created_at' => now()->toDateTimeString(),
                'url' => asset('storage/' . $pathWithoutStorage)
            ];

            // Add to temporary files
            $this->tempFiles[] = $tempFile;

            Log::info('Added temporary file in handleUppyUpload', [
                'tempFile' => $tempFile,
                'tempFilesCount' => count($this->tempFiles)
            ]);

            // Refresh the files
            $this->refreshFiles();

            // Emit an event to notify the frontend that files have been updated
            // In Livewire v3, dispatch() is used for both component and browser events
            $this->dispatch('filesUpdated', [
                'tempFiles' => $this->tempFiles
            ]);

            return [
                'success' => true,
                'message' => 'File uploaded successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Error in handleUppyUpload: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function setUploadedFileData($fileData)
    {
        Log::info('Setting uploadedFileData in ItemFileManager', ['fileData' => $fileData]);
        $this->uploadedFileData = $fileData;

        // Debug current tempFiles before processing
        Log::info('Current tempFiles before processing', ['count' => count($this->tempFiles), 'tempFiles' => $this->tempFiles]);

        // Process the uploaded file
        $this->handleUppyUploadSuccess();

        // Debug tempFiles after processing
        Log::info('Updated tempFiles after processing', ['count' => count($this->tempFiles), 'tempFiles' => $this->tempFiles]);

        // Force refresh the view
        $this->dispatch('filesUpdated');
    }

    public function handleUploadFile($params = null)
    {
        Log::info('Received uploadFile event in ItemFileManager', ['params' => $params]);

        // Extract fileData from params if provided
        $fileData = null;
        if (is_array($params) && isset($params['fileData'])) {
            $fileData = $params['fileData'];
        }

        // If no fileData in params, check if it was passed directly
        if (!$fileData && $params && !is_array($params)) {
            $fileData = $params;
        }

        // If still no fileData, check if it was set in the component
        if (!$fileData && $this->uploadedFileData) {
            $fileData = $this->uploadedFileData;
        }

        if (!$fileData) {
            Log::error('No fileData received in handleUploadFile');
            return;
        }

        // Set the uploaded file data and process it
        $this->uploadedFileData = $fileData;
        $this->handleUppyUploadSuccess();

        // Force refresh the view
        $this->dispatch('filesUpdated');
    }

    public function handleUppyFileUploaded($fileData)
    {
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            Log::info('Uppy file uploaded event received in ItemFileManager', ['fileData' => $fileData]);

            // Check if this is a document file (not an image)
            $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
            $isImage = strpos($fileType, 'image/') === 0 || (isset($fileData['is_image']) && $fileData['is_image'] === true);

            if ($isImage) {
                Log::info('Skipping image file in ItemFileManager: ' . $fileType);
                return [
                    'success' => false,
                    'message' => 'Not a document file'
                ];
            }

            // If the file was already saved to item_files by the controller, we don't need to create it again
            if (isset($fileData['id'])) {
                $file = ItemFile::find($fileData['id']);
                if ($file) {
                    Log::info('File already exists in database with ID: ' . $file->id);
                    $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';
                    $this->refreshFiles();
                    return [
                        'success' => true,
                        'message' => 'File already exists'
                    ];
                }
            }

            // If we don't have an item yet (create page), store in temporary array
            if (!$this->item) {
                // Get file path and name
                $relativePath = $fileData['path'] ?? '';
                $fileName = $fileData['name'] ?? '';
                $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
                $fileExtension = $fileData['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION);

                // Standardize the path format
                $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $relativePath);

                // The path should already be correct since we're uploading directly to the final location
                // Just ensure it starts with the appropriate folder prefix for consistency
                if (!str_starts_with($pathWithoutStorage, 'files/') &&
                    !str_starts_with($pathWithoutStorage, 'documents/') &&
                    !str_starts_with($pathWithoutStorage, 'audio/') &&
                    !str_starts_with($pathWithoutStorage, 'video/')) {
                    Log::info('Path does not start with expected folder prefix: ' . $pathWithoutStorage);
                    // No need to modify the path as it should already be in the correct location
                }

                // Get the actual file size if available
                $fileSize = $fileData['size'] ?? 0;

                // If file size is not provided or is 0, try to get it from the file system
                if ($fileSize == 0) {
                    // Try to get file size from public storage
                    $publicPath = public_path('storage/' . $pathWithoutStorage);
                    if (file_exists($publicPath)) {
                        $fileSize = filesize($publicPath);
                        Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                    }
                    // Try storage path if public path doesn't exist
                    else {
                        $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                        if (file_exists($storagePath)) {
                            $fileSize = filesize($storagePath);
                            Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                        }
                    }
                }

                // Store in temporary array for the UI
                $tempFile = [
                    'id' => 'temp_' . uniqid(),
                    'name' => $fileName,
                    'path' => $relativePath,
                    'type' => $fileType,
                    'extension' => $fileExtension,
                    'size' => $fileSize,
                    'is_image' => false,
                    'file_name' => $fileName,
                    'file_path' => $relativePath,
                    'file_type' => $fileType,
                    'file_extension' => $fileExtension,
                    'file_size' => $fileSize,
                    'sort_order' => count($this->tempFiles) + 1,
                    'is_main' => count($this->tempFiles) === 0,
                    'created_at' => now()->toDateTimeString(),
                    'url' => asset('storage/' . $pathWithoutStorage)
                ];

                $this->tempFiles[] = $tempFile;
                $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';

                // Refresh files to update the view
                $this->refreshFiles();

                // Emit an event to notify the frontend
                $this->dispatch('filesUpdated', [
                    'tempFiles' => $this->tempFiles
                ]);

                return [
                    'success' => true,
                    'message' => 'Temporary file stored successfully'
                ];
            }

            // Refresh files to show the newly uploaded file
            $this->refreshFiles();

            $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';

            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Error in handleUppyFileUploaded: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function handleFileUploaded()
    {
        $this->refreshFiles();
    }

    public function deleteFile($fileId)
    {
        try {
            $file = ItemFile::findOrFail($fileId);

            // Check if this is the main file
            $isMainFile = $file->is_main;

            // Delete the file from storage
            $filePath = $file->file_path;
            Log::info('Attempting to delete item file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            // Try to delete from storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted file from storage: ' . $filePath);
            }

            // Delete the record
            $file->delete();

            // No need to set another file as main since we now allow having no main file

            $this->successMessage = 'ไฟล์ถูกลบเรียบร้อยแล้ว';
            $this->refreshFiles();

        } catch (\Exception $e) {
            Log::error('Error deleting file: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการลบไฟล์: ' . $e->getMessage();
        }
    }

    public function setMainFile($fileId)
    {
        try {
            $file = ItemFile::findOrFail($fileId);

            // Check if this file is already the main file
            if ($file->is_main) {
                // If it's already main, unset it (no file will be main)
                $file->is_main = false;
                $file->save();
                $this->successMessage = 'ยกเลิกการตั้งค่าไฟล์หลักเรียบร้อยแล้ว';
            } else {
                // Reset all files to not be main
                ItemFile::where('item_id', $this->itemId)
                    ->update(['is_main' => false]);

                // Set the selected file as main
                $file->is_main = true;
                $file->save();

                $this->successMessage = 'ตั้งค่าไฟล์หลักเรียบร้อยแล้ว';
            }

            $this->refreshFiles();

        } catch (\Exception $e) {
            Log::error('Error setting main file: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการตั้งค่าไฟล์หลัก: ' . $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.item-file-manager');
    }
}
