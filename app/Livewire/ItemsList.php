<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Item;
use App\Models\Category;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;
use App\Models\ItemType;

class ItemsList extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Filter parameters
    public $search = '';
    public $categoryIds = [];
    public $itemTypeIds = [];
    public $materialIds = [];
    public $languageIds = [];
    public $scriptIds = [];
    public $provinceCodes = []; // เพิ่มการกรองตามจังหวัด (ใช้ province code)
    public $yearFrom = '';
    public $yearTo = '';
    public $sortBy = 'newest';
    public $perPage = 12;
    public $viewMode = 'grid'; // 'grid' or 'table'

    // Querystring parameters
    protected $queryString = [
        'search' => ['except' => ''],
        'categoryIds' => ['except' => []],
        'itemTypeIds' => ['except' => []],
        'materialIds' => ['except' => []],
        'languageIds' => ['except' => []],
        'scriptIds' => ['except' => []],
        'provinceCodes' => ['except' => []], // เพิ่มการกรองตามจังหวัด (ใช้ province code)
        'yearFrom' => ['except' => ''],
        'yearTo' => ['except' => ''],
        'sortBy' => ['except' => 'newest'],
        'perPage' => ['except' => 12],
        'viewMode' => ['except' => 'grid'],
    ];

    public function mount()
    {
        // Initialize from query parameters if available
        $this->search = request()->query('search', $this->search);

        // Handle array parameters
        $this->categoryIds = is_array(request()->query('categoryIds'))
            ? request()->query('categoryIds')
            : $this->categoryIds;

        $this->itemTypeIds = is_array(request()->query('itemTypeIds'))
            ? request()->query('itemTypeIds')
            : $this->itemTypeIds;

        $this->materialIds = is_array(request()->query('materialIds'))
            ? request()->query('materialIds')
            : $this->materialIds;

        $this->languageIds = is_array(request()->query('languageIds'))
            ? request()->query('languageIds')
            : $this->languageIds;

        $this->scriptIds = is_array(request()->query('scriptIds'))
            ? request()->query('scriptIds')
            : $this->scriptIds;

        $this->provinceCodes = is_array(request()->query('provinceCodes'))
            ? request()->query('provinceCodes')
            : $this->provinceCodes;

        $this->yearFrom = request()->query('yearFrom', $this->yearFrom);
        $this->yearTo = request()->query('yearTo', $this->yearTo);
        $this->sortBy = request()->query('sortBy', $this->sortBy);
        $this->perPage = request()->query('perPage', $this->perPage);
        $this->viewMode = request()->query('viewMode', $this->viewMode);

        // Handle single ID parameters
        if ($categoryId = request()->query('category_id')) {
            $this->categoryIds[] = $categoryId;
        }

        if ($itemTypeId = request()->query('item_type_id')) {
            $this->itemTypeIds[] = $itemTypeId;
        }

        if ($materialId = request()->query('material_id')) {
            $this->materialIds[] = $materialId;
        }

        if ($languageId = request()->query('language_id')) {
            $this->languageIds[] = $languageId;
        }

        if ($scriptId = request()->query('script_id')) {
            $this->scriptIds[] = $scriptId;
        }

        if ($provinceCode = request()->query('province')) {
            $this->provinceCodes[] = $provinceCode;
        }
    }

    public function updated($property)
    {
        if (in_array($property, [
            'search', 'categoryIds', 'itemTypeIds', 'materialIds',
            'languageIds', 'scriptIds', 'provinceCodes', 'yearFrom', 'yearTo', 'sortBy', 'perPage'
        ])) {
            $this->resetPage();
        }
    }

    public function resetFilters()
    {
        $this->reset([
            'search', 'categoryIds', 'itemTypeIds', 'materialIds',
            'languageIds', 'scriptIds', 'provinceCodes', 'yearFrom', 'yearTo'
        ]);
        $this->resetPage();
    }

    public function setViewMode($mode)
    {
        $this->viewMode = $mode;
    }

    public function setPerPage($perPage)
    {
        $this->perPage = $perPage;
        $this->resetPage();
    }

    public function setSortBy($sortBy)
    {
        $this->sortBy = $sortBy;
        $this->resetPage();
    }

    public function render()
    {
        // Build query
        $query = Item::with(['category', 'images', 'itemType', 'material', 'language', 'script']);

        // Apply search filter
        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', "%{$this->search}%")
                  ->orWhere('description', 'like', "%{$this->search}%")
                  ->orWhere('identifier_no', 'like', "%{$this->search}%")
                  ->orWhere('other_title1', 'like', "%{$this->search}%")
                  ->orWhere('other_title2', 'like', "%{$this->search}%")
                  ->orWhere('other_title3', 'like', "%{$this->search}%");
            });
        }

        // Apply category filter
        if (!empty($this->categoryIds)) {
            $query->whereIn('category_id', $this->categoryIds);
        }

        // Apply item type filter
        if (!empty($this->itemTypeIds)) {
            $query->whereIn('item_type_id', $this->itemTypeIds);
        }

        // Apply material filter
        if (!empty($this->materialIds)) {
            $query->whereIn('material_id', $this->materialIds);
        }

        // Apply language filter
        if (!empty($this->languageIds)) {
            $query->whereIn('language_id', $this->languageIds);
        }

        // Apply script filter
        if (!empty($this->scriptIds)) {
            $query->whereIn('script_id', $this->scriptIds);
        }

        // Apply province filter
        if (!empty($this->provinceCodes)) {
            $query->whereIn('province', $this->provinceCodes);
        }

        // Apply year range filter
        if ($this->yearFrom) {
            $query->where('year', '>=', $this->yearFrom);
        }

        if ($this->yearTo) {
            $query->where('year', '<=', $this->yearTo);
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'a-z':
                $query->orderBy('title', 'asc');
                break;
            case 'z-a':
                $query->orderBy('title', 'desc');
                break;
            case 'year-asc':
                $query->orderBy('year', 'asc');
                break;
            case 'year-desc':
                $query->orderBy('year', 'desc');
                break;
            case 'views':
                $query->orderBy('views', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Get items with pagination
        $items = $query->paginate($this->perPage);

        // Get filter options
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $provinces = \App\Models\Province::orderBy('name_th')->get();

        return view('livewire.items-list', [
            'items' => $items,
            'categories' => $categories,
            'itemTypes' => $itemTypes,
            'materials' => $materials,
            'languages' => $languages,
            'scripts' => $scripts,
            'provinces' => $provinces,
            'totalResults' => $items->total()
        ]);
    }
}
