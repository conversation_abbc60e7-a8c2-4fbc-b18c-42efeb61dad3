<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Document;
use App\Models\DocumentFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class DocumentFileManager extends Component
{
    use WithFileUploads;

    public $document;
    public $documentId;
    public $files = [];
    public $documentFiles = [];
    public $isUploading = false;
    public $uploadProgress = 0;
    public $successMessage = '';
    public $errorMessage = '';

    protected $listeners = [
        'refreshFiles' => 'refreshFiles',
        'fileUploaded' => 'handleFileUploaded',
        'uppyFileUploaded' => 'handleUppyFileUploaded'
    ];

    public function mount(Document $document)
    {
        $this->document = $document;
        $this->documentId = $document->id;
        $this->refreshFiles();
    }

    public function refreshFiles()
    {
        $this->document->refresh();
        $this->documentFiles = $this->document->files()->orderBy('sort_order')->get();
    }

    public function handleUppyFileUploaded($fileData)
    {
        $this->successMessage = '';
        $this->errorMessage = '';

        try {
            Log::info('Uppy file uploaded event received in DocumentFileManager', ['fileData' => $fileData]);

            // Check if this is a document file (not an image)
            $fileType = $fileData['type'] ?? $fileData['mime_type'] ?? '';
            $isImage = strpos($fileType, 'image/') === 0 || (isset($fileData['is_image']) && $fileData['is_image'] === true);

            if ($isImage) {
                Log::info('Skipping image file in DocumentFileManager: ' . $fileType);
                return [
                    'success' => false,
                    'message' => 'Not a document file'
                ];
            }

            // If the file was already saved to document_files by the controller, we don't need to create it again
            if (isset($fileData['id'])) {
                $file = DocumentFile::find($fileData['id']);
                if ($file) {
                    Log::info('File already exists in database with ID: ' . $file->id);
                    $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';
                    $this->refreshFiles();
                    return [
                        'success' => true,
                        'message' => 'File already exists'
                    ];
                }
            }

            // Refresh files to show the newly uploaded file
            $this->refreshFiles();

            $this->successMessage = 'ไฟล์ถูกอัพโหลดเรียบร้อยแล้ว';

            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Error in handleUppyFileUploaded: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    public function handleFileUploaded()
    {
        $this->refreshFiles();
    }

    public function deleteFile($fileId)
    {
        try {
            $file = DocumentFile::findOrFail($fileId);

            // Check if this is the main file
            $isMainFile = $file->is_main;

            // Delete the file from storage
            $filePath = $file->file_path;
            Log::info('Attempting to delete document file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            // Try to delete from storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted file from storage: ' . $filePath);
            }

            // Delete the record
            $file->delete();

            // No need to set another file as main since we now allow having no main file

            $this->successMessage = 'ไฟล์ถูกลบเรียบร้อยแล้ว';
            $this->refreshFiles();

        } catch (\Exception $e) {
            Log::error('Error deleting file: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการลบไฟล์: ' . $e->getMessage();
        }
    }

    public function setMainFile($fileId)
    {
        try {
            $file = DocumentFile::findOrFail($fileId);

            // Check if this file is already the main file
            if ($file->is_main) {
                // If it's already main, unset it (no file will be main)
                $file->is_main = false;
                $file->save();
                $this->successMessage = 'ยกเลิกการตั้งค่าไฟล์หลักเรียบร้อยแล้ว';
            } else {
                // Reset all files to not be main
                DocumentFile::where('document_id', $this->documentId)
                    ->update(['is_main' => false]);

                // Set the selected file as main
                $file->is_main = true;
                $file->save();

                $this->successMessage = 'ตั้งค่าไฟล์หลักเรียบร้อยแล้ว';
            }

            $this->refreshFiles();

        } catch (\Exception $e) {
            Log::error('Error setting main file: ' . $e->getMessage());
            $this->errorMessage = 'เกิดข้อผิดพลาดในการตั้งค่าไฟล์หลัก: ' . $e->getMessage();
        }
    }

    public function render()
    {
        return view('livewire.document-file-manager');
    }
}
