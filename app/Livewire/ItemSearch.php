<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;

class ItemSearch extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $searchTerm = '';
    public $searchType = 'all';
    public $categoryIds = [];
    public $itemTypeIds = [];
    public $materialIds = [];
    public $languageIds = [];
    public $scriptIds = [];
    public $yearFrom = '';
    public $yearTo = '';
    public $sortBy = 'newest';
    public $perPage = 12;
    public $viewMode = 'grid';

    protected $queryString = [
        'searchTerm' => ['except' => ''],
        'searchType' => ['except' => 'all'],
        'categoryIds' => ['except' => []],
        'itemTypeIds' => ['except' => []],
        'materialIds' => ['except' => []],
        'languageIds' => ['except' => []],
        'scriptIds' => ['except' => []],
        'yearFrom' => ['except' => ''],
        'yearTo' => ['except' => ''],
        'sortBy' => ['except' => 'newest'],
        'perPage' => ['except' => 12],
        'viewMode' => ['except' => 'grid'],
    ];

    public function mount($searchTerm = '')
    {
        $this->searchTerm = $searchTerm;
    }

    public function updated($property)
    {
        if (in_array($property, [
            'searchTerm', 'searchType', 'categoryIds', 'itemTypeIds', 'materialIds',
            'languageIds', 'scriptIds', 'yearFrom', 'yearTo', 'sortBy', 'perPage'
        ])) {
            $this->resetPage();
        }
    }

    public function resetFilters()
    {
        $this->reset([
            'searchType', 'categoryIds', 'itemTypeIds', 'materialIds',
            'languageIds', 'scriptIds', 'yearFrom', 'yearTo'
        ]);
        $this->resetPage();
    }

    public function setViewMode($mode)
    {
        $this->viewMode = $mode;
    }

    public function setPerPage($perPage)
    {
        $this->perPage = $perPage;
        $this->resetPage();
    }

    public function setSortBy($sortBy)
    {
        $this->sortBy = $sortBy;
        $this->resetPage();
    }

    public function render()
    {
        $query = Item::with(['category', 'images', 'itemType', 'material', 'language', 'script']);

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                // Search in title by default
                $q->where('title', 'like', "%{$this->searchTerm}%");

                // If search type is 'all', search in other fields too
                if ($this->searchType === 'all' || $this->searchType === 'description') {
                    $q->orWhere('description', 'like', "%{$this->searchTerm}%");
                }

                if ($this->searchType === 'all' || $this->searchType === 'identifier') {
                    $q->orWhere('identifier_no', 'like', "%{$this->searchTerm}%");
                }

                if ($this->searchType === 'all') {
                    $q->orWhere('other_title1', 'like', "%{$this->searchTerm}%")
                      ->orWhere('other_title2', 'like', "%{$this->searchTerm}%")
                      ->orWhere('other_title3', 'like', "%{$this->searchTerm}%");
                }
            });
        }

        // Apply category filter
        if (!empty($this->categoryIds)) {
            $query->whereIn('category_id', $this->categoryIds);
        }

        // Apply item type filter
        if (!empty($this->itemTypeIds)) {
            $query->whereIn('item_type_id', $this->itemTypeIds);
        }

        // Apply material filter
        if (!empty($this->materialIds)) {
            $query->whereIn('material_id', $this->materialIds);
        }

        // Apply language filter
        if (!empty($this->languageIds)) {
            $query->whereIn('language_id', $this->languageIds);
        }

        // Apply script filter
        if (!empty($this->scriptIds)) {
            $query->whereIn('script_id', $this->scriptIds);
        }

        // Apply year range filter
        if ($this->yearFrom) {
            $query->where('year', '>=', $this->yearFrom);
        }

        if ($this->yearTo) {
            $query->where('year', '<=', $this->yearTo);
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'a-z':
                $query->orderBy('title', 'asc');
                break;
            case 'z-a':
                $query->orderBy('title', 'desc');
                break;
            case 'year-asc':
                $query->orderBy('year', 'asc');
                break;
            case 'year-desc':
                $query->orderBy('year', 'desc');
                break;
            case 'views':
                $query->orderBy('views', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Get items with pagination
        $items = $query->paginate($this->perPage);

        // Get filter options
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();

        return view('livewire.item-search', [
            'items' => $items,
            'categories' => $categories,
            'itemTypes' => $itemTypes,
            'materials' => $materials,
            'languages' => $languages,
            'scripts' => $scripts,
            'totalResults' => $items->total()
        ]);
    }
}
