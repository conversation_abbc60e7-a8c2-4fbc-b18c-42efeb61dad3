<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;

class SearchItems extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Search parameters
    public $searchTerm = '';
    public $searchType = 'all';
    public $categoryIds = [];
    public $itemTypeIds = [];
    public $materialIds = [];
    public $languageIds = [];
    public $scriptIds = [];
    public $countryIds = []; // เพิ่มการกรองตามประเทศ
    public $provinceCodes = []; // เพิ่มการกรองตามจังหวัด
    public $yearFrom = '';
    public $yearTo = '';
    public $sortBy = 'newest';

    // For tracking if search is being performed
    public $isSearching = false;

    // Querystring parameters
    protected $queryString = [
        'searchTerm' => ['except' => ''],
        'searchType' => ['except' => 'all'],
        'categoryIds' => ['except' => []],
        'itemTypeIds' => ['except' => []],
        'materialIds' => ['except' => []],
        'languageIds' => ['except' => []],
        'scriptIds' => ['except' => []],
        'countryIds' => ['except' => []],
        'provinceCodes' => ['except' => []],
        'yearFrom' => ['except' => ''],
        'yearTo' => ['except' => ''],
        'sortBy' => ['except' => 'newest'],
    ];

    public function mount($searchTerm = null)
    {
        // รับค่าคำค้นหาจากพารามิเตอร์ (ถ้ามี)
        if ($searchTerm) {
            $this->searchTerm = $searchTerm;
        }

        // Check if any search parameters are set
        $this->isSearching = $this->searchTerm ||
                            !empty($this->categoryIds) ||
                            !empty($this->itemTypeIds) ||
                            !empty($this->materialIds) ||
                            !empty($this->languageIds) ||
                            !empty($this->scriptIds) ||
                            !empty($this->countryIds) ||
                            !empty($this->provinceCodes) ||
                            $this->yearFrom ||
                            $this->yearTo;
    }

    public function updated($property)
    {
        if (in_array($property, [
            'searchTerm', 'searchType',
            'categoryIds', 'itemTypeIds', 'materialIds', 'languageIds', 'scriptIds',
            'countryIds', 'provinceCodes', 'yearFrom', 'yearTo', 'sortBy'
        ])) {
            $this->resetPage();
            $this->isSearching = $this->searchTerm ||
                                !empty($this->categoryIds) ||
                                !empty($this->itemTypeIds) ||
                                !empty($this->materialIds) ||
                                !empty($this->languageIds) ||
                                !empty($this->scriptIds) ||
                                !empty($this->countryIds) ||
                                !empty($this->provinceCodes) ||
                                $this->yearFrom ||
                                $this->yearTo;
        }
    }

    public function resetFilters()
    {
        $this->reset([
            'searchTerm', 'searchType',
            'categoryIds', 'itemTypeIds', 'materialIds', 'languageIds', 'scriptIds',
            'countryIds', 'provinceCodes', 'yearFrom', 'yearTo', 'sortBy'
        ]);
        $this->isSearching = false;
        $this->resetPage();
    }

    public function render()
    {
        // Get filter options
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $countries = \App\Models\Country::orderBy('name')->get();
        $provinces = \App\Models\Province::orderBy('name_th')->get();

        // Initialize query
        $query = Item::query();

        // Apply search term filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                switch ($this->searchType) {
                    case 'title':
                        $q->where('title', 'like', "%{$this->searchTerm}%");
                        break;
                    case 'identifier':
                        $q->where('identifier_no', 'like', "%{$this->searchTerm}%");
                        break;
                    case 'all':
                    default:
                        $q->where('title', 'like', "%{$this->searchTerm}%")
                          ->orWhere('description', 'like', "%{$this->searchTerm}%")
                          ->orWhere('identifier_no', 'like', "%{$this->searchTerm}%")
                          ->orWhere('other_title1', 'like', "%{$this->searchTerm}%")
                          ->orWhere('other_title2', 'like', "%{$this->searchTerm}%")
                          ->orWhere('other_title3', 'like', "%{$this->searchTerm}%");
                        break;
                }
            });
        }

        // Apply category filter
        if (!empty($this->categoryIds)) {
            $query->whereIn('category_id', $this->categoryIds);
        }

        // Apply item type filter
        if (!empty($this->itemTypeIds)) {
            $query->whereIn('item_type_id', $this->itemTypeIds);
        }

        // Apply material filter
        if (!empty($this->materialIds)) {
            $query->whereIn('material_id', $this->materialIds);
        }

        // Apply language filter
        if (!empty($this->languageIds)) {
            $query->whereIn('language_id', $this->languageIds);
        }

        // Apply script filter
        if (!empty($this->scriptIds)) {
            $query->whereIn('script_id', $this->scriptIds);
        }

        // Apply country filter
        if (!empty($this->countryIds)) {
            $query->whereIn('country', $this->countryIds);
        }

        // Apply province filter
        if (!empty($this->provinceCodes)) {
            $query->whereIn('province', $this->provinceCodes);
        }

        // Apply year range filter
        if ($this->yearFrom) {
            $query->where('year', '>=', $this->yearFrom);
        }

        if ($this->yearTo) {
            $query->where('year', '<=', $this->yearTo);
        }

        // Apply sorting
        switch ($this->sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'a-z':
                $query->orderBy('title', 'asc');
                break;
            case 'z-a':
                $query->orderBy('title', 'desc');
                break;
            case 'year-asc':
                $query->orderBy('year', 'asc');
                break;
            case 'year-desc':
                $query->orderBy('year', 'desc');
                break;
            case 'views':
                $query->orderBy('views', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Get results with pagination
        $items = $query->with(['category', 'itemType', 'material', 'language', 'script'])->paginate(12);
        $totalResults = $items->total();

        return view('livewire.search-items', [
            'items' => $items,
            'categories' => $categories,
            'itemTypes' => $itemTypes,
            'materials' => $materials,
            'languages' => $languages,
            'scripts' => $scripts,
            'countries' => $countries,
            'provinces' => $provinces,
            'totalResults' => $totalResults
        ]);
    }
}
