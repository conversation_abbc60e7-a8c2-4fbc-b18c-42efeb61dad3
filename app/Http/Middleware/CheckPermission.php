<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $permission): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'กรุณาเข้าสู่ระบบก่อนเข้าถึงส่วนนี้');
        }

        if (!Auth::user()->hasPermissionTo($permission)) {
            // Instead of redirecting to home, show an error message on the current page
            return response()->view('errors.permission', [
                'permission' => $permission
            ], 403);
        }

        return $next($request);
    }
}
