<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // ตรวจสอบว่าโหมดบำรุงรักษาเปิดอยู่หรือไม่
        $maintenanceMode = Setting::get('maintenance_mode', 'false');
        
        // ถ้าโหมดบำรุงรักษาเปิดอยู่
        if ($maintenanceMode === 'true') {
            // ตรวจสอบว่าเป็นเส้นทางที่ยกเว้นหรือไม่
            $path = $request->path();
            
            // ยกเว้นหน้าแอดมินและหน้าล็อกอิน
            $exceptPaths = [
                'admin', 'admin/*', 'login', 'logout',
                'css/*', 'js/*', 'images/*', 'fonts/*', 'storage/*'
            ];
            
            // ตรวจสอบว่าเป็นเส้นทางที่ยกเว้นหรือไม่
            $isExceptPath = false;
            foreach ($exceptPaths as $exceptPath) {
                if (fnmatch($exceptPath, $path)) {
                    $isExceptPath = true;
                    break;
                }
            }
            
            // ตรวจสอบว่าผู้ใช้ล็อกอินหรือไม่
            $isLoggedIn = Auth::check();
            
            // ถ้าไม่ใช่เส้นทางที่ยกเว้นและไม่ได้ล็อกอิน ให้แสดงหน้าบำรุงรักษา
            if (!$isExceptPath && !$isLoggedIn) {
                return response()->view('errors.maintenance', [
                    'message' => Setting::get('maintenance_message', 'ระบบกำลังอยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่ในภายหลัง')
                ], 503);
            }
        }
        
        return $next($request);
    }
}
