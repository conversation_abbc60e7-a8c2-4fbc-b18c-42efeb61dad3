<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class CustomMaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // ตรวจสอบว่าโหมดบำรุงรักษาเปิดอยู่หรือไม่
        try {
            $maintenanceMode = Setting::where('key', 'maintenance_mode')->first();
            $isMaintenanceMode = $maintenanceMode && $maintenanceMode->value === 'true';
            
            // บันทึก Log เพื่อตรวจสอบ
            Log::info('Maintenance mode check: ' . ($isMaintenanceMode ? 'ON' : 'OFF'));
            Log::info('Current path: ' . $request->path());
            
            // ถ้าโหมดบำรุงรักษาเปิดอยู่
            if ($isMaintenanceMode) {
                // ตรวจสอบว่าเป็นเส้นทางที่ยกเว้นหรือไม่
                $path = $request->path();
                
                // ยกเว้นหน้าแอดมินและหน้าล็อกอิน
                if (str_starts_with($path, 'admin') || $path === 'login' || $path === 'logout') {
                    Log::info('Path is exempt from maintenance mode: ' . $path);
                    return $next($request);
                }
                
                // ยกเว้นไฟล์ static
                if (str_starts_with($path, 'css/') || 
                    str_starts_with($path, 'js/') || 
                    str_starts_with($path, 'images/') || 
                    str_starts_with($path, 'fonts/') || 
                    str_starts_with($path, 'storage/')) {
                    Log::info('Static file is exempt from maintenance mode: ' . $path);
                    return $next($request);
                }
                
                // แสดงหน้าบำรุงรักษา
                Log::info('Showing maintenance page for path: ' . $path);
                
                // ดึงข้อความบำรุงรักษา
                $maintenanceMessage = Setting::where('key', 'maintenance_message')->first();
                $message = $maintenanceMessage ? $maintenanceMessage->value : 'ระบบกำลังอยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่ในภายหลัง';
                
                return response()->view('errors.maintenance', ['message' => $message], 503);
            }
        } catch (\Exception $e) {
            Log::error('Error in maintenance mode middleware: ' . $e->getMessage());
        }
        
        return $next($request);
    }
}
