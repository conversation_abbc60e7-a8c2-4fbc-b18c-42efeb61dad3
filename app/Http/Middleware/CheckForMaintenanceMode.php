<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance as Middleware;
use Illuminate\Support\Facades\Log;

class CheckForMaintenanceMode extends Middleware
{
    /**
     * The URIs that should be reachable while maintenance mode is enabled.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/admin*',
        '/login',
        '/logout',
        '/storage/*',
        '/css/*',
        '/js/*',
        '/images/*',
        '/fonts/*',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // Log the current request path for debugging
        if ($this->app->maintenanceMode()->active()) {
            Log::info('Maintenance mode active, checking path: ' . $request->path());
            Log::info('Is path in except array: ' . ($this->inExceptArray($request) ? 'Yes' : 'No'));
        }

        return parent::handle($request, $next);
    }
}
