<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Item;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories
     */
    public function index()
    {
        $categories = Category::all();
        return view('categories.index', compact('categories'));
    }

    /**
     * Display the specified category with its items
     */
    public function show($id)
    {
        $category = Category::findOrFail($id);
        $items = Item::with(['itemType'])->where('category_id', $id)->latest()->get();
        return view('categories.show', compact('category', 'items'));
    }
}
