<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Province;
use App\Models\Language;
use App\Models\Script;
use App\Models\Country;

class MapController extends Controller
{
    /**
     * Display the map page with all items that have coordinates
     */
    public function index()
    {
        // Get only items with latitude and longitude coordinates
        $items = Item::whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->with(['category', 'itemType', 'provinceRelation', 'countryRelation', 'language', 'script'])
            ->get();

        // Add province name and country to each item
        $items->each(function ($item) {
            $item->province_name = $item->getProvinceNameAttribute();

            // ถ้าไม่มีข้อมูลประเทศ ให้กำหนดค่าเริ่มต้น
            if (!$item->country) {
                if ($item->province === 'MM') {
                    $item->country = 'MM';
                } else if ($item->province) {
                    $item->country = 'TH';
                }
            }
        });

        // Get filter options for the sidebar
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $provinces = Province::orderBy('name_th')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $countries = Country::orderBy('name')->get();

        return view('maps.index', compact(
            'items',
            'categories',
            'itemTypes',
            'provinces',
            'languages',
            'scripts',
            'countries'
        ));
    }
}
