<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;
use App\Models\User;
use App\Models\Country;
use App\Models\Province;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Models\Setting;
use App\Models\ItemImage;
use App\Models\ItemFile;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Middleware is defined in routes file
    }

    /**
     * แสดงหน้าแดชบอร์ด
     */
    public function dashboard()
    {
        // สถิติสำหรับแดชบอร์ด
        $stats = [
            'total_documents' => Item::count(),
            'total_categories' => Category::count(),
            'total_document_types' => ItemType::count(),
            'total_materials' => Material::count(),
            'total_languages' => Language::count(),
            'total_scripts' => Script::count(),
            'total_users' => User::count(),
            'total_views' => Item::sum('views'),
        ];

        // เอกสารล่าสุด
        $recentDocuments = Item::with(['category', 'itemType'])
            ->latest()
            ->take(5)
            ->get();

        // ข้อมูลสำหรับกราฟ
        $documentsByCategory = Category::withCount('items')
            ->orderBy('items_count', 'desc')
            ->take(10)
            ->get();

        $documentsByType = ItemType::withCount('items')
            ->orderBy('items_count', 'desc')
            ->take(10)
            ->get();

        $documentsByYear = Item::select(DB::raw('year, count(*) as count'))
            ->whereNotNull('year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->take(10)
            ->get();

        $documentsByLanguage = Language::withCount('items')
            ->orderBy('items_count', 'desc')
            ->take(10)
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentDocuments',
            'documentsByCategory',
            'documentsByType',
            'documentsByYear',
            'documentsByLanguage'
        ));
    }

    /**
     * แสดงรายการทั้งหมด
     */
    public function items(Request $request)
    {
        $query = Item::with(['category', 'itemType', 'language']);

        // ค้นหา
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('identifier_no', 'like', "%{$search}%");
            });
        }

        $items = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.items.index', compact('items'));
    }

    /**
     * แสดงหน้าเพิ่มรายการ
     */
    public function createItem()
    {
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $countries = Country::orderBy('name')->get();
        $provinces = Province::orderBy('name_th')->get();

        return view('admin.items.create', compact(
            'categories',
            'itemTypes',
            'materials',
            'languages',
            'scripts',
            'countries',
            'provinces'
        ));
    }

    /**
     * บันทึกรายการใหม่
     */
    public function storeItem(Request $request)
    {
        // Log the request data for debugging
        Log::info('AdminController::storeItem - Request data:', [
            'all' => $request->all(),
            'has_uploaded_images' => $request->has('uploaded_images'),
            'has_uploaded_files' => $request->has('uploaded_files'),
            'uploaded_images' => $request->uploaded_images,
            'uploaded_files' => $request->uploaded_files
        ]);

        // Dump the raw request data to a file for debugging
        file_put_contents(storage_path('logs/admin_store_item_raw_request.log'),
            date('Y-m-d H:i:s') . " - Raw request data\n" .
            "Content type: " . $request->header('Content-Type') . "\n" .
            "Request method: " . $request->method() . "\n" .
            "Request URI: " . $request->getRequestUri() . "\n" .
            "Request data: " . json_encode($request->all(), JSON_PRETTY_PRINT) . "\n" .
            "Request files: " . json_encode($request->allFiles(), JSON_PRETTY_PRINT) . "\n\n",
            FILE_APPEND);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'year' => 'nullable|string|max:10',
            'item_type_id' => 'nullable|exists:item_types,id',
            'category_id' => 'nullable|exists:categories,id',
            'identifier_no' => 'nullable|string|max:100|unique:items,identifier_no',
            'material_id' => 'nullable|exists:materials,id',
            'language_id' => 'nullable|exists:languages,id',
            'script_id' => 'nullable|exists:scripts,id',
        ]);

        // Check for session data
        $sessionTempImages = session('temp_images', []);
        $sessionTempFiles = session('temp_files', []);

        Log::info('AdminController::storeItem - Session data:', [
            'session_temp_images_count' => count($sessionTempImages),
            'session_temp_files_count' => count($sessionTempFiles)
        ]);

        // If we have session data but no request data, use the session data
        if (count($sessionTempImages) > 0 && !$request->has('uploaded_images')) {
            Log::info('AdminController::storeItem - Using session temp images');
            $request->merge(['uploaded_images' => json_encode($sessionTempImages)]);
        }

        if (count($sessionTempFiles) > 0 && !$request->has('uploaded_files')) {
            Log::info('AdminController::storeItem - Using session temp files');
            $request->merge(['uploaded_files' => json_encode($sessionTempFiles)]);
        }

        file_put_contents(storage_path('logs/admin_store_item_debug.log'),
            date('Y-m-d H:i:s') . " - Session data\n" .
            "Session temp images count: " . count($sessionTempImages) . "\n" .
            "Session temp files count: " . count($sessionTempFiles) . "\n" .
            "Session temp images: " . json_encode($sessionTempImages, JSON_PRETTY_PRINT) . "\n" .
            "Session temp files: " . json_encode($sessionTempFiles, JSON_PRETTY_PRINT) . "\n\n",
            FILE_APPEND);

        $item = Item::create($request->all());

        Log::info('AdminController::storeItem - Item created:', [
            'item_id' => $item->id,
            'title' => $item->title
        ]);

        // Check for Livewire components directly
        try {
            // ใช้ session แทนการเข้าถึง Livewire components โดยตรง
            // เนื่องจาก Livewire API อาจแตกต่างกันในแต่ละเวอร์ชัน

            Log::info('AdminController::storeItem - Checking for Livewire data in session');

            file_put_contents(storage_path('logs/admin_store_item_debug.log'),
                date('Y-m-d H:i:s') . " - Checking for Livewire data in session\n",
                FILE_APPEND);

            // ใช้ข้อมูลจาก session ที่เราได้บันทึกไว้แล้วในขั้นตอนการอัพโหลด
            $sessionTempImages = session('temp_images', []);
            $sessionTempFiles = session('temp_files', []);

            // บันทึกข้อมูลลง log
            Log::info('AdminController::storeItem - Session data for Livewire components', [
                'temp_images_count' => count($sessionTempImages),
                'temp_files_count' => count($sessionTempFiles)
            ]);

            file_put_contents(storage_path('logs/admin_store_item_debug.log'),
                date('Y-m-d H:i:s') . " - Session data for Livewire components\n" .
                "Temp images count: " . count($sessionTempImages) . "\n" .
                "Temp files count: " . count($sessionTempFiles) . "\n\n",
                FILE_APPEND);

            // ถ้ามีข้อมูลรูปภาพใน session ให้ใช้ข้อมูลนั้น
            if (!empty($sessionTempImages)) {
                Log::info('AdminController::storeItem - Using temp images from session', [
                    'count' => count($sessionTempImages)
                ]);

                file_put_contents(storage_path('logs/admin_store_item_debug.log'),
                    date('Y-m-d H:i:s') . " - Using temp images from session\n" .
                    "Images: " . json_encode($sessionTempImages, JSON_PRETTY_PRINT) . "\n\n",
                    FILE_APPEND);

                // ถ้ายังไม่มีข้อมูลใน request ให้เพิ่มเข้าไป
                if (!$request->has('uploaded_images') || empty($request->uploaded_images)) {
                    $request->merge(['uploaded_images' => json_encode($sessionTempImages)]);
                    Log::info('AdminController::storeItem - Added temp images to request');
                }
            }

            // ถ้ามีข้อมูลไฟล์ใน session ให้ใช้ข้อมูลนั้น
            if (!empty($sessionTempFiles)) {
                Log::info('AdminController::storeItem - Using temp files from session', [
                    'count' => count($sessionTempFiles)
                ]);

                file_put_contents(storage_path('logs/admin_store_item_debug.log'),
                    date('Y-m-d H:i:s') . " - Using temp files from session\n" .
                    "Files: " . json_encode($sessionTempFiles, JSON_PRETTY_PRINT) . "\n\n",
                    FILE_APPEND);

                // ถ้ายังไม่มีข้อมูลใน request ให้เพิ่มเข้าไป
                if (!$request->has('uploaded_files') || empty($request->uploaded_files)) {
                    $request->merge(['uploaded_files' => json_encode($sessionTempFiles)]);
                    Log::info('AdminController::storeItem - Added temp files to request');
                }
            }
        } catch (\Exception $e) {
            Log::error('AdminController::storeItem - Error accessing session data: ' . $e->getMessage());
        }

        // ตรวจสอบว่ามีการบันทึกรูปภาพไปแล้วหรือไม่
        $processedImages = [];

        // Process uploaded images if available
        if ($request->has('uploaded_images')) {
            try {
                $uploadedImages = json_decode($request->uploaded_images, true);
                if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                    Log::info('AdminController::storeItem - Processing uploaded images', [
                        'images_count' => count($uploadedImages)
                    ]);

                    // ใช้ array เพื่อเก็บ path ของรูปที่บันทึกไปแล้ว ป้องกันการบันทึกซ้ำ
                    foreach ($uploadedImages as $index => $imageData) {
                        try {
                            // ตรวจสอบว่ารูปนี้ถูกบันทึกไปแล้วหรือไม่
                            $imagePath = $imageData['path'] ?? '';
                            if (empty($imagePath)) {
                                Log::warning('AdminController::storeItem - Empty image path, skipping');
                                continue;
                            }

                            // ตรวจสอบว่าเคยบันทึกรูปนี้ไปแล้วหรือไม่ในรอบนี้
                            if (in_array($imagePath, $processedImages)) {
                                Log::warning('AdminController::storeItem - Duplicate image path found, skipping: ' . $imagePath);
                                continue;
                            }

                            // ตรวจสอบว่ามีรูปนี้ในฐานข้อมูลแล้วหรือไม่
                            $existingImage = ItemImage::where('item_id', $item->id)
                                ->where('image_path', $imagePath)
                                ->first();

                            if ($existingImage) {
                                Log::info('AdminController::storeItem - Image already exists in database, skipping', [
                                    'image_id' => $existingImage->id,
                                    'image_path' => $existingImage->image_path
                                ]);

                                // เพิ่มเข้าไปใน processedImages เพื่อไม่ให้บันทึกซ้ำอีก
                                $processedImages[] = $imagePath;
                                continue;
                            }

                            // บันทึกรูปภาพใหม่ - เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                            $itemImage = new ItemImage();
                            $itemImage->item_id = $item->id;

                            // เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                            $fileName = basename($imagePath);
                            $itemImage->image_path = $fileName;

                            $itemImage->image_name = $imageData['name'] ?? $fileName;
                            $itemImage->image_type = $imageData['type'] ?? '';
                            $itemImage->sort_order = $index;
                            $itemImage->is_main = ($index === 0);
                            $itemImage->save();

                            // เพิ่มเข้าไปใน processedImages เพื่อไม่ให้บันทึกซ้ำอีก
                            $processedImages[] = $imagePath;

                            Log::info('AdminController::storeItem - Saved image', [
                                'image_id' => $itemImage->id,
                                'image_path' => $itemImage->image_path
                            ]);
                        } catch (\Exception $e) {
                            Log::error('AdminController::storeItem - Error saving image: ' . $e->getMessage());
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('AdminController::storeItem - Error processing uploaded images: ' . $e->getMessage());
            }
        }

        // ตรวจสอบว่ามีการบันทึกไฟล์ไปแล้วหรือไม่
        $processedFiles = [];

        // Process uploaded files if available
        if ($request->has('uploaded_files')) {
            try {
                $uploadedFiles = json_decode($request->uploaded_files, true);
                if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                    Log::info('AdminController::storeItem - Processing uploaded files', [
                        'files_count' => count($uploadedFiles)
                    ]);

                    // ใช้ array เพื่อเก็บ path ของไฟล์ที่บันทึกไปแล้ว ป้องกันการบันทึกซ้ำ
                    foreach ($uploadedFiles as $index => $fileData) {
                        try {
                            // ตรวจสอบว่าไฟล์นี้ถูกบันทึกไปแล้วหรือไม่
                            $filePath = $fileData['path'] ?? '';
                            if (empty($filePath)) {
                                Log::warning('AdminController::storeItem - Empty file path, skipping');
                                continue;
                            }

                            // ตรวจสอบว่าเคยบันทึกไฟล์นี้ไปแล้วหรือไม่ในรอบนี้
                            if (in_array($filePath, $processedFiles)) {
                                Log::warning('AdminController::storeItem - Duplicate file path found, skipping: ' . $filePath);
                                continue;
                            }

                            // ตรวจสอบว่ามีไฟล์นี้ในฐานข้อมูลแล้วหรือไม่
                            $existingFile = ItemFile::where('item_id', $item->id)
                                ->where('file_path', $filePath)
                                ->first();

                            if ($existingFile) {
                                Log::info('AdminController::storeItem - File already exists in database, skipping', [
                                    'file_id' => $existingFile->id,
                                    'file_path' => $existingFile->file_path
                                ]);

                                // เพิ่มเข้าไปใน processedFiles เพื่อไม่ให้บันทึกซ้ำอีก
                                $processedFiles[] = $filePath;
                                continue;
                            }

                            // บันทึกไฟล์ใหม่ - เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                            $itemFile = new ItemFile();
                            $itemFile->item_id = $item->id;

                            // เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                            $fileName = basename($filePath);
                            $itemFile->file_path = $fileName;

                            $itemFile->file_name = $fileData['name'] ?? $fileName;
                            $itemFile->file_type = $fileData['type'] ?? '';
                            $itemFile->file_extension = $fileData['extension'] ?? '';
                            $itemFile->file_size = $fileData['size'] ?? 0;
                            $itemFile->sort_order = $index;
                            $itemFile->is_main = ($index === 0);
                            $itemFile->save();

                            // เพิ่มเข้าไปใน processedFiles เพื่อไม่ให้บันทึกซ้ำอีก
                            $processedFiles[] = $filePath;

                            Log::info('AdminController::storeItem - Saved file', [
                                'file_id' => $itemFile->id,
                                'file_path' => $itemFile->file_path
                            ]);
                        } catch (\Exception $e) {
                            Log::error('AdminController::storeItem - Error saving file: ' . $e->getMessage());
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('AdminController::storeItem - Error processing uploaded files: ' . $e->getMessage());
            }
        }

        // Process session temp images if available
        if (count($sessionTempImages) > 0) {
            Log::info('AdminController::storeItem - Processing session temp images', [
                'images_count' => count($sessionTempImages)
            ]);

            foreach ($sessionTempImages as $index => $imageData) {
                try {
                    // ตรวจสอบว่ารูปนี้ถูกบันทึกไปแล้วหรือไม่
                    $imagePath = $imageData['path'] ?? $imageData['image_path'] ?? '';
                    if (empty($imagePath)) {
                        Log::warning('AdminController::storeItem - Empty image path in session, skipping');
                        continue;
                    }

                    // ตรวจสอบว่าเคยบันทึกรูปนี้ไปแล้วหรือไม่ในรอบนี้
                    if (in_array($imagePath, $processedImages)) {
                        Log::warning('AdminController::storeItem - Duplicate image path found in session, skipping: ' . $imagePath);
                        continue;
                    }

                    // ตรวจสอบว่ามีรูปนี้ในฐานข้อมูลแล้วหรือไม่
                    $existingImage = ItemImage::where('item_id', $item->id)
                        ->where('image_path', $imagePath)
                        ->first();

                    if ($existingImage) {
                        Log::info('AdminController::storeItem - Session image already exists in database, skipping', [
                            'image_id' => $existingImage->id,
                            'image_path' => $existingImage->image_path
                        ]);

                        // เพิ่มเข้าไปใน processedImages เพื่อไม่ให้บันทึกซ้ำอีก
                        $processedImages[] = $imagePath;
                        continue;
                    }

                    // บันทึกรูปภาพใหม่ - เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                    $itemImage = new ItemImage();
                    $itemImage->item_id = $item->id;

                    // เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                    $fileName = basename($imagePath);
                    $itemImage->image_path = $fileName;

                    $itemImage->image_name = $imageData['name'] ?? $imageData['image_name'] ?? $fileName;
                    $itemImage->image_type = $imageData['type'] ?? $imageData['image_type'] ?? '';
                    $itemImage->sort_order = $index;
                    $itemImage->is_main = ($index === 0);
                    $itemImage->save();

                    // เพิ่มเข้าไปใน processedImages เพื่อไม่ให้บันทึกซ้ำอีก
                    $processedImages[] = $imagePath;

                    Log::info('AdminController::storeItem - Saved session temp image', [
                        'image_id' => $itemImage->id,
                        'image_path' => $itemImage->image_path
                    ]);
                } catch (\Exception $e) {
                    Log::error('AdminController::storeItem - Error saving session temp image: ' . $e->getMessage());
                }
            }

            // Clear session temp images
            session(['temp_images' => []]);
        }

        // Process session temp files if available
        if (count($sessionTempFiles) > 0) {
            Log::info('AdminController::storeItem - Processing session temp files', [
                'files_count' => count($sessionTempFiles)
            ]);

            foreach ($sessionTempFiles as $index => $fileData) {
                try {
                    // ตรวจสอบว่าไฟล์นี้ถูกบันทึกไปแล้วหรือไม่
                    $filePath = $fileData['path'] ?? $fileData['file_path'] ?? '';
                    if (empty($filePath)) {
                        Log::warning('AdminController::storeItem - Empty file path in session, skipping');
                        continue;
                    }

                    // ตรวจสอบว่าเคยบันทึกไฟล์นี้ไปแล้วหรือไม่ในรอบนี้
                    if (in_array($filePath, $processedFiles)) {
                        Log::warning('AdminController::storeItem - Duplicate file path found in session, skipping: ' . $filePath);
                        continue;
                    }

                    // ตรวจสอบว่ามีไฟล์นี้ในฐานข้อมูลแล้วหรือไม่
                    $existingFile = ItemFile::where('item_id', $item->id)
                        ->where('file_path', $filePath)
                        ->first();

                    if ($existingFile) {
                        Log::info('AdminController::storeItem - Session file already exists in database, skipping', [
                            'file_id' => $existingFile->id,
                            'file_path' => $existingFile->file_path
                        ]);

                        // เพิ่มเข้าไปใน processedFiles เพื่อไม่ให้บันทึกซ้ำอีก
                        $processedFiles[] = $filePath;
                        continue;
                    }

                    // บันทึกไฟล์ใหม่ - เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                    $itemFile = new ItemFile();
                    $itemFile->item_id = $item->id;

                    // เก็บเฉพาะชื่อไฟล์ ไม่เก็บ path เต็ม
                    $fileName = basename($filePath);
                    $itemFile->file_path = $fileName;

                    $itemFile->file_name = $fileData['name'] ?? $fileData['file_name'] ?? $fileName;
                    $itemFile->file_type = $fileData['type'] ?? $fileData['file_type'] ?? '';
                    $itemFile->file_extension = $fileData['extension'] ?? $fileData['file_extension'] ?? '';
                    $itemFile->file_size = $fileData['size'] ?? $fileData['file_size'] ?? 0;
                    $itemFile->sort_order = $index;
                    $itemFile->is_main = ($index === 0);
                    $itemFile->save();

                    // เพิ่มเข้าไปใน processedFiles เพื่อไม่ให้บันทึกซ้ำอีก
                    $processedFiles[] = $filePath;

                    Log::info('AdminController::storeItem - Saved session temp file', [
                        'file_id' => $itemFile->id,
                        'file_path' => $itemFile->file_path
                    ]);
                } catch (\Exception $e) {
                    Log::error('AdminController::storeItem - Error saving session temp file: ' . $e->getMessage());
                }
            }

            // Clear session temp files
            session(['temp_files' => []]);
        }

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * แสดงหน้าแก้ไขรายการ
     */
    public function editItem($id)
    {
        $item = Item::findOrFail($id);

        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $countries = Country::orderBy('name')->get();
        $provinces = Province::orderBy('name_th')->get();

        // เตรียมข้อมูลไฟล์รายการ
        $itemFiles = [
            'pdf' => [],
            'image' => [],
            'audio' => [],
            'video' => [],
            'other' => [],
        ];

        return view('admin.items.edit', compact(
            'item',
            'categories',
            'itemTypes',
            'materials',
            'languages',
            'scripts',
            'countries',
            'provinces',
            'itemFiles'
        ));
    }

    /**
     * อัปเดตรายการ
     */
    public function updateItem(Request $request, $id)
    {
        // Log all request data for debugging
        Log::info('AdminController::updateItem - Request data:', [
            'all' => $request->all(),
            'has_uploaded_images' => $request->has('uploaded_images'),
            'has_uploaded_files' => $request->has('uploaded_files'),
            'uploaded_images' => $request->uploaded_images,
            'uploaded_files' => $request->uploaded_files
        ]);

        $item = Item::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'year' => 'nullable|string|max:10',
            'item_type_id' => 'nullable|exists:item_types,id',
            'category_id' => 'nullable|exists:categories,id',
            'identifier_no' => 'nullable|string|max:100|unique:items,identifier_no,'.$id,
            'material_id' => 'nullable|exists:materials,id',
            'language_id' => 'nullable|exists:languages,id',
            'script_id' => 'nullable|exists:scripts,id',
            'creator' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'contributor' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:10',
            'province' => 'nullable|string|max:10',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'remark' => 'nullable|string',
            'brief' => 'nullable|string',
            'manuscript_condition' => 'nullable|string',
            'quantity' => 'nullable|integer|min:1',
            'other_title1' => 'nullable|string|max:255',
            'other_title2' => 'nullable|string|max:255',
            'other_title3' => 'nullable|string|max:255',
        ]);

        // Get all request data
        $itemData = $request->all();

        // Remove uploaded_images and uploaded_files from itemData to prevent storing JSON in the item record
        $uploadedImagesBackup = $itemData['uploaded_images'] ?? null;
        $uploadedFilesBackup = $itemData['uploaded_files'] ?? null;

        if (isset($itemData['uploaded_images'])) {
            unset($itemData['uploaded_images']);
            Log::info('Removed uploaded_images from itemData before updating item');
        }

        if (isset($itemData['uploaded_files'])) {
            unset($itemData['uploaded_files']);
            Log::info('Removed uploaded_files from itemData before updating item');
        }

        // Remove CSRF token and method from data
        unset($itemData['_token'], $itemData['_method']);

        try {
            $item->update($itemData);
            Log::info('Updated item successfully', [
                'item_id' => $item->id,
                'title' => $item->title,
                'item_type_id' => $item->item_type_id,
                'category_id' => $item->category_id
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating item: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Re-throw to handle at higher level
        }

        // Process uploaded images if any
        if ($uploadedImagesBackup && !empty($uploadedImagesBackup)) {
            try {
                $uploadedImages = is_string($uploadedImagesBackup) ? json_decode($uploadedImagesBackup, true) : $uploadedImagesBackup;

                if (is_array($uploadedImages)) {
                    Log::info('Processing uploaded images for item update', [
                        'item_id' => $item->id,
                        'images_count' => count($uploadedImages)
                    ]);

                    foreach ($uploadedImages as $imageData) {
                        if (isset($imageData['path']) && !empty($imageData['path'])) {
                            // Check if this image already exists for this item
                            $existingImage = ItemImage::where('item_id', $item->id)
                                ->where('image_path', $imageData['path'])
                                ->first();

                            if (!$existingImage) {
                                try {
                                    $itemImage = ItemImage::create([
                                        'item_id' => $item->id,
                                        'image_path' => $imageData['path'],
                                        'original_name' => $imageData['original_name'] ?? '',
                                        'file_size' => $imageData['file_size'] ?? 0,
                                        'mime_type' => $imageData['mime_type'] ?? '',
                                        'sort_order' => $imageData['sort_order'] ?? 0,
                                        'is_main' => $imageData['is_main'] ?? false,
                                    ]);

                                    Log::info('Saved image for item update', [
                                        'image_id' => $itemImage->id,
                                        'item_id' => $item->id,
                                        'path' => $imageData['path']
                                    ]);
                                } catch (\Exception $e) {
                                    Log::error('Error saving individual image during update: ' . $e->getMessage());
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error processing uploaded images during update: ' . $e->getMessage());
            }
        }

        // Process uploaded files if any
        if ($uploadedFilesBackup && !empty($uploadedFilesBackup)) {
            try {
                $uploadedFiles = is_string($uploadedFilesBackup) ? json_decode($uploadedFilesBackup, true) : $uploadedFilesBackup;

                if (is_array($uploadedFiles)) {
                    Log::info('Processing uploaded files for item update', [
                        'item_id' => $item->id,
                        'files_count' => count($uploadedFiles)
                    ]);

                    foreach ($uploadedFiles as $fileData) {
                        if (isset($fileData['path']) && !empty($fileData['path'])) {
                            // Check if this file already exists for this item
                            $existingFile = ItemFile::where('item_id', $item->id)
                                ->where('file_path', $fileData['path'])
                                ->first();

                            if (!$existingFile) {
                                try {
                                    $itemFile = ItemFile::create([
                                        'item_id' => $item->id,
                                        'file_path' => $fileData['path'],
                                        'original_name' => $fileData['original_name'] ?? '',
                                        'file_size' => $fileData['file_size'] ?? 0,
                                        'mime_type' => $fileData['mime_type'] ?? '',
                                        'file_type' => $fileData['file_type'] ?? 'other',
                                        'sort_order' => $fileData['sort_order'] ?? 0,
                                        'is_main' => $fileData['is_main'] ?? false,
                                    ]);

                                    Log::info('Saved file for item update', [
                                        'file_id' => $itemFile->id,
                                        'item_id' => $item->id,
                                        'path' => $fileData['path']
                                    ]);
                                } catch (\Exception $e) {
                                    Log::error('Error saving individual file during update: ' . $e->getMessage());
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error processing uploaded files during update: ' . $e->getMessage());
            }
        }

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * ลบรายการ
     */
    public function destroyItem($id)
    {
        $item = Item::findOrFail($id);
        $item->delete();

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกลบเรียบร้อยแล้ว');
    }

    /**
     * แสดงรายการหมวดหมู่
     */
    public function categories()
    {
        $categories = Category::withCount('documents')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.categories.index', compact('categories'));
    }

    /**
     * แสดงหน้าเพิ่มหมวดหมู่
     */
    public function createCategory()
    {
        // Get all categories for parent selection
        $parentCategories = Category::orderBy('name')->get();

        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * บันทึกหมวดหมู่ใหม่
     */
    public function storeCategory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->except('image');

        // จัดการกับการอัพโหลดรูปภาพ
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();

            // เก็บไฟล์ในโฟลเดอร์ categories ภายใน storage/app/public
            $path = $image->storeAs('categories', $imageName, 'public');

            // บันทึกพาธของรูปภาพลงในฐานข้อมูล
            $data['image_path'] = $path;
        }

        Category::create($data);

        return redirect()->route('admin.categories.index')
            ->with('success', 'หมวดหมู่ถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * แสดงหน้าแก้ไขหมวดหมู่
     */
    public function editCategory($id)
    {
        $category = Category::findOrFail($id);

        // Get all categories except the current one for parent selection
        $parentCategories = Category::where('id', '!=', $id)
            ->orderBy('name')
            ->get();

        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * อัปเดตหมวดหมู่
     */
    public function updateCategory(Request $request, $id)
    {
        $category = Category::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->except('image');

        // จัดการกับการอัพโหลดรูปภาพ
        if ($request->hasFile('image')) {
            // ลบรูปภาพเก่า (ถ้ามี)
            if ($category->image_path && Storage::disk('public')->exists($category->image_path)) {
                Storage::disk('public')->delete($category->image_path);
            }

            $image = $request->file('image');
            $imageName = time() . '_' . $image->getClientOriginalName();

            // เก็บไฟล์ในโฟลเดอร์ categories ภายใน storage/app/public
            $path = $image->storeAs('categories', $imageName, 'public');

            // บันทึกพาธของรูปภาพลงในฐานข้อมูล
            $data['image_path'] = $path;
        }

        $category->update($data);

        return redirect()->route('admin.categories.index')
            ->with('success', 'หมวดหมู่ถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * ลบหมวดหมู่
     */
    public function destroyCategory($id)
    {
        $category = Category::findOrFail($id);

        // ตรวจสอบว่ามีรายการที่ใช้หมวดหมู่นี้หรือไม่
        if ($category->items()->count() > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'ไม่สามารถลบหมวดหมู่ได้เนื่องจากมีรายการที่เกี่ยวข้อง');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'หมวดหมู่ถูกลบเรียบร้อยแล้ว');
    }

    /**
     * แสดงรายการประเภทรายการ (สำหรับความเข้ากันได้กับโค้ดเก่า)
     */
    public function itemTypes()
    {
        // Use the ItemTypeController directly
        $controller = new \App\Http\Controllers\Admin\ItemTypeController();
        return $controller->index();
    }

    /**
     * แสดงรายการภาษา
     */
    public function languages()
    {
        $languages = Language::withCount('items')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.languages.index', compact('languages'));
    }

    /**
     * แสดงรายการอักษร
     */
    public function scripts()
    {
        $scripts = Script::withCount('items')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.scripts.index', compact('scripts'));
    }

    /**
     * แสดงรายการวัสดุ
     */
    public function materials()
    {
        $materials = Material::withCount('items')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.materials.index', compact('materials'));
    }

    /**
     * แสดงรายการผู้ใช้
     */
    public function users()
    {
        $users = User::orderBy('name')
            ->paginate(15);

        return view('admin.users.index', compact('users'));
    }

    /**
     * แสดงหน้าโปรไฟล์
     */
    public function profile()
    {
        $user = Auth::user();

        return view('admin.profile', compact('user'));
    }

    /**
     * อัปเดตโปรไฟล์
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
        ]);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->save();

        return redirect()->route('admin.profile')
            ->with('success', 'โปรไฟล์ถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * อัปเดตรูปโปรไฟล์
     */
    public function updateProfileImage(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'profile_image.required' => 'กรุณาเลือกรูปภาพ',
            'profile_image.image' => 'ไฟล์ที่อัพโหลดต้องเป็นรูปภาพเท่านั้น',
            'profile_image.mimes' => 'รองรับไฟล์ประเภท jpeg, png, jpg, gif เท่านั้น',
            'profile_image.max' => 'ขนาดไฟล์ต้องไม่เกิน 2MB',
        ]);

        // ลบรูปภาพเก่า (ถ้ามี)
        if ($user->profile_image && Storage::disk('public')->exists($user->profile_image)) {
            Storage::disk('public')->delete($user->profile_image);
        }

        // อัพโหลดรูปภาพใหม่
        $image = $request->file('profile_image');
        $imageName = 'profile_' . time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
        $path = $image->storeAs('profiles', $imageName, 'public');

        // บันทึกพาธของรูปภาพลงในฐานข้อมูล
        $user->profile_image = $path;
        $user->save();

        return redirect()->route('admin.profile')
            ->with('success', 'รูปโปรไฟล์ถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * อัปเดตรหัสผ่าน
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        // ตรวจสอบรหัสผ่านปัจจุบัน
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->route('admin.profile')
                ->with('error', 'รหัสผ่านปัจจุบันไม่ถูกต้อง');
        }

        $user->password = Hash::make($request->password);
        $user->save();

        return redirect()->route('admin.profile')
            ->with('success', 'รหัสผ่านถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * แสดงหน้าตั้งค่า
     */
    public function settings()
    {
        $settings = [
            'site_title' => Setting::get('site_title', 'คลังข้อมูลดิจิทัล'),
            'site_description' => Setting::get('site_description', ''),
            'contact_email' => Setting::get('contact_email', '<EMAIL>'),
            'facebook_url' => Setting::get('facebook_url', ''),
            'twitter_url' => Setting::get('twitter_url', ''),
            'instagram_url' => Setting::get('instagram_url', ''),
            'youtube_url' => Setting::get('youtube_url', ''),
            'site_logo' => Setting::get('site_logo', ''),
            'hero_image' => Setting::get('hero_image', ''),
            'footer_text' => Setting::get('footer_text', ''),
            'google_analytics_id' => Setting::get('google_analytics_id', ''),
            'maintenance_mode' => Setting::get('maintenance_mode', 'false'),
            'maintenance_message' => Setting::get('maintenance_message', 'เว็บไซต์อยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่ในภายหลัง'),
        ];

        return view('admin.settings', compact('settings'));
    }

    /**
     * อัปเดตการตั้งค่า
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_title' => 'required|string|max:255',
            'site_description' => 'nullable|string',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:50',
            'contact_address' => 'nullable|string|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'site_logo' => 'nullable|image|max:2048',
            'hero_image' => 'nullable|image|max:2048',
            'footer_text' => 'nullable|string',
            'copyright_text' => 'nullable|string|max:255',
            'google_analytics_id' => 'nullable|string|max:50',
            'maintenance_message' => 'nullable|string',
        ]);

        // Update text settings
        $textSettings = [
            'site_title',
            'site_description',
            'contact_email',
            'contact_phone',
            'contact_address',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'youtube_url',
            'footer_text',
            'copyright_text',
            'google_analytics_id',
            'maintenance_message',
        ];

        foreach ($textSettings as $key) {
            if ($request->has($key)) {
                Setting::set($key, $request->input($key));
            }
        }

        // Update boolean settings
        $maintenanceMode = $request->has('maintenance_mode') ? 'true' : 'false';
        Setting::set('maintenance_mode', $maintenanceMode);

        // บันทึกข้อความบำรุงรักษา
        if ($request->has('maintenance_message')) {
            Setting::set('maintenance_message', $request->maintenance_message);
        }

        // Log สำหรับการตรวจสอบ
        if ($maintenanceMode === 'true') {
            \Log::info('เปิดโหมดบำรุงรักษาสำเร็จ');
        } else {
            \Log::info('ปิดโหมดบำรุงรักษาสำเร็จ');
        }

        // Handle site logo upload
        if ($request->hasFile('site_logo')) {
            // Delete old logo if exists
            $oldLogo = Setting::get('site_logo');
            if ($oldLogo && Storage::exists('public/' . $oldLogo)) {
                Storage::delete('public/' . $oldLogo);
            }

            $logo = $request->file('site_logo');
            $logoName = 'logo_' . time() . '.' . $logo->getClientOriginalExtension();
            $path = $logo->storeAs('settings', $logoName, 'public');
            Setting::set('site_logo', $path);
        }

        // Handle hero image upload
        if ($request->hasFile('hero_image')) {
            // Delete old hero image if exists
            $oldHeroImage = Setting::get('hero_image');
            if ($oldHeroImage && Storage::exists('public/' . $oldHeroImage)) {
                Storage::delete('public/' . $oldHeroImage);
            }

            $heroImage = $request->file('hero_image');
            $heroImageName = 'hero_' . time() . '.' . $heroImage->getClientOriginalExtension();
            $path = $heroImage->storeAs('settings', $heroImageName, 'public');
            Setting::set('hero_image', $path);
        }

        return redirect()->route('admin.settings')
            ->with('success', 'การตั้งค่าถูกบันทึกเรียบร้อยแล้ว');
    }

    /**
     * อัพโหลดไฟล์
     */
    public function uploadFile(Request $request, $id = null)
    {
        // Log the request for debugging
        Log::info('Upload file request received', [
            'files' => $request->allFiles(),
            'headers' => $request->header(),
            'content_type' => $request->header('Content-Type')
        ]);

        // Check for file in different ways (Dropzone sends as 'file')
        $file = null;
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            Log::info('File found in request as "file"');
        } elseif (count($request->allFiles()) > 0) {
            // Get the first file from the request
            $files = $request->allFiles();
            $firstKey = array_key_first($files);
            $file = $files[$firstKey];
            Log::info('File found in request with key: ' . $firstKey);
        } else {
            // Check if this is a request with raw data
            $contentType = $request->header('Content-Type');
            if (strpos($contentType, 'multipart/form-data') !== false) {
                Log::info('Multipart form data detected, checking for raw file data');

                // Try to get the file from the request body
                $fileData = $request->getContent();
                if (!empty($fileData)) {
                    // Create a temporary file
                    $tempFile = tempnam(sys_get_temp_dir(), 'upload_');
                    file_put_contents($tempFile, $fileData);

                    // Create an UploadedFile instance
                    $originalName = $request->header('X-File-Name', 'uploaded_file');
                    $mimeType = $request->header('Content-Type', 'application/octet-stream');

                    $file = new \Illuminate\Http\UploadedFile(
                        $tempFile,
                        $originalName,
                        $mimeType,
                        null,
                        true
                    );

                    Log::info('Created file from raw data', [
                        'name' => $originalName,
                        'mime' => $mimeType,
                        'size' => strlen($fileData)
                    ]);
                }
            }
        }

        if (!$file) {
            Log::error('No file found in request');
            return response()->json(['error' => 'No file uploaded'], 400);
        }

        // Process the file upload regardless of whether we have an ID or not
        // We'll handle the database saving differently based on whether we have an item ID

        // เก็บชื่อไฟล์ต้นฉบับ
        $originalFileName = $file->getClientOriginalName();

        // เก็บไฟล์ไว้ในโฟลเดอร์จริงเลย
        $fileName = time() . '_' . $originalFileName;
        $fileType = $file->getClientOriginalExtension();
        $fileSize = $file->getSize();
        $mimeType = $file->getMimeType();

        // Get file type from header if available
        $headerFileType = $request->header('X-File-Type');

        // Determine storage directory based on file type
        $storageDir = '';
        $isImage = false;
        $type = '';

        // ตรวจสอบประเภทไฟล์จาก MIME type และนามสกุลไฟล์
        $mimeType = $file->getMimeType();
        $fileExtension = strtolower($fileType);

        // ตรวจสอบว่าเป็นรูปภาพหรือไม่
        if (strpos($mimeType, 'image/') === 0 || in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])) {
            $storageDir = 'images';
            $type = 'image';
            $isImage = true;
        }
        // ตรวจสอบว่าเป็นเอกสารหรือไม่
        elseif (strpos($mimeType, 'application/pdf') === 0 ||
                strpos($mimeType, 'application/msword') === 0 ||
                strpos($mimeType, 'application/vnd.openxmlformats-officedocument') === 0 ||
                in_array($fileExtension, ['pdf', 'doc', 'docx'])) {
            $storageDir = 'documents';
            $type = 'document';
            $isImage = false;
        }
        // ตรวจสอบว่าเป็นไฟล์เสียงหรือไม่
        elseif (strpos($mimeType, 'audio/') === 0 || in_array($fileExtension, ['mp3', 'wav', 'ogg', 'flac'])) {
            $storageDir = 'audio';
            $type = 'audio';
            $isImage = false;
        }
        // ตรวจสอบว่าเป็นไฟล์วิดีโอหรือไม่
        elseif (strpos($mimeType, 'video/') === 0 || in_array($fileExtension, ['mp4', 'webm', 'mov', 'avi'])) {
            $storageDir = 'video';
            $type = 'video';
            $isImage = false;
        }
        // ไฟล์ประเภทอื่นๆ
        else {
            $storageDir = 'files';
            $type = 'file';
            $isImage = false;
        }

        // บันทึกข้อมูลการตรวจสอบประเภทไฟล์
        Log::info('File type detection', [
            'mime_type' => $mimeType,
            'extension' => $fileExtension,
            'detected_type' => $type,
            'is_image' => $isImage,
            'storage_dir' => $storageDir
        ]);

        // Remove 'public/' prefix if present to prevent double 'public' in the path
        if (strpos($storageDir, 'public/') === 0) {
            $storageDir = substr($storageDir, 7); // Remove 'public/'
        }

        // Also remove any leading slash
        $storageDir = ltrim($storageDir, '/');

        // Use the public disk directly without any 'public/' prefix in the path
        $path = Storage::disk('public')->putFileAs($storageDir, $file, $fileName);

        // Create the public URL and standardize the path format
        $storagePath = 'storage/' . $path;
        $url = asset($storagePath);

        // Log file path information for debugging
        Log::info('File uploaded for new item (no ID yet)');
        Log::info('Storage path: ' . $path);
        Log::info('Public URL: ' . $url);

        // Check if we have an item_id in the request or route parameter
        $itemId = $id;
        if (!is_numeric($itemId)) {
            $itemId = $request->input('item_id');
        }
        if ($itemId && is_numeric($itemId)) {
            try {
                $item = Item::findOrFail($itemId);

                // Save directly to database
                if ($isImage) {
                    // Create item image record
                    $itemImage = ItemImage::create([
                        'item_id' => $item->id,
                        'image_name' => $originalFileName, // ใช้ชื่อไฟล์ต้นฉบับ
                        'image_path' => $storagePath,
                        'image_type' => $fileType,
                        'sort_order' => ItemImage::where('item_id', $item->id)->max('sort_order') + 1,
                        'is_main' => ItemImage::where('item_id', $item->id)->count() === 0
                    ]);

                    Log::info('Created item image record directly: ' . $itemImage->id);

                    return response()->json([
                        'success' => true,
                        'id' => $itemImage->id,
                        'path' => $storagePath,
                        'url' => $url,
                        'type' => $type,
                        'name' => $fileName,
                        'is_image' => true
                    ]);
                } else {
                    // Create item file record
                    $itemFile = ItemFile::create([
                        'item_id' => $item->id,
                        'file_name' => $originalFileName, // ใช้ชื่อไฟล์ต้นฉบับ
                        'file_path' => $storagePath,
                        'file_type' => $fileType,
                        'file_size' => $fileSize,
                        'file_extension' => $fileType,
                        'sort_order' => ItemFile::where('item_id', $item->id)->max('sort_order') + 1,
                        'is_main' => ItemFile::where('item_id', $item->id)->count() === 0
                    ]);

                    Log::info('Created item file record directly: ' . $itemFile->id);

                    return response()->json([
                        'success' => true,
                        'id' => $itemFile->id,
                        'path' => $storagePath,
                        'url' => $url,
                        'type' => $type,
                        'name' => $fileName,
                        'is_image' => false
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Error saving file to database: ' . $e->getMessage());
                // Continue with returning file info without database record
            }
        }

        return response()->json([
            'success' => true,
            'path' => $storagePath,
            'url' => $url,
            'type' => $type,
            'name' => $originalFileName, // ใช้ชื่อไฟล์ต้นฉบับ
            'file_name' => $originalFileName, // ใช้ชื่อไฟล์ต้นฉบับ
            'extension' => $fileType,
            'file_extension' => $fileType, // เพิ่ม field นี้เพื่อให้ตรงกับที่ ItemFile ต้องการ
            'size' => $fileSize,
            'file_size' => $fileSize, // เพิ่ม field นี้เพื่อให้ตรงกับที่ ItemFile ต้องการ
            'mime_type' => $mimeType,
            'file_type' => $mimeType, // เพิ่ม field นี้เพื่อให้ตรงกับที่ ItemFile ต้องการ
            'is_image' => $isImage
        ]);


    }

    /**
     * อัพโหลดไฟล์สำหรับเอกสาร
     */
    public function uploadFiles(Request $request, $id)
    {
        try {
            if (!$request->hasFile('files')) {
                return response()->json(['error' => 'No file uploaded'], 400);
            }

            $document = Document::findOrFail($id);
            $files = $request->file('files');
            $fileType = $request->input('fileType', 'document');
            $metadata = $request->input('metadata', '{}');

            // Parse metadata
            $metadata = json_decode($metadata, true) ?: [];

            // Process all files in the array
            $uploadedFiles = [];
            $processedFiles = [];

            // Convert to array if it's a single file
            if (!is_array($files)) {
                $files = [$files];
            }

            // Process each file
            foreach ($files as $file) {
                $fileName = $metadata['name'] ?? $file->getClientOriginalName();

                // Determine file category based on mime type
                $mimeType = $file->getMimeType();
                $extension = $file->getClientOriginalExtension();

                // Determine storage directory and database table
                $isImage = strpos($mimeType, 'image/') === 0;
                $storageDir = $isImage ? 'images' : 'files';

                // Generate a unique filename
                $uniqueFileName = time() . '_' . Str::random(10) . '_' . $fileName;

                // Debug information
                Log::info('Uploading file (AdminController): ' . $fileName);
                Log::info('MIME type: ' . $mimeType);
                Log::info('Storage directory: ' . $storageDir);
                Log::info('Unique filename: ' . $uniqueFileName);

                // Store the file in the correct directory
                // Remove 'public/' prefix if present to prevent double 'public' in the path
                if (strpos($storageDir, 'public/') === 0) {
                    $storageDir = substr($storageDir, 7); // Remove 'public/'
                }

                // Also remove any leading slash
                $storageDir = ltrim($storageDir, '/');

                Log::info('Storage directory after cleanup: ' . $storageDir);

                // Use the public disk directly without any 'public/' prefix in the path
                $path = Storage::disk('public')->putFileAs($storageDir, $file, $uniqueFileName);

                // Debug path information
                Log::info('File stored at path: ' . $path);

                // Check if file exists after upload
                if (Storage::disk('public')->exists($path)) {
                    Log::info('File exists in storage at: ' . $path);
                } else {
                    Log::error('File does not exist in storage at: ' . $path);

                    // Check alternative paths for debugging
                    $alt1 = storage_path('app/public/' . $path);
                    $alt2 = storage_path('app/public/public/' . $path); // Check the incorrect path

                    Log::info('Checking alternative path 1: ' . $alt1 . ' exists: ' . (file_exists($alt1) ? 'Yes' : 'No'));
                    Log::info('Checking alternative path 2: ' . $alt2 . ' exists: ' . (file_exists($alt2) ? 'Yes' : 'No'));

                    // Try to copy from the incorrect path if it exists
                    if (file_exists($alt2)) {
                        try {
                            // Ensure the correct directory exists
                            $storageDir = dirname($path);
                            $correctStoragePath = storage_path('app/public/' . $storageDir);
                            if (!file_exists($correctStoragePath)) {
                                mkdir($correctStoragePath, 0755, true);
                            }

                            // Copy from incorrect to correct storage path
                            $correctPath = storage_path('app/public/' . $path);
                            copy($alt2, $correctPath);
                            Log::info('Copied file from incorrect path to correct storage path: ' . $correctPath);
                        } catch (\Exception $e) {
                            Log::error('Failed to copy file from incorrect path: ' . $e->getMessage());
                        }
                    }
                }

                // Create the public URL and standardize the path format
                // When using the 'public' disk, the path is already relative to the storage directory
                $storagePath = '/storage/' . $path;
                $url = asset('storage/' . $path);

                // Verify the file is accessible via the URL
                $publicPath = public_path('storage/' . $path);
                Log::info('Public path check: ' . $publicPath . ' exists: ' . (file_exists($publicPath) ? 'Yes' : 'No'));

                // Copy file to public directory if it doesn't exist there
                if (!file_exists($publicPath)) {
                    $storagePath = storage_path('app/public/' . $path);
                    if (file_exists($storagePath)) {
                        // Ensure directory exists
                        $directory = dirname($publicPath);
                        if (!file_exists($directory)) {
                            mkdir($directory, 0755, true);
                        }

                        // Copy file
                        copy($storagePath, $publicPath);
                        Log::info('Copied file from storage to public: ' . $publicPath);
                    }
                }

                // Use standardized path format for consistency
                $relativePath = $storagePath;

                // Debug URL and relative path
                Log::info('URL: ' . $url);
                Log::info('Relative path: ' . $relativePath);

                $fileSize = $file->getSize();

                // Save to the appropriate table based on file type
                if ($isImage) {
                    // Create document image record
                    $documentImage = new DocumentImage([
                        'document_id' => $document->id,
                        'image_name' => $fileName,
                        'image_path' => $relativePath,
                        'image_type' => $extension,
                        'sort_order' => 0,
                        'is_main' => false
                    ]);

                    $documentImage->save();

                    Log::info('Created document image record: ' . $documentImage->id);

                    // Add to processed files array
                    $processedFiles[] = [
                        'id' => $documentImage->id,
                        'name' => $fileName,
                        'path' => $relativePath,
                        'url' => $url,
                        'mime_type' => $mimeType,
                        'extension' => $extension,
                        'category' => $storageDir,
                        'type' => $extension,
                        'size' => $fileSize,
                        'metadata' => $metadata,
                        'is_image' => true
                    ];
                } else {
                    // Create document file record
                    $documentFile = new DocumentFile([
                        'document_id' => $document->id,
                        'file_name' => $fileName,
                        'file_path' => $relativePath,
                        'file_type' => $extension,
                        'file_size' => $fileSize,
                        'file_extension' => $extension,
                        'sort_order' => 0,
                        'is_main' => false
                    ]);

                    $documentFile->save();

                    Log::info('Created document file record: ' . $documentFile->id);

                    // Add to processed files array
                    $processedFiles[] = [
                        'id' => $documentFile->id,
                        'name' => $fileName,
                        'path' => $relativePath,
                        'url' => $url,
                        'mime_type' => $mimeType,
                        'extension' => $extension,
                        'category' => $storageDir,
                        'type' => $extension,
                        'size' => $fileSize,
                        'metadata' => $metadata,
                        'is_image' => false
                    ];
                }
            }

            // Return response with all processed files
            return response()->json([
                'success' => true,
                'files' => $processedFiles,
                'file' => $processedFiles[0] ?? null // For backward compatibility
            ]);
        } catch (\Exception $e) {
            Log::error('File upload error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * ลบไฟล์
     */
    public function deleteFile(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
            'type' => 'required|in:file',
        ]);

        $id = $request->input('id');

        try {
            $file = DocumentFile::findOrFail($id);
            $document = $file->document;

            // Check if this is the main file
            if ($document->file_path === $file->file_path) {
                // Find another file to set as main
                $newMainFile = $document->files()
                    ->where('id', '!=', $file->id)
                    ->orderBy('sort_order')
                    ->first();

                if ($newMainFile) {
                    $document->file_path = $newMainFile->file_path;
                    $document->file_type = $newMainFile->file_type;
                } else {
                    $document->file_path = null;
                    $document->file_type = null;
                }

                $document->save();
            }

            // Delete the file from storage
            $filePath = $file->file_path;
            Log::info('Attempting to delete document file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            Log::info('Standardized file path for deletion: ' . $filePath);

            // Check and delete from public storage
            $deleted = false;

            // Try to delete from public storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted file from public disk: ' . $filePath);
                $deleted = true;
            }

            // Also check with 'public/' prefix (for legacy paths)
            if (Storage::disk('public')->exists('public/' . $filePath)) {
                Storage::disk('public')->delete('public/' . $filePath);
                Log::info('Deleted file from public disk with public/ prefix: public/' . $filePath);
                $deleted = true;
            }

            // Check in the public directory
            $publicPath = public_path('storage/' . $filePath);
            if (file_exists($publicPath)) {
                unlink($publicPath);
                Log::info('Deleted file from public path: ' . $publicPath);
                $deleted = true;
            }

            // Check in the storage directory
            $storagePath = storage_path('app/public/' . $filePath);
            if (file_exists($storagePath)) {
                unlink($storagePath);
                Log::info('Deleted file from storage path: ' . $storagePath);
                $deleted = true;
            }

            // Check in the incorrect storage directory (with double public)
            $incorrectStoragePath = storage_path('app/public/public/' . $filePath);
            if (file_exists($incorrectStoragePath)) {
                unlink($incorrectStoragePath);
                Log::info('Deleted file from incorrect storage path: ' . $incorrectStoragePath);
                $deleted = true;
            }

            if (!$deleted) {
                Log::warning('Could not find document file to delete. Tried multiple paths for: ' . $file->file_path);
            }

            // Delete the record
            $file->delete();

            return response()->json([
                'success' => true,
                'message' => 'ไฟล์ถูกลบเรียบร้อยแล้ว',
            ]);
        } catch (\Exception $e) {
            Log::error('File deletion error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบไฟล์: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * ลบรูปภาพ
     */
    public function deleteImage(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $id = $request->input('id');

        try {
            $image = DocumentImage::findOrFail($id);
            $document = $image->document;

            // Check if this is the main image
            if ($document->image_path === $image->image_path) {
                // Find another image to set as main
                $newMainImage = $document->images()
                    ->where('id', '!=', $image->id)
                    ->orderBy('sort_order')
                    ->first();

                if ($newMainImage) {
                    $document->image_path = $newMainImage->image_path;
                } else {
                    $document->image_path = null;
                }

                $document->save();
            }

            // Delete the file from storage
            $filePath = $image->image_path;
            Log::info('Attempting to delete image file: ' . $filePath);

            // Standardize the path format
            $filePath = ltrim($filePath, '/');
            if (strpos($filePath, 'storage/') === 0) {
                $filePath = substr($filePath, 8); // Remove 'storage/'
            }

            // Remove 'public/' prefix if present
            if (strpos($filePath, 'public/') === 0) {
                $filePath = substr($filePath, 7); // Remove 'public/'
            }

            Log::info('Standardized file path for deletion: ' . $filePath);

            // Check and delete from public storage
            $deleted = false;

            // Try to delete from public storage
            if (Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
                Log::info('Deleted image from public disk: ' . $filePath);
                $deleted = true;
            }

            // Also check with 'public/' prefix (for legacy paths)
            if (Storage::disk('public')->exists('public/' . $filePath)) {
                Storage::disk('public')->delete('public/' . $filePath);
                Log::info('Deleted image from public disk with public/ prefix: public/' . $filePath);
                $deleted = true;
            }

            // Check in the public directory
            $publicPath = public_path('storage/' . $filePath);
            if (file_exists($publicPath)) {
                unlink($publicPath);
                Log::info('Deleted image from public path: ' . $publicPath);
                $deleted = true;
            }

            // Check in the storage directory
            $storagePath = storage_path('app/public/' . $filePath);
            if (file_exists($storagePath)) {
                unlink($storagePath);
                Log::info('Deleted image from storage path: ' . $storagePath);
                $deleted = true;
            }

            // Check in the incorrect storage directory (with double public)
            $incorrectStoragePath = storage_path('app/public/public/' . $filePath);
            if (file_exists($incorrectStoragePath)) {
                unlink($incorrectStoragePath);
                Log::info('Deleted image from incorrect storage path: ' . $incorrectStoragePath);
                $deleted = true;
            }

            if (!$deleted) {
                Log::warning('Could not find image file to delete. Tried multiple paths for: ' . $image->image_path);
            }

            // Delete the record
            $image->delete();

            return response()->json([
                'success' => true,
                'message' => 'รูปภาพถูกลบเรียบร้อยแล้ว',
            ]);
        } catch (\Exception $e) {
            Log::error('Image deletion error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบรูปภาพ: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * ตั้งค่ารูปภาพหลัก
     */
    public function setMainImage(Request $request)
    {
        $request->validate([
            'id' => 'required|integer',
        ]);

        $imageId = $request->input('id');

        try {
            $image = DocumentImage::findOrFail($imageId);
            $document = $image->document;

            // Set this image as the main image
            $document->image_path = $image->image_path;
            $document->save();

            // Set this image's sort_order to 0 and increment others
            DB::transaction(function () use ($image, $document) {
                // First, increment all sort orders to make room for the new first image
                DocumentImage::where('document_id', $document->id)
                    ->increment('sort_order');

                // Then set the selected image to sort_order 0
                $image->sort_order = 0;
                $image->save();
            });

            return response()->json([
                'success' => true,
                'message' => 'รูปภาพหลักถูกตั้งค่าเรียบร้อยแล้ว',
            ]);
        } catch (\Exception $e) {
            Log::error('Set main image error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการตั้งค่ารูปภาพหลัก: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * ตรวจสอบรหัสรายการ
     */
    public function checkIdentifier(Request $request)
    {
        $request->validate([
            'identifier' => 'required|string',
            'item_id' => 'nullable|integer',
        ]);

        $identifier = $request->input('identifier');
        $itemId = $request->input('item_id');

        // ตรวจสอบว่ามีรายการที่ใช้รหัสนี้แล้วหรือไม่
        $query = Item::where('identifier_no', $identifier);

        // ถ้ามี item_id ให้ไม่นับรายการปัจจุบัน
        if ($itemId) {
            $query->where('id', '!=', $itemId);
        }

        $exists = $query->exists();

        return response()->json([
            'exists' => $exists,
            'message' => $exists ? 'รหัสรายการนี้ถูกใช้งานแล้ว' : 'รหัสรายการนี้สามารถใช้งานได้',
        ]);
    }

    /**
     * ทำความสะอาดไฟล์ชั่วคราว
     */
    public function tempCleanup(Request $request)
    {
        try {
            // ลบไฟล์ชั่วคราวทั้งหมด
            $tempFiles = Storage::files('public/temp');
            $deletedCount = 0;

            foreach ($tempFiles as $file) {
                // ลบไฟล์ทั้งหมดโดยไม่ต้องตรวจสอบอายุไฟล์
                Storage::delete($file);
                $deletedCount++;

                // ลบไฟล์ในโฟลเดอร์ public/storage ด้วย (ถ้ามี)
                $publicPath = str_replace('public/', 'storage/', $file);
                if (file_exists(public_path($publicPath))) {
                    @unlink(public_path($publicPath));
                }
            }

            Log::info("Temp cleanup: Deleted {$deletedCount} files");
            return response()->json(['success' => true, 'deleted' => $deletedCount]);
        } catch (\Exception $e) {
            Log::error('Temp cleanup error: ' . $e->getMessage());
            return response()->json(['success' => false, 'error' => $e->getMessage()]);
        }
    }

    /**
     * แสดง iframe สำหรับ Uppy uploader
     */
    public function uppyIframe(Request $request)
    {
        $documentId = $request->document_id;
        $type = $request->type ?? 'image';

        return view('admin.documents.uppy-iframe', compact('documentId', 'type'));
    }

    /**
     * แสดง iframe สำหรับ Uppy uploader แบบรวม
     */
    public function uppyUnifiedIframe(Request $request)
    {
        $documentId = $request->document_id;

        return view('admin.documents.uppy-unified-iframe', compact('documentId'));
    }

    /**
     * ตั้งค่าไฟล์หลัก
     */
    public function setMainFile(Request $request)
    {
        $fileId = $request->file_id;
        $documentId = $request->document_id;

        // ยกเลิกการตั้งค่าไฟล์หลักเดิม
        DocumentFile::where('document_id', $documentId)
            ->update(['is_main' => false]);

        // ตั้งค่าไฟล์หลักใหม่
        $file = DocumentFile::findOrFail($fileId);
        $file->is_main = true;
        $file->save();

        return response()->json([
            'success' => true,
            'message' => 'ตั้งค่าไฟล์หลักเรียบร้อยแล้ว'
        ]);
    }
}
