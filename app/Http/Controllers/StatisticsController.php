<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Country;
use App\Models\Script;
use Illuminate\Support\Facades\DB;

class StatisticsController extends Controller
{
    public function index()
    {
        // สถิติตามหมวดหมู่
        $categoryStats = Category::withCount('items')
            ->orderBy('items_count', 'desc')
            ->get();

        // สถิติตามประเภทรายการ
        $itemTypeStats = DB::table('items')
            ->select('item_types.name as item_type', DB::raw('count(*) as count'))
            ->join('item_types', 'items.item_type_id', '=', 'item_types.id')
            ->whereNotNull('item_type_id')
            ->groupBy('item_types.name')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติตามภาษา
        $languageStats = DB::table('items')
            ->select('languages.name as language', DB::raw('count(*) as count'))
            ->join('languages', 'items.language_id', '=', 'languages.id')
            ->whereNotNull('language_id')
            ->groupBy('languages.name')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติตามสถานที่
        $locationStats = Item::select('location', DB::raw('count(*) as count'))
            ->whereNotNull('location')
            ->groupBy('location')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติตามปี (ทศวรรษ)
        $yearStats = Item::select(
                DB::raw('FLOOR(year/10)*10 as decade'),
                DB::raw('count(*) as count')
            )
            ->whereNotNull('year')
            ->groupBy('decade')
            ->orderBy('decade')
            ->get();

        // สถิติตามวัสดุ
        $materialStats = DB::table('items')
            ->select('materials.name as material', DB::raw('count(*) as count'))
            ->join('materials', 'items.material_id', '=', 'materials.id')
            ->whereNotNull('material_id')
            ->groupBy('materials.name')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติตามประเทศ
        $countryStats = DB::table('items')
            ->select('countries.name as country', DB::raw('count(*) as count'))
            ->join('countries', 'items.country', '=', 'countries.code')
            ->whereNotNull('country')
            ->groupBy('countries.name')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติตามจังหวัด
        $provinceStats = DB::table('items')
            ->select(
                'items.province',
                DB::raw('CASE
                    WHEN items.province = "MM" THEN "ประเทศเมียนมาร์"
                    ELSE provinces.name_th
                    END as province_name'),
                DB::raw('count(*) as count'))
            ->leftJoin('provinces', 'items.province', '=', 'provinces.code')
            ->whereNotNull('items.province')
            ->groupBy('items.province', 'provinces.name_th')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติตามตัวอักษร
        $scriptStats = DB::table('items')
            ->select('scripts.name as script', DB::raw('count(*) as count'))
            ->join('scripts', 'items.script_id', '=', 'scripts.id')
            ->whereNotNull('script_id')
            ->groupBy('scripts.name')
            ->orderBy('count', 'desc')
            ->get();

        // สถิติรวม
        $totalItems = Item::count();
        $totalCategories = Category::count();
        $totalLanguages = DB::table('languages')->count();
        $totalLocations = Item::whereNotNull('location')->distinct('location')->count('location');
        $totalCountries = DB::table('countries')->count();
        $totalScripts = DB::table('scripts')->count();

        // สถิติรวมจังหวัด
        $totalProvinces = DB::table('items')
            ->whereNotNull('province')
            ->distinct('province')
            ->count('province');

        return view('statistics.index', compact(
            'categoryStats',
            'itemTypeStats',
            'languageStats',
            'locationStats',
            'yearStats',
            'materialStats',
            'countryStats',
            'provinceStats',
            'scriptStats',
            'totalItems',
            'totalCategories',
            'totalLanguages',
            'totalLocations',
            'totalCountries',
            'totalProvinces',
            'totalScripts'
        ));
    }
}
