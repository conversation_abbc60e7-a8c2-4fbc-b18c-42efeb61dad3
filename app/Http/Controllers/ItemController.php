<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\Category;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;
use App\Models\ItemType;
// Location-related models removed

class ItemController extends Controller
{
    /**
     * Display a listing of items using Livewire component
     */
    public function index(Request $request)
    {
        return view('items.index');
    }

    /**
     * Display the specified item
     */
    public function show($id)
    {
        $item = Item::with([
            'category',
            'images',
            'files',
            'countryRelation',
            'provinceRelation'
        ])->findOrFail($id);

        // เพิ่มข้อมูลชื่อจังหวัด
        if ($item->province) {
            $item->province_name = $item->getProvinceNameAttribute();
        }

        // Get related data directly using IDs
        $material = null;
        $language = null;
        $script = null;
        $itemType = null;
        // Location-related variables removed

        if ($item->material_id) {
            $material = Material::find($item->material_id);
        }

        if ($item->language_id) {
            $language = Language::find($item->language_id);
        }

        if ($item->script_id) {
            $script = Script::find($item->script_id);
        }

        if ($item->item_type_id) {
            $itemType = ItemType::find($item->item_type_id);
        }

        // Location-related data retrieval removed

        // Get file type for display
        $fileType = $item->getFileType();

        // Group files by type for better organization
        $itemFiles = [
            'pdf' => [],
            'image' => [],
            'audio' => [],
            'video' => [],
            'other' => []
        ];

        foreach ($item->files as $file) {
            $extension = strtolower(pathinfo($file->file_path, PATHINFO_EXTENSION));

            if (in_array($extension, ['pdf'])) {
                $itemFiles['pdf'][] = $file;
            } elseif (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'svg'])) {
                $itemFiles['image'][] = $file;
            } elseif (in_array($extension, ['mp3', 'wav', 'ogg'])) {
                $itemFiles['audio'][] = $file;
            } elseif (in_array($extension, ['mp4', 'webm', 'avi', 'mov'])) {
                $itemFiles['video'][] = $file;
            } else {
                $itemFiles['other'][] = $file;
            }
        }

        // Increment view count
        $item->increment('views');

        return view('items.show', compact(
            'item',
            'material',
            'language',
            'script',
            'itemType',
            'fileType',
            'itemFiles'
        ));
    }
}
