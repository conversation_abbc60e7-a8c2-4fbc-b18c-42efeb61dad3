<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Item;
use App\Models\ItemType;

class HomeController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        $categories = Category::all();
        $latestItems = Item::with(['category', 'itemType'])->latest()->take(8)->get();
        $itemTypes = ItemType::withCount('items')->orderBy('items_count', 'desc')->take(12)->get();

        // สถิติสำคัญ
        $stats = [
            'total_items' => Item::count(),
            'total_categories' => Category::count(),
            'total_item_types' => ItemType::count(),
            'total_views' => Item::sum('views')
        ];

        return view('home.index', compact('categories', 'latestItems', 'itemTypes', 'stats'));
    }
}
