<?php

namespace App\Http\Controllers;

use App\Models\Country;
use App\Models\Province;
use App\Models\Amphure;
use App\Models\District;
use App\Models\Geography;
use Illuminate\Http\Request;

class LocationController extends Controller
{


    /**
     * Get all countries.
     */
    public function getCountries()
    {
        $countries = Country::orderBy('name')->get();

        // Log the countries for debugging
        \Log::info('Countries retrieved: ' . $countries->count());
        \Log::info($countries->toJson());

        return response()->json($countries);
    }

    /**
     * Get provinces by country code.
     */
    public function getProvincesByCountry($countryCode)
    {
        // Log the request for debugging
        \Log::info('Provinces requested for country code: ' . $countryCode);
        \Log::info('Request URL: ' . request()->fullUrl());
        \Log::info('Request path: ' . request()->path());

        try {
            // Only show provinces for Thailand (TH)
            if (strtoupper($countryCode) === 'TH') {
                $provinces = Province::orderBy('name_th')
                    ->get(['id', 'name_th', 'name_en', 'code']);
            } else {
                $provinces = collect([]); // Empty collection for non-Thailand countries
            }

            // If no provinces found, return empty collection
            if ($provinces->isEmpty()) {
                \Log::warning("No provinces found for country code: {$countryCode}");
            }

            // Log the provinces for debugging
            \Log::info('Provinces retrieved for country ' . $countryCode . ': ' . $provinces->count());
            \Log::info($provinces->toJson());

            return response()->json($provinces);
        } catch (\Exception $e) {
            \Log::error('Error retrieving provinces: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // ส่งข้อผิดพลาดกลับไปให้ client ในรูปแบบ JSON
            return response()->json([
                'error' => 'Error retrieving provinces: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get amphures (districts) by province ID.
     */
    public function getAmphuresByProvince($provinceId)
    {
        $amphures = Amphure::where('province_id', $provinceId)
            ->orderBy('name_th')
            ->get();

        // Log the amphures for debugging
        \Log::info('Amphures retrieved for province ' . $provinceId . ': ' . $amphures->count());
        \Log::info($amphures->toJson());

        return response()->json($amphures);
    }

    /**
     * Get districts (sub-districts) by amphure ID.
     */
    public function getDistrictsByAmphure($amphureId)
    {
        $districts = District::where('amphure_id', $amphureId)
            ->orderBy('name_th')
            ->get();

        // Log the districts for debugging
        \Log::info('Districts retrieved for amphure ' . $amphureId . ': ' . $districts->count());
        \Log::info($districts->toJson());

        return response()->json($districts);
    }
}
