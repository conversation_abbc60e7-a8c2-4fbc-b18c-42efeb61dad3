<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Category;
use App\Models\ItemImage;
use App\Models\ItemFile;
use App\Models\Item;

class CleanupController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // ตรวจสอบว่ามีการสร้าง symlink แล้วหรือไม่
        $this->checkStorageSymlink();
    }

    /**
     * แสดงหน้าเครื่องมือลบไฟล์ขยะ
     */
    public function index()
    {
        return view('admin.cleanup.index');
    }

    /**
     * ตรวจสอบและสร้าง symlink ถ้ายังไม่มี
     */
    private function checkStorageSymlink()
    {
        $publicStoragePath = public_path('storage');

        // ถ้ายังไม่มี symlink ให้ลองสร้าง
        if (!file_exists($publicStoragePath)) {
            try {
                // สร้าง symlink ด้วย PHP
                $target = storage_path('app/public');
                symlink($target, $publicStoragePath);
                Log::info('Created storage symlink: ' . $publicStoragePath . ' -> ' . $target);
            } catch (\Exception $e) {
                Log::error('Failed to create storage symlink: ' . $e->getMessage());
            }
        }
    }



    /**
     * ค้นหาไฟล์ขยะในระบบ
     */
    public function scan(Request $request)
    {
        // ประเภทไฟล์ที่ต้องการตรวจสอบ
        $type = $request->input('type', 'all');

        // ค้นหาไฟล์ขยะตามประเภท
        switch ($type) {
            case 'images':
                $orphanedFiles = $this->scanOrphanedImages();
                break;
            case 'files':
                $orphanedFiles = $this->scanOrphanedFiles();
                break;
            case 'settings':
                $orphanedFiles = $this->scanOrphanedSettingsFiles();
                break;
            case 'categories':
                $orphanedFiles = $this->scanOrphanedCategoryFiles();
                break;
            case 'profiles':
                $orphanedFiles = $this->scanOrphanedProfileFiles();
                break;
            case 'all':
            default:
                $orphanedImages = $this->scanOrphanedImages();
                $orphanedFiles = $this->scanOrphanedFiles();
                $orphanedSettings = $this->scanOrphanedSettingsFiles();
                $orphanedCategories = $this->scanOrphanedCategoryFiles();
                $orphanedProfiles = $this->scanOrphanedProfileFiles();
                $orphanedFiles = array_merge($orphanedImages, $orphanedFiles, $orphanedSettings, $orphanedCategories, $orphanedProfiles);
                break;
        }

        // จัดกลุ่มไฟล์ตามประเภท
        $groupedFiles = [];
        foreach ($orphanedFiles as $file) {
            // ตรวจสอบว่าเป็นไฟล์ประเภทพิเศษหรือไม่
            if (isset($file['fileType'])) {
                switch ($file['fileType']) {
                    case 'settings':
                        $groupedFiles['settings'][] = $file;
                        continue 2;
                    case 'categories':
                        $groupedFiles['categories'][] = $file;
                        continue 2;
                    case 'profiles':
                        $groupedFiles['profiles'][] = $file;
                        continue 2;
                }
            }

            $extension = pathinfo($file['path'], PATHINFO_EXTENSION);
            $extension = strtolower($extension);

            // จัดกลุ่มตามประเภทไฟล์
            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'])) {
                $groupedFiles['images'][] = $file;
            } elseif (in_array($extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
                // ยังคงใช้ชื่อ documents ในฝั่ง backend แต่แสดงเป็น "ไฟล์อื่นๆ" ในฝั่ง frontend
                $groupedFiles['documents'][] = $file;
            } else {
                $groupedFiles['others'][] = $file;
            }
        }

        // คำนวณขนาดรวมของไฟล์ขยะ
        $totalSize = array_sum(array_column($orphanedFiles, 'size'));

        return response()->json([
            'success' => true,
            'files' => $groupedFiles,
            'total' => count($orphanedFiles),
            'totalSize' => $totalSize,
            'totalSizeFormatted' => $this->formatBytes($totalSize),
            'type' => $type // ส่งประเภทที่ค้นหากลับไปด้วย
        ]);
    }

    /**
     * ลบไฟล์ขยะที่เลือก
     */
    public function cleanup(Request $request)
    {
        $files = $request->input('files', []);
        $deletedCount = 0;
        $failedCount = 0;
        $totalSize = 0;

        foreach ($files as $file) {
            $path = $file['path'];
            $size = $file['size'] ?? 0;

            try {
                // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                if (Storage::disk('public')->exists($path)) {
                    // ลบไฟล์
                    Storage::disk('public')->delete($path);
                    $deletedCount++;
                    $totalSize += $size;
                    Log::info("Deleted orphaned file: {$path}");

                    // ตรวจสอบและลบไฟล์ในโฟลเดอร์ public/storage ด้วย (ถ้ามี)
                    $publicPath = public_path('storage/' . $path);
                    if (file_exists($publicPath)) {
                        @unlink($publicPath);
                        Log::info("Also deleted public copy: {$publicPath}");
                    }
                } else {
                    $failedCount++;
                    Log::warning("Failed to delete orphaned file (not found): {$path}");
                }
            } catch (\Exception $e) {
                $failedCount++;
                Log::error("Error deleting orphaned file: {$path} - " . $e->getMessage());
            }
        }

        return response()->json([
            'success' => true,
            'deleted' => $deletedCount,
            'failed' => $failedCount,
            'totalSizeDeleted' => $totalSize,
            'totalSizeDeletedFormatted' => $this->formatBytes($totalSize)
        ]);
    }

    /**
     * ค้นหารูปภาพที่ไม่มีการอ้างอิงในฐานข้อมูล
     */
    private function scanOrphanedImages()
    {
        // รายการไฟล์ในโฟลเดอร์ storage/app/public/images และ storage/app/public/categories
        $storedImages = array_merge(
            $this->getFilesInDirectory('images'),
            $this->getFilesInDirectory('categories'),
            $this->getFilesInDirectory('item-types')
        );

        // รายการรูปภาพที่มีการอ้างอิงในฐานข้อมูล
        $referencedImages = $this->getReferencedImages();

        // เก็บข้อมูลการตรวจสอบไว้ในล็อก
        Log::info('Referenced images count: ' . count($referencedImages));

        // หาไฟล์ที่ไม่มีการอ้างอิง
        $orphanedImages = [];
        foreach ($storedImages as $image) {
            $relativePath = $image['path'];
            $fileName = basename($relativePath);

            // ข้ามไฟล์ในโฟลเดอร์ defaults (ตรวจสอบซ้ำเพื่อความมั่นใจ)
            if (strpos($relativePath, 'defaults/') !== false) {
                continue;
            }

            // ข้ามไฟล์ที่มีชื่อเริ่มต้นด้วย "default" หรือ "placeholder"
            if (strpos(strtolower($fileName), 'default') === 0 || strpos(strtolower($fileName), 'placeholder') === 0) {
                continue;
            }

            // ตรวจสอบว่ามีการอ้างอิงหรือไม่ โดยใช้ทั้งเส้นทางเต็มและชื่อไฟล์
            $isReferenced = false;

            foreach ($referencedImages as $refPath) {
                // เปรียบเทียบเส้นทางเต็ม
                if ($relativePath == $refPath) {
                    $isReferenced = true;
                    break;
                }

                // เปรียบเทียบชื่อไฟล์
                if ($fileName == basename($refPath)) {
                    $isReferenced = true;
                    break;
                }
            }

            if (!$isReferenced) {
                $orphanedImages[] = $image;
                Log::info('Found orphaned image: ' . $relativePath);
            }
        }

        return $orphanedImages;
    }

    /**
     * ค้นหาไฟล์ที่ไม่มีการอ้างอิงในฐานข้อมูล
     */
    private function scanOrphanedFiles()
    {
        // รายการไฟล์ในโฟลเดอร์ต่างๆ ที่อาจมีไฟล์อื่นๆ
        $storedFiles = array_merge(
            $this->getFilesInDirectory('files'),
            $this->getFilesInDirectory('documents'),
            $this->getFilesInDirectory('uploads'),
            $this->getFilesInDirectory('attachments')
        );

        // รายการไฟล์ที่มีการอ้างอิงในฐานข้อมูล
        $referencedFiles = $this->getReferencedFiles();

        // เก็บข้อมูลการตรวจสอบไว้ในล็อก
        Log::info('Referenced files count: ' . count($referencedFiles));
        Log::info('Stored files count: ' . count($storedFiles));

        // หาไฟล์ที่ไม่มีการอ้างอิง
        $orphanedFiles = [];
        foreach ($storedFiles as $file) {
            // ข้ามไฟล์รูปภาพ (เพราะจะถูกตรวจสอบโดย scanOrphanedImages)
            $extension = strtolower(pathinfo($file['path'], PATHINFO_EXTENSION));
            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'])) {
                continue;
            }

            $relativePath = $file['path'];
            $fileName = basename($relativePath);

            // ตรวจสอบว่ามีการอ้างอิงหรือไม่ โดยใช้ทั้งเส้นทางเต็มและชื่อไฟล์
            $isReferenced = false;

            foreach ($referencedFiles as $refPath) {
                // เปรียบเทียบเส้นทางเต็ม
                if ($relativePath == $refPath) {
                    $isReferenced = true;
                    break;
                }

                // เปรียบเทียบชื่อไฟล์
                if ($fileName == basename($refPath)) {
                    $isReferenced = true;
                    break;
                }
            }

            if (!$isReferenced) {
                // กำหนดประเภทไฟล์ให้ถูกต้อง
                $file['fileType'] = 'file';
                $orphanedFiles[] = $file;
                Log::info('Found orphaned file: ' . $relativePath);
            }
        }

        return $orphanedFiles;
    }

    /**
     * ดึงรายการไฟล์ในโฟลเดอร์ที่กำหนด
     */
    private function getFilesInDirectory($directory)
    {
        $files = [];

        // ตรวจสอบว่าโฟลเดอร์มีอยู่หรือไม่
        if (!Storage::disk('public')->exists($directory)) {
            Log::info("Directory not found: {$directory}");
            return $files;
        }

        // ดึงรายการไฟล์ทั้งหมดในโฟลเดอร์และโฟลเดอร์ย่อย
        $allFiles = Storage::disk('public')->allFiles($directory);
        Log::info("Found {$directory} files: " . count($allFiles));

        foreach ($allFiles as $file) {
            // ข้ามไฟล์ .gitignore และไฟล์ซ่อนอื่นๆ
            if (strpos(basename($file), '.') === 0) {
                continue;
            }

            // ข้ามไฟล์ในโฟลเดอร์ defaults
            if (strpos($file, 'defaults/') !== false) {
                continue;
            }

            // ตรวจสอบประเภทไฟล์
            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            $fileType = 'other';

            // กำหนดประเภทไฟล์ตาม extension
            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'])) {
                $fileType = 'image';
            } elseif (in_array($extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
                $fileType = 'document';
            } elseif ($directory === 'temp') {
                $fileType = 'temp';
            }

            // ข้อมูลไฟล์
            $files[] = [
                'path' => $file,
                'name' => basename($file),
                'size' => Storage::disk('public')->size($file),
                'last_modified' => Storage::disk('public')->lastModified($file),
                'url' => asset('storage/' . $file),
                'fileType' => $fileType
            ];
        }

        Log::info("Processed {$directory} files: " . count($files));
        return $files;
    }

    /**
     * ดึงรายการรูปภาพที่มีการอ้างอิงในฐานข้อมูล
     */
    private function getReferencedImages()
    {
        $referencedImages = [];

        // รูปภาพจากตาราง categories
        $categoryImages = Category::whereNotNull('image_path')->pluck('image_path')->toArray();

        // รูปภาพจากตาราง item_images
        $itemImages = ItemImage::pluck('image_path')->toArray();

        // รูปภาพจากตาราง items (image_path)
        $itemMainImages = Item::whereNotNull('image_path')->pluck('image_path')->toArray();

        // รูปภาพจากตาราง item_types (icon_path)
        $itemTypeIcons = DB::table('item_types')->whereNotNull('icon_path')->pluck('icon_path')->toArray();

        // รูปภาพจากตาราง settings (site_logo, hero_image, favicon)
        $settingsImages = DB::table('settings')
            ->whereIn('key', ['site_logo', 'hero_image', 'favicon'])
            ->whereNotNull('value')
            ->pluck('value')
            ->toArray();

        // รูปภาพจากตาราง users (profile_image)
        $userImages = DB::table('users')->whereNotNull('profile_image')->pluck('profile_image')->toArray();

        // รวมรายการรูปภาพที่มีการอ้างอิง
        $referencedImages = array_merge(
            $categoryImages,
            $itemImages,
            $itemMainImages,
            $itemTypeIcons,
            $settingsImages,
            $userImages
        );

        // ทำความสะอาดเส้นทางไฟล์
        $referencedImages = array_map(function($path) {
            // ลบ /storage/ ออกจากเส้นทาง
            return preg_replace('#^/?(storage/)?#', '', $path);
        }, $referencedImages);

        // ล็อกข้อมูลเส้นทางไฟล์เพื่อตรวจสอบ
        Log::debug('Referenced images paths: ' . json_encode(array_slice($referencedImages, 0, 10)));

        return $referencedImages;
    }

    /**
     * ดึงรายการไฟล์ที่มีการอ้างอิงในฐานข้อมูล
     */
    private function getReferencedFiles()
    {
        $referencedFiles = [];

        // ไฟล์จากตาราง item_files
        $itemFiles = ItemFile::pluck('file_path')->toArray();

        // ไฟล์จากตาราง items (file_path)
        $itemMainFiles = Item::whereNotNull('file_path')->pluck('file_path')->toArray();

        // รวมรายการไฟล์ที่มีการอ้างอิง
        $referencedFiles = array_merge($itemFiles, $itemMainFiles);

        // ทำความสะอาดเส้นทางไฟล์
        $referencedFiles = array_map(function($path) {
            // ลบ /storage/ ออกจากเส้นทาง
            return preg_replace('#^/?(storage/)?#', '', $path);
        }, $referencedFiles);

        // ล็อกข้อมูลเส้นทางไฟล์เพื่อตรวจสอบ
        Log::debug('Referenced files paths: ' . json_encode(array_slice($referencedFiles, 0, 10)));

        return $referencedFiles;
    }

    /**
     * แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    /**
     * ค้นหาไฟล์ settings ที่ไม่ได้ใช้
     */
    private function scanOrphanedSettingsFiles()
    {
        // รายการไฟล์ในโฟลเดอร์ storage/app/public/settings
        $storedFiles = $this->getFilesInDirectory('settings');

        // รายการไฟล์ที่มีการอ้างอิงในฐานข้อมูล settings
        $referencedFiles = DB::table('settings')
            ->whereNotNull('value')
            ->where('value', '!=', '')
            ->pluck('value')
            ->toArray();

        // ทำความสะอาดเส้นทางไฟล์
        $referencedFiles = array_map(function($path) {
            return preg_replace('#^/?(storage/)?#', '', $path);
        }, $referencedFiles);

        // หาไฟล์ที่ไม่มีการอ้างอิง
        $orphanedFiles = [];
        foreach ($storedFiles as $file) {
            if (!in_array($file['path'], $referencedFiles)) {
                $file['fileType'] = 'settings';
                $orphanedFiles[] = $file;
                Log::info('Found orphaned settings file: ' . $file['path']);
            }
        }

        return $orphanedFiles;
    }

    /**
     * ค้นหาไฟล์ categories ที่ไม่ได้ใช้
     */
    private function scanOrphanedCategoryFiles()
    {
        // รายการไฟล์ในโฟลเดอร์ storage/app/public/categories
        $storedFiles = $this->getFilesInDirectory('categories');

        // รายการไฟล์ที่มีการอ้างอิงในฐานข้อมูล categories
        $referencedFiles = DB::table('categories')
            ->whereNotNull('image_path')
            ->where('image_path', '!=', '')
            ->pluck('image_path')
            ->toArray();

        // ทำความสะอาดเส้นทางไฟล์
        $referencedFiles = array_map(function($path) {
            return preg_replace('#^/?(storage/)?#', '', $path);
        }, $referencedFiles);

        // หาไฟล์ที่ไม่มีการอ้างอิง
        $orphanedFiles = [];
        foreach ($storedFiles as $file) {
            if (!in_array($file['path'], $referencedFiles)) {
                $file['fileType'] = 'categories';
                $orphanedFiles[] = $file;
                Log::info('Found orphaned category file: ' . $file['path']);
            }
        }

        return $orphanedFiles;
    }

    /**
     * ค้นหาไฟล์ profiles ที่ไม่ได้ใช้
     */
    private function scanOrphanedProfileFiles()
    {
        // รายการไฟล์ในโฟลเดอร์ storage/app/public/profiles
        $storedFiles = $this->getFilesInDirectory('profiles');

        // รายการไฟล์ที่มีการอ้างอิงในฐานข้อมูล users
        $referencedFiles = DB::table('users')
            ->whereNotNull('profile_image')
            ->where('profile_image', '!=', '')
            ->pluck('profile_image')
            ->toArray();

        // ทำความสะอาดเส้นทางไฟล์
        $referencedFiles = array_map(function($path) {
            return preg_replace('#^/?(storage/)?#', '', $path);
        }, $referencedFiles);

        // หาไฟล์ที่ไม่มีการอ้างอิง
        $orphanedFiles = [];
        foreach ($storedFiles as $file) {
            if (!in_array($file['path'], $referencedFiles)) {
                $file['fileType'] = 'profiles';
                $orphanedFiles[] = $file;
                Log::info('Found orphaned profile file: ' . $file['path']);
            }
        }

        return $orphanedFiles;
    }
}
