<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Script;

class ScriptController extends Controller
{
    /**
     * Display a listing of the scripts.
     */
    public function index()
    {
        $scripts = Script::withCount('items')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.scripts.index', compact('scripts'));
    }

    /**
     * Show the form for creating a new script.
     */
    public function create()
    {
        return view('admin.scripts.create');
    }

    /**
     * Store a newly created script in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:10|unique:scripts,code',
            'description' => 'nullable|string',
        ]);

        Script::create($request->all());

        return redirect()->route('admin.scripts')
            ->with('success', 'อักษรถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified script.
     */
    public function show(Script $script)
    {
        $script->load(['items' => function ($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.scripts.show', compact('script'));
    }

    /**
     * Show the form for editing the specified script.
     */
    public function edit(Script $script)
    {
        return view('admin.scripts.edit', compact('script'));
    }

    /**
     * Update the specified script in storage.
     */
    public function update(Request $request, Script $script)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:10|unique:scripts,code,' . $script->id,
            'description' => 'nullable|string',
        ]);

        $script->update($request->all());

        return redirect()->route('admin.scripts')
            ->with('success', 'อักษรถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified script from storage.
     */
    public function destroy(Script $script)
    {
        // Check if script has items
        if ($script->items()->count() > 0) {
            return redirect()->route('admin.scripts')
                ->with('error', 'ไม่สามารถลบอักษรได้เนื่องจากมีรายการที่เกี่ยวข้อง');
        }

        $script->delete();

        return redirect()->route('admin.scripts')
            ->with('success', 'อักษรถูกลบเรียบร้อยแล้ว');
    }
}
