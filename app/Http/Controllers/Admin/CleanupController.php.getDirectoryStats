    /**
     * ดึงข้อมูลสถิติไฟล์ในโฟลเดอร์ที่กำหนด
     */
    private function getDirectoryStats($directories)
    {
        $count = 0;
        $size = 0;
        
        foreach ($directories as $directory) {
            if (Storage::disk('public')->exists($directory)) {
                $files = Storage::disk('public')->allFiles($directory);
                
                foreach ($files as $file) {
                    // ข้ามไฟล์ .gitignore และไฟล์ซ่อนอื่นๆ
                    if (strpos(basename($file), '.') === 0) {
                        continue;
                    }
                    
                    // ข้ามไฟล์ในโฟลเดอร์ defaults
                    if (strpos($file, 'defaults/') !== false) {
                        continue;
                    }
                    
                    $count++;
                    $size += Storage::disk('public')->size($file);
                }
            }
        }
        
        return [
            'count' => $count,
            'size' => $size
        ];
    }
