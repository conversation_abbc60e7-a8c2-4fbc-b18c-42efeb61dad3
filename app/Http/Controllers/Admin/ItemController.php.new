<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Item;
use App\Models\ItemFile;
use App\Models\ItemImage;
use App\Models\ItemType;
use App\Models\Language;
use App\Models\Material;
use App\Models\Script;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ItemController extends Controller
{
    /**
     * Display a listing of the items.
     */
    public function index()
    {
        $items = Item::with(['itemType', 'category', 'material', 'language', 'script'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.items.index', compact('items'));
    }

    /**
     * Show the form for creating a new item.
     */
    public function create()
    {
        $itemTypes = ItemType::all();
        $categories = Category::all();
        $materials = Material::all();
        $languages = Language::all();
        $scripts = Script::all();

        // Generate a temporary item ID for file uploads
        $tempItemId = Str::uuid();
        session(['temp_item_id' => $tempItemId]);

        return view('admin.items.create', compact('itemTypes', 'categories', 'materials', 'languages', 'scripts', 'tempItemId'));
    }

    /**
     * Store a newly created item in storage.
     */
    public function store(Request $request)
    {
        // Log all request data for debugging
        Log::info('ItemController::store - Request data:', [
            'all' => $request->all(),
            'has_uploaded_images' => $request->has('uploaded_images'),
            'has_uploaded_files' => $request->has('uploaded_files'),
            'uploaded_images' => $request->uploaded_images,
            'uploaded_files' => $request->uploaded_files
        ]);
        
        // ตรวจสอบว่าข้อมูลรูปภาพและไฟล์ถูกส่งมาหรือไม่
        if ($request->has('uploaded_images')) {
            $uploadedImagesData = json_decode($request->uploaded_images, true);
            Log::info('Decoded uploaded_images:', [
                'data' => $uploadedImagesData,
                'is_array' => is_array($uploadedImagesData),
                'count' => is_array($uploadedImagesData) ? count($uploadedImagesData) : 0
            ]);
        }
        
        if ($request->has('uploaded_files')) {
            $uploadedFilesData = json_decode($request->uploaded_files, true);
            Log::info('Decoded uploaded_files:', [
                'data' => $uploadedFilesData,
                'is_array' => is_array($uploadedFilesData),
                'count' => is_array($uploadedFilesData) ? count($uploadedFilesData) : 0
            ]);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'year' => 'nullable|string|max:10',
            'item_type_id' => 'nullable|exists:item_types,id',
            'category_id' => 'nullable|exists:categories,id',
            'identifier_no' => 'nullable|string|max:100',
            'material_id' => 'nullable|exists:materials,id',
            'language_id' => 'nullable|exists:languages,id',
            'script_id' => 'nullable|exists:scripts,id',
            'creator' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:10',
            'province' => 'nullable|string|max:10',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'remark' => 'nullable|string',
            'manuscript_condition' => 'nullable|string|max:255',
        ]);

        // สร้างรายการใหม่โดยตรง
        $item = Item::create($request->all());
        Log::info('Created new item', [
            'item_id' => $item->id
        ]);

        // บันทึกข้อมูลรูปภาพ
        if ($request->has('uploaded_images') && !empty($request->uploaded_images)) {
            $uploadedImages = json_decode($request->uploaded_images, true);
            
            Log::info('Processing uploaded images', [
                'item_id' => $item->id,
                'images_count' => is_array($uploadedImages) ? count($uploadedImages) : 0
            ]);
            
            if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                foreach ($uploadedImages as $index => $imageData) {
                    try {
                        // สร้างข้อมูลรูปภาพใหม่
                        $itemImage = new ItemImage();
                        $itemImage->item_id = $item->id;
                        $itemImage->image_path = $imageData['path'] ?? '';
                        $itemImage->image_name = $imageData['name'] ?? ($imageData['file_name'] ?? '');
                        $itemImage->image_type = $imageData['type'] ?? ($imageData['file_type'] ?? '');
                        $itemImage->sort_order = $index;
                        $itemImage->is_main = ($index === 0);
                        
                        $saveResult = $itemImage->save();
                        
                        Log::info('Image save result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'image_id' => $itemImage->id ?? 'no_id'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving image', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id
                        ]);
                    }
                }
            }
        }
        
        // บันทึกข้อมูลไฟล์
        if ($request->has('uploaded_files') && !empty($request->uploaded_files)) {
            $uploadedFiles = json_decode($request->uploaded_files, true);
            
            Log::info('Processing uploaded files', [
                'item_id' => $item->id,
                'files_count' => is_array($uploadedFiles) ? count($uploadedFiles) : 0
            ]);
            
            if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                foreach ($uploadedFiles as $index => $fileData) {
                    try {
                        // สร้างข้อมูลไฟล์ใหม่
                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $fileData['path'] ?? '';
                        $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                        $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                        $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                        $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                        $itemFile->sort_order = $index;
                        $itemFile->is_main = ($index === 0);
                        
                        $saveResult = $itemFile->save();
                        
                        Log::info('File save result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'file_id' => $itemFile->id ?? 'no_id'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving file', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id
                        ]);
                    }
                }
            }
        }

        // ตรวจสอบว่ามีการบันทึกข้อมูลไฟล์หรือไม่
        $savedImages = ItemImage::where('item_id', $item->id)->get();
        $savedFiles = ItemFile::where('item_id', $item->id)->get();

        Log::info('Final check - Saved images and files', [
            'item_id' => $item->id,
            'saved_images_count' => $savedImages->count(),
            'saved_files_count' => $savedFiles->count()
        ]);

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกสร้างเรียบร้อยแล้ว');
    }

    // ส่วนที่เหลือของคลาสยังคงเหมือนเดิม
}
