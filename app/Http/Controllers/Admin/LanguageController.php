<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Language;

class LanguageController extends Controller
{
    /**
     * Display a listing of the languages.
     */
    public function index()
    {
        $languages = Language::withCount('items')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.languages.index', compact('languages'));
    }

    /**
     * Show the form for creating a new language.
     */
    public function create()
    {
        return view('admin.languages.create');
    }

    /**
     * Store a newly created language in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:10|unique:languages,code',
            'description' => 'nullable|string',
        ]);

        Language::create($request->all());

        return redirect()->route('admin.languages')
            ->with('success', 'ภาษาถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified language.
     */
    public function show(Language $language)
    {
        $language->load(['items' => function ($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.languages.show', compact('language'));
    }

    /**
     * Show the form for editing the specified language.
     */
    public function edit(Language $language)
    {
        return view('admin.languages.edit', compact('language'));
    }

    /**
     * Update the specified language in storage.
     */
    public function update(Request $request, Language $language)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:10|unique:languages,code,' . $language->id,
            'description' => 'nullable|string',
        ]);

        $language->update($request->all());

        return redirect()->route('admin.languages')
            ->with('success', 'ภาษาถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified language from storage.
     */
    public function destroy(Language $language)
    {
        // Check if language has items
        if ($language->items()->count() > 0) {
            return redirect()->route('admin.languages')
                ->with('error', 'ไม่สามารถลบภาษาได้เนื่องจากมีรายการที่เกี่ยวข้อง');
        }

        $language->delete();

        return redirect()->route('admin.languages')
            ->with('success', 'ภาษาถูกลบเรียบร้อยแล้ว');
    }
}
