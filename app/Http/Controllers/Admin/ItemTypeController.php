<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ItemType;

class ItemTypeController extends Controller
{
    /**
     * Display a listing of the item types.
     */
    public function index()
    {
        $itemTypes = ItemType::withCount('items')
            ->orderBy('name')
            ->paginate(15);
        
        return view('admin.item-types.index', compact('itemTypes'));
    }

    /**
     * Show the form for creating a new item type.
     */
    public function create()
    {
        return view('admin.item-types.create');
    }

    /**
     * Store a newly created item type in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon_class' => 'nullable|string|max:50',
        ]);
        
        ItemType::create($request->all());
        
        return redirect()->route('admin.item-types.index')
            ->with('success', 'ประเภทรายการถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified item type.
     */
    public function show(ItemType $itemType)
    {
        $itemType->load(['items' => function ($query) {
            $query->latest()->take(10);
        }]);
        
        return view('admin.item-types.show', compact('itemType'));
    }

    /**
     * Show the form for editing the specified item type.
     */
    public function edit(ItemType $itemType)
    {
        return view('admin.item-types.edit', compact('itemType'));
    }

    /**
     * Update the specified item type in storage.
     */
    public function update(Request $request, ItemType $itemType)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon_class' => 'nullable|string|max:50',
        ]);
        
        $itemType->update($request->all());
        
        return redirect()->route('admin.item-types.index')
            ->with('success', 'ประเภทรายการถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified item type from storage.
     */
    public function destroy(ItemType $itemType)
    {
        // Check if item type has items
        if ($itemType->items()->count() > 0) {
            return redirect()->route('admin.item-types.index')
                ->with('error', 'ไม่สามารถลบประเภทรายการได้เนื่องจากมีรายการที่เกี่ยวข้อง');
        }
        
        $itemType->delete();
        
        return redirect()->route('admin.item-types.index')
            ->with('success', 'ประเภทรายการถูกลบเรียบร้อยแล้ว');
    }
}
