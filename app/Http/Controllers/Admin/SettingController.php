<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SettingController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        $settings = [
            'site_title' => Setting::get('site_title', 'คลังเอกสารดิจิทัล'),
            'site_description' => Setting::get('site_description', ''),
            'contact_email' => Setting::get('contact_email', '<EMAIL>'),
            'facebook_url' => Setting::get('facebook_url', ''),
            'twitter_url' => Setting::get('twitter_url', ''),
            'instagram_url' => Setting::get('instagram_url', ''),
            'youtube_url' => Setting::get('youtube_url', ''),
            'site_logo' => Setting::get('site_logo', ''),
            'hero_image' => Setting::get('hero_image', ''),
            'footer_text' => Setting::get('footer_text', ''),
            'google_analytics_id' => Setting::get('google_analytics_id', ''),
            'maintenance_mode' => Setting::get('maintenance_mode', 'false'),
            'maintenance_message' => Setting::get('maintenance_message', 'เว็บไซต์อยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่ในภายหลัง'),
        ];
        
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            'site_title' => 'required|string|max:255',
            'site_description' => 'nullable|string',
            'contact_email' => 'nullable|email|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'site_logo' => 'nullable|image|max:2048',
            'hero_image' => 'nullable|image|max:2048',
            'footer_text' => 'nullable|string',
            'google_analytics_id' => 'nullable|string|max:50',
            'maintenance_mode' => 'nullable|in:true,false',
            'maintenance_message' => 'nullable|string',
        ]);
        
        // Update text settings
        $textSettings = [
            'site_title',
            'site_description',
            'contact_email',
            'facebook_url',
            'twitter_url',
            'instagram_url',
            'youtube_url',
            'footer_text',
            'google_analytics_id',
            'maintenance_message',
        ];
        
        foreach ($textSettings as $key) {
            if ($request->has($key)) {
                Setting::set($key, $request->input($key));
            }
        }
        
        // Update boolean settings
        Setting::set('maintenance_mode', $request->has('maintenance_mode') ? 'true' : 'false');
        
        // Handle site logo upload
        if ($request->hasFile('site_logo')) {
            // Delete old logo if exists
            $oldLogo = Setting::get('site_logo');
            if ($oldLogo && Storage::exists('public/' . $oldLogo)) {
                Storage::delete('public/' . $oldLogo);
            }
            
            $logo = $request->file('site_logo');
            $logoName = 'logo_' . time() . '.' . $logo->getClientOriginalExtension();
            $path = $logo->storeAs('public/settings', $logoName);
            Setting::set('site_logo', str_replace('public/', '', $path));
        }
        
        // Handle hero image upload
        if ($request->hasFile('hero_image')) {
            // Delete old hero image if exists
            $oldHeroImage = Setting::get('hero_image');
            if ($oldHeroImage && Storage::exists('public/' . $oldHeroImage)) {
                Storage::delete('public/' . $oldHeroImage);
            }
            
            $heroImage = $request->file('hero_image');
            $heroImageName = 'hero_' . time() . '.' . $heroImage->getClientOriginalExtension();
            $path = $heroImage->storeAs('public/settings', $heroImageName);
            Setting::set('hero_image', str_replace('public/', '', $path));
        }
        
        return redirect()->route('admin.settings')
            ->with('success', 'การตั้งค่าถูกบันทึกเรียบร้อยแล้ว');
    }
}
