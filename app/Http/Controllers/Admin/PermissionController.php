<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionController extends Controller
{
    /**
     * Display a listing of the permissions.
     */
    public function index(Request $request)
    {
        $query = Permission::with('roles');
        
        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }
        
        $permissions = $query->orderBy('name')->paginate(10);
        
        return view('admin.permissions.index', compact('permissions'));
    }

    /**
     * Show the form for creating a new permission.
     */
    public function create()
    {
        return view('admin.permissions.create');
    }

    /**
     * Store a newly created permission in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
        ], [
            'name.required' => 'กรุณาระบุชื่อสิทธิ์',
            'name.unique' => 'ชื่อสิทธิ์นี้มีอยู่แล้ว',
        ]);

        Permission::create(['name' => $request->name]);

        return redirect()->route('admin.permissions')
            ->with('success', 'เพิ่มสิทธิ์เรียบร้อยแล้ว');
    }

    /**
     * Show the form for editing the specified permission.
     */
    public function edit(Permission $permission)
    {
        return view('admin.permissions.edit', compact('permission'));
    }

    /**
     * Update the specified permission in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
        ], [
            'name.required' => 'กรุณาระบุชื่อสิทธิ์',
            'name.unique' => 'ชื่อสิทธิ์นี้มีอยู่แล้ว',
        ]);

        $permission->update(['name' => $request->name]);

        return redirect()->route('admin.permissions')
            ->with('success', 'แก้ไขสิทธิ์เรียบร้อยแล้ว');
    }

    /**
     * Remove the specified permission from storage.
     */
    public function destroy(Permission $permission)
    {
        // Check if permission is in use by any roles
        if ($permission->roles->count() > 0) {
            return redirect()->route('admin.permissions')
                ->with('error', 'ไม่สามารถลบสิทธิ์นี้ได้เนื่องจากมีบทบาทที่ใช้สิทธิ์นี้อยู่');
        }

        $permission->delete();

        return redirect()->route('admin.permissions')
            ->with('success', 'ลบสิทธิ์เรียบร้อยแล้ว');
    }
}
