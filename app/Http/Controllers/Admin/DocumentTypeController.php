<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DocumentType;

class DocumentTypeController extends Controller
{
    /**
     * Display a listing of the document types.
     */
    public function index()
    {
        $documentTypes = DocumentType::withCount('documents')
            ->orderBy('name')
            ->paginate(15);
        
        return view('admin.document-types.index', compact('documentTypes'));
    }

    /**
     * Show the form for creating a new document type.
     */
    public function create()
    {
        return view('admin.document-types.create');
    }

    /**
     * Store a newly created document type in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:50',
        ]);
        
        DocumentType::create($request->all());
        
        return redirect()->route('admin.document-types.index')
            ->with('success', 'ประเภทเอกสารถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified document type.
     */
    public function show(DocumentType $documentType)
    {
        $documentType->load(['documents' => function ($query) {
            $query->latest()->take(10);
        }]);
        
        return view('admin.document-types.show', compact('documentType'));
    }

    /**
     * Show the form for editing the specified document type.
     */
    public function edit(DocumentType $documentType)
    {
        return view('admin.document-types.edit', compact('documentType'));
    }

    /**
     * Update the specified document type in storage.
     */
    public function update(Request $request, DocumentType $documentType)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:50',
        ]);
        
        $documentType->update($request->all());
        
        return redirect()->route('admin.document-types.index')
            ->with('success', 'ประเภทเอกสารถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified document type from storage.
     */
    public function destroy(DocumentType $documentType)
    {
        // Check if document type has documents
        if ($documentType->documents()->count() > 0) {
            return redirect()->route('admin.document-types.index')
                ->with('error', 'ไม่สามารถลบประเภทเอกสารได้เนื่องจากมีเอกสารที่เกี่ยวข้อง');
        }
        
        $documentType->delete();
        
        return redirect()->route('admin.document-types.index')
            ->with('success', 'ประเภทเอกสารถูกลบเรียบร้อยแล้ว');
    }
}
