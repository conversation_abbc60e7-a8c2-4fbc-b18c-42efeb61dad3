<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Material;

class MaterialController extends Controller
{
    /**
     * Display a listing of the materials.
     */
    public function index()
    {
        $materials = Material::withCount('items')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.materials.index', compact('materials'));
    }

    /**
     * Show the form for creating a new material.
     */
    public function create()
    {
        return view('admin.materials.create');
    }

    /**
     * Store a newly created material in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        Material::create($request->all());

        return redirect()->route('admin.materials')
            ->with('success', 'วัสดุถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified material.
     */
    public function show(Material $material)
    {
        $material->load(['items' => function ($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.materials.show', compact('material'));
    }

    /**
     * Show the form for editing the specified material.
     */
    public function edit(Material $material)
    {
        return view('admin.materials.edit', compact('material'));
    }

    /**
     * Update the specified material in storage.
     */
    public function update(Request $request, Material $material)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $material->update($request->all());

        return redirect()->route('admin.materials')
            ->with('success', 'วัสดุถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified material from storage.
     */
    public function destroy(Material $material)
    {
        // Check if material has items
        if ($material->items()->count() > 0) {
            return redirect()->route('admin.materials')
                ->with('error', 'ไม่สามารถลบวัสดุได้เนื่องจากมีรายการที่เกี่ยวข้อง');
        }

        $material->delete();

        return redirect()->route('admin.materials')
            ->with('success', 'วัสดุถูกลบเรียบร้อยแล้ว');
    }
}
