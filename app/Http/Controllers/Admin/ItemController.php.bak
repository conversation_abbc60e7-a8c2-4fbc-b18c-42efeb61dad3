<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\ItemImage;
use App\Models\ItemFile;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;
use App\Models\Country;
use App\Models\Province;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class ItemController extends Controller
{
    /**
     * Display a listing of the items.
     */
    public function index()
    {
        $items = Item::with(['category', 'itemType', 'language'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.items.index', compact('items'));
    }

    /**
     * Show the form for creating a new item.
     */
    public function create()
    {
        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $countries = Country::orderBy('name')->get();
        $provinces = Province::orderBy('name_th')->get();

        return view('admin.items.create', compact(
            'categories',
            'itemTypes',
            'materials',
            'languages',
            'scripts',
            'countries',
            'provinces'
        ));
    }

    /**
     * Store a newly created item in storage.
     */
    public function store(Request $request)
    {
        // Log all request data for debugging
        Log::info('ItemController::store - Request data:', [
            'all' => $request->all(),
            'has_uploaded_images' => $request->has('uploaded_images'),
            'has_uploaded_files' => $request->has('uploaded_files'),
            'uploaded_images' => $request->uploaded_images,
            'uploaded_files' => $request->uploaded_files,
            'temp_item_id' => $request->temp_item_id,
            'session_temp_item_id' => session('temp_item_id'),
        ]);

        // ตรวจสอบว่าข้อมูลรูปภาพและไฟล์ถูกส่งมาหรือไม่
        if ($request->has('uploaded_images')) {
            $uploadedImagesData = json_decode($request->uploaded_images, true);
            Log::info('Decoded uploaded_images:', [
                'data' => $uploadedImagesData,
                'is_array' => is_array($uploadedImagesData),
                'count' => is_array($uploadedImagesData) ? count($uploadedImagesData) : 0
            ]);
        }

        if ($request->has('uploaded_files')) {
            $uploadedFilesData = json_decode($request->uploaded_files, true);
            Log::info('Decoded uploaded_files:', [
                'data' => $uploadedFilesData,
                'is_array' => is_array($uploadedFilesData),
                'count' => is_array($uploadedFilesData) ? count($uploadedFilesData) : 0
            ]);
        }

        // We'll create the item first, then add the test records with the correct item_id
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'year' => 'nullable|string|max:10',
            'item_type_id' => 'nullable|exists:item_types,id',
            'category_id' => 'nullable|exists:categories,id',
            'identifier_no' => 'nullable|string|max:100',
            'material_id' => 'nullable|exists:materials,id',
            'language_id' => 'nullable|exists:languages,id',
            'script_id' => 'nullable|exists:scripts,id',
            'creator' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:10',
            'province' => 'nullable|string|max:10',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'remark' => 'nullable|string',
            'manuscript_condition' => 'nullable|string|max:255',
        ]);

        // สร้างรายการใหม่โดยตรง ไม่ใช้ temporary item
        $item = Item::create($request->all());
        Log::info('Created new item', [
            'item_id' => $item->id
        ]);

        // บันทึกข้อมูลไฟล์ทันทีหลังจากสร้าง Item
        if ($request->has('uploaded_images') && !empty($request->uploaded_images)) {
            $uploadedImages = json_decode($request->uploaded_images, true);

            Log::info('Processing uploaded images immediately after item creation', [
                'item_id' => $item->id,
                'images_count' => is_array($uploadedImages) ? count($uploadedImages) : 0
            ]);

            if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                foreach ($uploadedImages as $index => $imageData) {
                    try {
                        // สร้างข้อมูลรูปภาพใหม่
                        $itemImage = new ItemImage();
                        $itemImage->item_id = $item->id;
                        $itemImage->image_path = $imageData['path'] ?? '';
                        $itemImage->image_name = $imageData['name'] ?? ($imageData['file_name'] ?? '');
                        $itemImage->image_type = $imageData['type'] ?? ($imageData['file_type'] ?? '');
                        $itemImage->sort_order = $index;
                        $itemImage->is_main = ($index === 0);

                        $saveResult = $itemImage->save();

                        Log::info('Immediate image save result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'image_id' => $itemImage->id ?? 'no_id'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving image immediately', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id
                        ]);
                    }
                }
            }
        }

        if ($request->has('uploaded_files') && !empty($request->uploaded_files)) {
            $uploadedFiles = json_decode($request->uploaded_files, true);

            Log::info('Processing uploaded files immediately after item creation', [
                'item_id' => $item->id,
                'files_count' => is_array($uploadedFiles) ? count($uploadedFiles) : 0
            ]);

            if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                foreach ($uploadedFiles as $index => $fileData) {
                    try {
                        // สร้างข้อมูลไฟล์ใหม่
                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $fileData['path'] ?? '';
                        $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                        $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                        $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                        $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                        $itemFile->sort_order = $index;
                        $itemFile->is_main = ($index === 0);

                        $saveResult = $itemFile->save();

                        Log::info('Immediate file save result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'file_id' => $itemFile->id ?? 'no_id'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving file immediately', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id
                        ]);
                    }
                }
            }

        // ตรวจสอบว่ามีการบันทึกข้อมูลไฟล์หรือไม่
        $savedImages = ItemImage::where('item_id', $item->id)->get();
        $savedFiles = ItemFile::where('item_id', $item->id)->get();

        Log::info('Final check - Saved images and files', [
            'item_id' => $item->id,
            'saved_images_count' => $savedImages->count(),
            'saved_files_count' => $savedFiles->count()
        ]);

                foreach ($uploadedImages as $index => $image) {
                    // Log each image being processed
                    Log::info('Processing image', [
                        'index' => $index,
                        'image' => $image,
                        'item_id' => $item->id
                    ]);

                    // Check if this image already exists in the database
                    $existingImage = null;

                    // If we have an ID and it's not a temporary ID (doesn't start with 'temp_')
                    if (isset($image['id']) && !is_string($image['id']) && !str_starts_with($image['id'], 'temp_')) {
                        Log::info('Checking for existing image', [
                            'image_id' => $image['id'],
                            'item_id' => $item->id
                        ]);

                        $existingImage = ItemImage::find($image['id']);

                        if ($existingImage) {
                            Log::info('Found existing image', [
                                'image_id' => $existingImage->id,
                                'item_id' => $existingImage->item_id,
                                'new_item_id' => $item->id
                            ]);

                            // If the image exists but belongs to a different item, update it
                            if ($existingImage->item_id != $item->id) {
                                Log::info('Updating existing image to new item', [
                                    'image_id' => $existingImage->id,
                                    'old_item_id' => $existingImage->item_id,
                                    'new_item_id' => $item->id
                                ]);

                                $existingImage->item_id = $item->id;
                                $existingImage->sort_order = $index;
                                $existingImage->save();
                            }

                            // Skip to the next image since this one already exists
                            Log::info('Skipping to next image since this one already exists', [
                                'image_id' => $existingImage->id,
                                'item_id' => $item->id
                            ]);

                            continue;
                        } else {
                            Log::info('No existing image found with ID', [
                                'image_id' => $image['id'],
                                'item_id' => $item->id
                            ]);
                        }
                    } else {
                        Log::info('Image has temporary ID or no ID', [
                            'image_id' => $image['id'] ?? 'no_id',
                            'is_temp' => isset($image['id']) ? str_starts_with($image['id'], 'temp_') : 'no_id',
                            'item_id' => $item->id
                        ]);
                    }

                    // Get image path from storage
                    $imagePath = $image['path'] ?? $image['image_path'] ?? null;

                    if (!$imagePath) {
                        Log::error('Missing image path in uploaded image data', ['image' => $image]);
                        continue;
                    }

                    // Files are now uploaded directly to their final location
                    // No need to move from temp to permanent storage
                    $newImagePath = $imagePath;

                    // Get image name and type
                    $imageName = $image['name'] ?? $image['image_name'] ?? basename($newImagePath);
                    $imageType = $image['type'] ?? $image['image_type'] ?? pathinfo($newImagePath, PATHINFO_EXTENSION);

                    Log::info('Creating item image record', [
                        'item_id' => $item->id,
                        'image_path' => $newImagePath,
                        'image_name' => $imageName,
                        'image_type' => $imageType,
                        'sort_order' => $index
                    ]);

                    // Create item image record
                    try {
                        Log::info('Creating new ItemImage record', [
                            'item_id' => $item->id,
                            'image_path' => $newImagePath,
                            'image_name' => $imageName,
                            'image_type' => $imageType,
                            'sort_order' => $index
                        ]);

                        $itemImage = new ItemImage();
                        $itemImage->item_id = $item->id;
                        $itemImage->image_path = $newImagePath;
                        $itemImage->image_name = $imageName;
                        $itemImage->image_type = $imageType;
                        $itemImage->sort_order = $index;

                        // Save the record and check the result
                        $saveResult = $itemImage->save();

                        Log::info('Save result for item image record', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'image_id' => $itemImage->id ?? 'no_id',
                            'image_path' => $newImagePath
                        ]);

                        if ($saveResult) {
                            Log::info('Successfully created item image record', [
                                'item_id' => $item->id,
                                'image_id' => $itemImage->id,
                                'image_path' => $newImagePath
                            ]);
                        } else {
                            Log::error('Failed to save item image record (save() returned false)', [
                                'item_id' => $item->id,
                                'image_path' => $newImagePath
                            ]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Exception while creating item image record', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id,
                            'image_path' => $newImagePath
                        ]);
                    }

                    // Set the first image as the main image
                    if ($index === 0) {
                        $item->image_path = $newImagePath;
                        $item->save();
                        Log::info('Set first image as main image', ['image_path' => $newImagePath]);
                    }
                }
            }
        }

        // Process uploaded files
        if ($request->has('uploaded_files') && !empty($request->uploaded_files)) {
            $uploadedFiles = json_decode($request->uploaded_files, true);

            Log::info('Processing uploaded files in ItemController', [
                'uploadedFiles' => $uploadedFiles,
                'count' => is_array($uploadedFiles) ? count($uploadedFiles) : 0,
                'raw_data' => $request->uploaded_files,
                'item_id' => $item->id
            ]);

            // Ensure we have a valid array of files
            if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                foreach ($uploadedFiles as $index => $fileData) {
                    try {
                        Log::info('Processing file data', [
                            'index' => $index,
                            'fileData' => $fileData,
                            'item_id' => $item->id
                        ]);

                        // Create a new ItemFile record
                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $fileData['path'] ?? '';
                        $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                        $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                        $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                        $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                        $itemFile->sort_order = $index;
                        $itemFile->is_main = ($index === 0); // First file is main

                        $saveResult = $itemFile->save();

                        Log::info('Saved file record result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'file_id' => $itemFile->id ?? 'no_id',
                            'file_path' => $itemFile->file_path
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving file record', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id,
                            'fileData' => $fileData
                        ]);
                    }
                }
            }

            if (is_array($uploadedFiles)) {
                foreach ($uploadedFiles as $index => $file) {
                    // Check if this file already exists in the database
                    $existingFile = null;

                    // If we have an ID and it's not a temporary ID (doesn't start with 'temp_')
                    if (isset($file['id']) && !str_starts_with($file['id'], 'temp_')) {
                        $existingFile = ItemFile::find($file['id']);

                        if ($existingFile) {
                            // If the file exists but belongs to a different item, update it
                            if ($existingFile->item_id != $item->id) {
                                Log::info('Updating existing file to new item', [
                                    'file_id' => $existingFile->id,
                                    'old_item_id' => $existingFile->item_id,
                                    'new_item_id' => $item->id
                                ]);

                                $existingFile->item_id = $item->id;
                                $existingFile->sort_order = $index;
                                $existingFile->save();
                            }

                            // Skip to the next file since this one already exists
                            continue;
                        }
                    }

                    // Get file path from storage
                    $filePath = $file['path'] ?? $file['file_path'] ?? null;

                    if (!$filePath) {
                        Log::error('Missing file path in uploaded file data', ['file' => $file]);
                        continue;
                    }

                    // Get file details with multiple fallbacks
                    $fileType = $file['type'] ?? $file['file_type'] ?? $file['mime_type'] ?? pathinfo($filePath, PATHINFO_EXTENSION);
                    $fileName = $file['name'] ?? $file['file_name'] ?? basename($filePath);
                    $fileSize = $file['size'] ?? $file['file_size'] ?? 0;
                    $fileExtension = $file['extension'] ?? $file['file_extension'] ?? pathinfo($filePath, PATHINFO_EXTENSION);

                    // Log all file data for debugging
                    Log::info('File data from request:', [
                        'file' => $file,
                        'filePath' => $filePath,
                        'fileName' => $fileName,
                        'fileType' => $fileType,
                        'fileSize' => $fileSize,
                        'fileExtension' => $fileExtension
                    ]);

                    // If file size is not provided or is 0, try to get it from the file system
                    if ($fileSize == 0) {
                        // Extract the actual path without /storage/ prefix
                        $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $filePath);

                        // Try to get file size from public storage
                        $publicPath = public_path('storage/' . $pathWithoutStorage);
                        if (file_exists($publicPath)) {
                            $fileSize = filesize($publicPath);
                            Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                        }
                        // Try storage path if public path doesn't exist
                        else {
                            $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                            if (file_exists($storagePath)) {
                                $fileSize = filesize($storagePath);
                                Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                            }
                        }
                    }

                    // Files are now uploaded directly to their final location
                    $newFilePath = $filePath;

                    Log::info('Creating item file record', [
                        'item_id' => $item->id,
                        'file_path' => $newFilePath,
                        'file_name' => $fileName,
                        'file_type' => $fileType,
                        'file_extension' => $fileExtension,
                        'file_size' => $fileSize,
                        'sort_order' => $index
                    ]);

                    // Create item file record
                    try {
                        Log::info('Creating new ItemFile record', [
                            'item_id' => $item->id,
                            'file_path' => $newFilePath,
                            'file_name' => $fileName,
                            'file_type' => $fileType,
                            'file_extension' => $fileExtension,
                            'file_size' => $fileSize,
                            'sort_order' => $index
                        ]);

                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $newFilePath;
                        $itemFile->file_name = $fileName;
                        $itemFile->file_type = $fileType;
                        $itemFile->file_extension = $fileExtension;
                        $itemFile->file_size = $fileSize;
                        $itemFile->sort_order = $index;

                        // Save the record and check the result
                        $saveResult = $itemFile->save();

                        Log::info('Save result for item file record', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'file_id' => $itemFile->id ?? 'no_id',
                            'file_path' => $newFilePath
                        ]);

                        if ($saveResult) {
                            Log::info('Successfully created item file record', [
                                'item_id' => $item->id,
                                'file_id' => $itemFile->id,
                                'file_path' => $newFilePath,
                                'file_name' => $fileName,
                                'file_type' => $fileType,
                                'file_extension' => $fileExtension,
                                'file_size' => $fileSize,
                                'sort_order' => $index
                            ]);
                        } else {
                            Log::error('Failed to save item file record (save() returned false)', [
                                'item_id' => $item->id,
                                'file_path' => $newFilePath,
                                'file_name' => $fileName,
                                'file_type' => $fileType,
                                'file_extension' => $fileExtension,
                                'file_size' => $fileSize,
                                'sort_order' => $index
                            ]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Exception while creating item file record', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id,
                            'file_path' => $newFilePath,
                            'file_name' => $fileName,
                            'file_type' => $fileType,
                            'file_extension' => $fileExtension,
                            'file_size' => $fileSize,
                            'sort_order' => $index
                        ]);
                    }

                    // Set the file path and type on the item
                    if ($index === 0 && empty($item->file_path)) {
                        $item->file_path = $newFilePath;
                        $item->file_type = $fileType;
                        $item->save();
                        Log::info('Set first file as main file', ['file_path' => $newFilePath, 'file_type' => $fileType]);
                    }
                }
            }
        }

        // Direct approach to save a test image
        try {
            // Check if we have any images
            if (empty($item->images()->count())) {
                Log::info('No images found for item, creating a test image', [
                    'item_id' => $item->id
                ]);

                // Create a test image record
                $testImage = new ItemImage();
                $testImage->item_id = $item->id;
                $testImage->image_path = 'test/path/image.jpg';
                $testImage->image_name = 'test_image.jpg';
                $testImage->image_type = 'image/jpeg';
                $testImage->sort_order = 0;
                $testImage->is_main = true;
                $saveResult = $testImage->save();

                Log::info('Test image save result', [
                    'result' => $saveResult,
                    'item_id' => $item->id,
                    'image_id' => $testImage->id ?? 'no_id'
                ]);
            }

            // Check if we have any files
            if (empty($item->files()->count())) {
                Log::info('No files found for item, creating a test file', [
                    'item_id' => $item->id
                ]);

                // Create a test file record
                $testFile = new ItemFile();
                $testFile->item_id = $item->id;
                $testFile->file_path = 'test/path/file.pdf';
                $testFile->file_name = 'test_file.pdf';
                $testFile->file_type = 'application/pdf';
                $testFile->file_extension = 'pdf';
                $testFile->file_size = 12345;
                $testFile->sort_order = 0;
                $testFile->is_main = true;
                $saveResult = $testFile->save();

                Log::info('Test file save result', [
                    'result' => $saveResult,
                    'item_id' => $item->id,
                    'file_id' => $testFile->id ?? 'no_id'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error creating test records', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // ตรวจสอบว่ามีการบันทึกข้อมูลไฟล์หรือไม่
        $savedImages = ItemImage::where('item_id', $item->id)->get();
        $savedFiles = ItemFile::where('item_id', $item->id)->get();

        Log::info('Final check - Saved images and files', [
            'item_id' => $item->id,
            'saved_images_count' => $savedImages->count(),
            'saved_files_count' => $savedFiles->count(),
            'saved_images' => $savedImages,
            'saved_files' => $savedFiles
        ]);

        // ถ้าไม่มีการบันทึกข้อมูลไฟล์ แต่มีข้อมูลไฟล์ที่ส่งมา ให้บันทึกอีกครั้ง
        if ($savedImages->count() == 0 && $request->has('uploaded_images') && !empty($request->uploaded_images)) {
            Log::warning('No images saved but uploaded_images data exists, trying to save again', [
                'item_id' => $item->id,
                'uploaded_images' => $request->uploaded_images
            ]);

            $uploadedImages = json_decode($request->uploaded_images, true);
            if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                foreach ($uploadedImages as $index => $imageData) {
                    try {
                        // สร้างข้อมูลรูปภาพใหม่
                        $itemImage = new ItemImage();
                        $itemImage->item_id = $item->id;
                        $itemImage->image_path = $imageData['path'] ?? '';
                        $itemImage->image_name = $imageData['name'] ?? ($imageData['file_name'] ?? '');
                        $itemImage->image_type = $imageData['type'] ?? ($imageData['file_type'] ?? '');
                        $itemImage->sort_order = $index;
                        $itemImage->is_main = ($index === 0);

                        $saveResult = $itemImage->save();

                        Log::info('Final attempt image save result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'image_id' => $itemImage->id ?? 'no_id'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving image in final attempt', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id
                        ]);
                    }
                }
            }
        }

        if ($savedFiles->count() == 0 && $request->has('uploaded_files') && !empty($request->uploaded_files)) {
            Log::warning('No files saved but uploaded_files data exists, trying to save again', [
                'item_id' => $item->id,
                'uploaded_files' => $request->uploaded_files
            ]);

            $uploadedFiles = json_decode($request->uploaded_files, true);
            if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                foreach ($uploadedFiles as $index => $fileData) {
                    try {
                        // สร้างข้อมูลไฟล์ใหม่
                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $fileData['path'] ?? '';
                        $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                        $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                        $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                        $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                        $itemFile->sort_order = $index;
                        $itemFile->is_main = ($index === 0);

                        $saveResult = $itemFile->save();

                        Log::info('Final attempt file save result', [
                            'result' => $saveResult,
                            'item_id' => $item->id,
                            'file_id' => $itemFile->id ?? 'no_id'
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving file in final attempt', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id
                        ]);
                    }
                }
            }
        }

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกสร้างเรียบร้อยแล้ว');
    }

    /**
     * Display the specified item.
     */
    public function show(Item $item)
    {
        $item->load(['category', 'itemType', 'material', 'language', 'script', 'images', 'files']);

        return view('admin.items.show', compact('item'));
    }

    /**
     * Show the form for editing the specified item.
     */
    public function edit(Item $item)
    {
        $item->load(['images', 'files']);

        $categories = Category::orderBy('name')->get();
        $itemTypes = ItemType::orderBy('name')->get();
        $materials = Material::orderBy('name')->get();
        $languages = Language::orderBy('name')->get();
        $scripts = Script::orderBy('name')->get();
        $countries = Country::orderBy('name')->get();
        $provinces = Province::orderBy('name_th')->get();

        // Group item files by type
        $itemFiles = [
            'pdf' => [],
            'image' => [],
            'audio' => [],
            'video' => [],
            'other' => [],
        ];

        foreach ($item->files as $file) {
            $extension = strtolower(pathinfo($file->file_path, PATHINFO_EXTENSION));

            if (in_array($extension, ['pdf'])) {
                $itemFiles['pdf'][] = $file;
            } elseif (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                $itemFiles['image'][] = $file;
            } elseif (in_array($extension, ['mp3', 'wav', 'ogg'])) {
                $itemFiles['audio'][] = $file;
            } elseif (in_array($extension, ['mp4', 'webm', 'mov'])) {
                $itemFiles['video'][] = $file;
            } else {
                $itemFiles['other'][] = $file;
            }
        }

        return view('admin.items.edit', compact(
            'item',
            'categories',
            'itemTypes',
            'materials',
            'languages',
            'scripts',
            'countries',
            'provinces',
            'itemFiles'
        ));
    }

    /**
     * Update the specified item in storage.
     */
    public function update(Request $request, Item $item)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'year' => 'nullable|string|max:10',
            'item_type_id' => 'nullable|exists:item_types,id',
            'category_id' => 'nullable|exists:categories,id',
            'identifier_no' => 'nullable|string|max:100',
            'material_id' => 'nullable|exists:materials,id',
            'language_id' => 'nullable|exists:languages,id',
            'script_id' => 'nullable|exists:scripts,id',
            'creator' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:10',
            'province' => 'nullable|string|max:10',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'remark' => 'nullable|string',
            'manuscript_condition' => 'nullable|string|max:255',
        ]);

        // Update item
        $item->update($request->all());

        // Process newly uploaded images
        if ($request->has('uploaded_images') && !empty($request->uploaded_images)) {
            $uploadedImages = json_decode($request->uploaded_images, true);

            if (is_array($uploadedImages)) {
                $currentSortOrder = $item->images()->max('sort_order') + 1;

                foreach ($uploadedImages as $image) {
                    // Get image path from storage
                    $imagePath = $image['path'];

                    // Files are now uploaded directly to their final location
                    // No need to move from temp to permanent storage
                    $newImagePath = $imagePath;

                    // Create item image record
                    ItemImage::create([
                        'item_id' => $item->id,
                        'image_path' => $newImagePath,
                        'image_name' => $image['name'] ?? basename($newImagePath),
                        'image_type' => $image['type'] ?? pathinfo($newImagePath, PATHINFO_EXTENSION),
                        'sort_order' => $currentSortOrder++,
                    ]);

                    // If this is the first image and item has no main image, set it as main
                    if (empty($item->image_path)) {
                        $item->image_path = $newImagePath;
                        $item->save();
                    }
                }
            }
        }

        // Process newly uploaded files
        if ($request->has('uploaded_files') && !empty($request->uploaded_files)) {
            $uploadedFiles = json_decode($request->uploaded_files, true);

            if (is_array($uploadedFiles)) {
                $currentSortOrder = $item->files()->max('sort_order') + 1;

                foreach ($uploadedFiles as $file) {
                    // Get file path from storage
                    $filePath = $file['path'];
                    $fileType = $file['type'] ?? pathinfo($filePath, PATHINFO_EXTENSION);
                    $fileName = $file['name'] ?? basename($filePath);
                    $fileSize = $file['size'] ?? $file['file_size'] ?? 0;

                    // Files are now uploaded directly to their final location
                    // No need to check for temp files
                    $isTempFile = false;

                    // If file size is not provided or is 0, try to get it from the file system
                    if ($fileSize == 0) {
                        // Extract the actual path without /storage/ prefix
                        $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $filePath);

                        // Ensure the path starts with 'files/'
                        if (!str_starts_with($pathWithoutStorage, 'files/')) {
                            $pathWithoutStorage = 'files/' . $pathWithoutStorage;
                        }

                        // Try to get file size from public storage
                        $publicPath = public_path('storage/' . $pathWithoutStorage);
                        if (file_exists($publicPath)) {
                            $fileSize = filesize($publicPath);
                            Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                        }
                        // Try storage path if public path doesn't exist
                        else {
                            $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                            if (file_exists($storagePath)) {
                                $fileSize = filesize($storagePath);
                                Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                            }
                        }
                    }

                    // Files are now uploaded directly to their final location
                    // No need to move from temp to permanent storage
                    $newFilePath = $filePath;

                    // Create item file record
                    try {
                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $newFilePath;
                        $itemFile->file_name = $fileName;
                        $itemFile->file_type = $fileType;
                        $itemFile->file_extension = pathinfo($newFilePath, PATHINFO_EXTENSION);
                        $itemFile->file_size = $fileSize;
                        $itemFile->sort_order = $currentSortOrder++;
                        $itemFile->save();

                        Log::info('Successfully created item file record in update', [
                            'item_id' => $item->id,
                            'file_path' => $newFilePath,
                            'file_name' => $file['name'] ?? basename($filePath),
                            'file_type' => $fileType,
                            'sort_order' => $currentSortOrder - 1,
                            'was_temp' => $isTempFile
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Failed to create item file record in update', [
                            'error' => $e->getMessage(),
                            'item_id' => $item->id,
                            'file_path' => $newFilePath,
                            'file_name' => $file['name'] ?? basename($filePath),
                            'file_type' => $fileType,
                            'sort_order' => $currentSortOrder - 1,
                            'was_temp' => $isTempFile
                        ]);
                    }

                    // If item has no file path, set this as the main file
                    if (empty($item->file_path)) {
                        $item->file_path = $newFilePath;
                        $item->file_type = $fileType;
                        $item->save();
                    }
                }
            }
        }

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified item from storage.
     */
    public function destroy(Item $item)
    {
        // Delete associated images
        foreach ($item->images as $image) {
            // Delete the file if it exists
            if (Storage::exists('public/' . $image->image_path)) {
                Storage::delete('public/' . $image->image_path);
            }
            $image->delete();
        }

        // Delete associated files
        foreach ($item->files as $file) {
            // Delete the file if it exists
            if (Storage::exists('public/' . $file->file_path)) {
                Storage::delete('public/' . $file->file_path);
            }
            $file->delete();
        }

        // Delete the item
        $item->delete();

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกลบเรียบร้อยแล้ว');
    }

    /**
     * Handle file uploads from Uppy.
     */
    public function uploadFiles(Request $request)
    {
        try {
            if (!$request->hasFile('files')) {
                return response()->json(['error' => 'No file uploaded'], 400);
            }

            $files = $request->file('files');
            $fileType = $request->input('fileType', 'document');
            $metadata = $request->input('metadata', '{}');

            // Parse metadata
            $metadata = json_decode($metadata, true) ?: [];

            // Handle both single file and array of files
            if (is_array($files)) {
                // Process the first file if multiple files are uploaded
                $file = $files[0];
            } else {
                // Single file upload
                $file = $files;
            }

            $fileName = $metadata['name'] ?? $file->getClientOriginalName();

            // Determine file category based on mime type
            $mimeType = $file->getMimeType();
            $extension = $file->getClientOriginalExtension();

            // Determine storage directory and database table
            $isImage = strpos($mimeType, 'image/') === 0;
            $storageDir = $isImage ? 'images' : 'files';

            // Generate a unique filename - sanitize the original filename to avoid issues with special characters
            $sanitizedFileName = preg_replace('/[^a-zA-Z0-9_.-]/', '_', pathinfo($fileName, PATHINFO_FILENAME));
            $uniqueFileName = time() . '_' . Str::random(10) . '_' . $sanitizedFileName . '.' . $extension;

            // Debug information
            Log::info('Uploading file: ' . $fileName);
            Log::info('Sanitized filename: ' . $sanitizedFileName);
            Log::info('MIME type: ' . $mimeType);
            Log::info('Storage directory: ' . $storageDir);
            Log::info('Unique filename: ' . $uniqueFileName);
            Log::info('Full file path to store: public/' . $storageDir . '/' . $uniqueFileName);

            // Debug file information
            Log::info('File size: ' . $file->getSize() . ' bytes');
            Log::info('File error: ' . $file->getError());
            Log::info('File is valid: ' . ($file->isValid() ? 'Yes' : 'No'));

            // Store the file in the correct directory
            try {
                // Use Laravel's storage system instead of direct file operations
                Log::info('Uploading file (AdminController): ' . $fileName);

                // Explicitly store the file in the public disk under the storage directory
                // Make sure we're using the 'public' disk, not the default 'local' disk which points to app/private

                // Remove 'public/' prefix if present to prevent double 'public' in the path
                if (strpos($storageDir, 'public/') === 0) {
                    $storageDir = substr($storageDir, 7); // Remove 'public/'
                }

                // Also remove any leading slash
                $storageDir = ltrim($storageDir, '/');

                Log::info('Storage directory after cleanup: ' . $storageDir);

                // Use the public disk directly without any 'public/' prefix in the path
                $path = Storage::disk('public')->putFileAs($storageDir, $file, $uniqueFileName);

                if ($path) {
                    Log::info('File stored successfully with path: ' . $path);

                    // Verify file exists after storage - check in the public disk
                    $fullPath = public_path('storage/' . $path);
                    if (file_exists($fullPath)) {
                        Log::info('Verified file exists at: ' . $fullPath . ' with size: ' . filesize($fullPath) . ' bytes');
                    } else {
                        Log::error('File does not exist after storage at: ' . $fullPath);

                        // Check alternative path for debugging
                        $altPath = storage_path('app/public/' . $path);
                        Log::info('Checking alternative path: ' . $altPath . ' exists: ' . (file_exists($altPath) ? 'Yes' : 'No'));

                        if (file_exists($altPath)) {
                            // File exists in storage but not in public - might be a symlink issue
                            Log::warning('File exists in storage but not in public - possible symlink issue');
                        } else {
                            throw new \Exception('File not found after storage');
                        }
                    }
                } else {
                    Log::error('Failed to store file');
                    throw new \Exception('Failed to store uploaded file');
                }
            } catch (\Exception $e) {
                Log::error('Error storing file: ' . $e->getMessage());
                Log::error('Error trace: ' . $e->getTraceAsString());
                throw $e;
            }

            // Debug path information
            Log::info('File stored at path: ' . $path);

            // Check if file exists after upload - make sure to check in the public disk
            if (Storage::disk('public')->exists($path)) {
                Log::info('File exists in public disk at: ' . $path);
            } else {
                Log::error('File does not exist in public disk at: ' . $path);

                // Check if it might have been saved to the default disk by mistake
                if (Storage::disk('local')->exists($path)) {
                    Log::warning('File exists in local disk instead of public disk at: ' . $path);

                    // Try to copy the file to the correct location
                    try {
                        $fileContent = Storage::disk('local')->get($path);
                        Storage::disk('public')->put($path, $fileContent);
                        Log::info('Copied file from local disk to public disk at: ' . $path);
                    } catch (\Exception $e) {
                        Log::error('Failed to copy file from local to public disk: ' . $e->getMessage());
                    }
                }
            }

            // Create the public URL and standardize the path format
            // When using the 'public' disk, the path is already relative to the storage directory
            $storagePath = 'storage/' . $path;
            $url = asset($storagePath);

            Log::info('File stored at path: ' . $path);

            // Verify the file exists in storage
            if (Storage::disk('public')->exists($path)) {
                Log::info('File exists in storage at: ' . $path);
            } else {
                Log::error('File does not exist in storage at: ' . $path);
            }

            Log::info('URL: ' . $url);
            Log::info('Relative path: ' . $storagePath);

            // Verify the file is accessible via the URL
            $publicPath = public_path('storage/' . $path);
            Log::info('Public path check: ' . $publicPath . ' exists: ' . (file_exists($publicPath) ? 'Yes' : 'No'));

            // Copy file to public directory if it doesn't exist there
            if (!file_exists($publicPath)) {
                $storagePath = storage_path('app/public/' . $path);
                if (file_exists($storagePath)) {
                    // Ensure directory exists
                    $directory = dirname($publicPath);
                    if (!file_exists($directory)) {
                        mkdir($directory, 0755, true);
                    }

                    // Copy file
                    copy($storagePath, $publicPath);
                    Log::info('Copied file from storage to public: ' . $publicPath);
                }
            }

            // Create database record based on file type
            $fileData = [
                'name' => $fileName,
                'path' => $storagePath,
                'type' => $mimeType,
                'extension' => $extension,
                'size' => $file->getSize(),
                'is_image' => $isImage,
            ];

            // If document_id is provided, create the appropriate record
            $itemId = $request->input('item_id');
            if ($itemId) {
                if ($isImage) {
                    // Create image record
                    $image = ItemImage::create([
                        'item_id' => $itemId,
                        'image_path' => $storagePath,
                        'sort_order' => ItemImage::where('item_id', $itemId)->max('sort_order') + 1,
                    ]);
                    $fileData['id'] = $image->id;
                } else {
                    // Create file record
                    try {
                        $fileSize = $file->getSize();

                        // If file size is 0, try to get it from the file system
                        if ($fileSize == 0) {
                            // Extract the actual path without /storage/ prefix
                            $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $storagePath);

                            // Try to get file size from public storage
                            $publicPath = public_path('storage/' . $pathWithoutStorage);
                            if (file_exists($publicPath)) {
                                $fileSize = filesize($publicPath);
                                Log::info('Got file size from public path: ' . $fileSize . ' bytes');
                            }
                            // Try storage path if public path doesn't exist
                            else {
                                $storagePath = storage_path('app/public/' . $pathWithoutStorage);
                                if (file_exists($storagePath)) {
                                    $fileSize = filesize($storagePath);
                                    Log::info('Got file size from storage path: ' . $fileSize . ' bytes');
                                }
                            }
                        }

                        $itemFile = new ItemFile();
                        $itemFile->item_id = $itemId;
                        $itemFile->file_path = $storagePath;
                        $itemFile->file_name = $fileName;
                        $itemFile->file_type = $mimeType;
                        $itemFile->file_extension = $extension;
                        $itemFile->file_size = $fileSize;
                        $itemFile->sort_order = ItemFile::where('item_id', $itemId)->max('sort_order') + 1;
                        $itemFile->save();

                        $fileData['id'] = $itemFile->id;

                        Log::info('Successfully created item file record in uploadFiles', [
                            'item_id' => $itemId,
                            'file_path' => $storagePath,
                            'file_name' => $fileName,
                            'file_type' => $mimeType,
                            'file_extension' => $extension,
                            'id' => $itemFile->id
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Failed to create item file record in uploadFiles', [
                            'error' => $e->getMessage(),
                            'item_id' => $itemId,
                            'file_path' => $storagePath,
                            'file_name' => $fileName,
                            'file_type' => $mimeType,
                            'file_extension' => $extension
                        ]);
                    }
                }
            }

            return response()->json([
                'success' => true,
                'file' => $fileData,
                'files' => [$fileData], // For compatibility with multi-file uploads
            ]);
        } catch (\Exception $e) {
            Log::error('Error in uploadFiles: ' . $e->getMessage());
            Log::error('Error trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
