<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\Category;
use App\Models\ItemType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;
use App\Models\User;
use App\Models\ItemFile;
use App\Models\ItemImage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get counts for dashboard cards
        $stats = [
            'total_documents' => Item::count(),
            'total_categories' => Category::count(),
            'total_document_types' => ItemType::count(),
            'total_materials' => Material::count(),
            'total_languages' => Language::count(),
            'total_scripts' => Script::count(),
            'total_users' => User::count(),
            'total_views' => Item::sum('views'),
        ];

        // Get file statistics
        $fileStats = $this->getFileStats();

        // Get recent documents
        $recentDocuments = Item::with(['category', 'itemType'])
            ->latest()
            ->take(5)
            ->get();

        // Get documents by category for chart
        $documentsByCategory = Category::withCount('items')
            ->orderBy('items_count', 'desc')
            ->take(10)
            ->get();

        // Get documents by document type for chart
        $documentsByType = ItemType::withCount('items')
            ->orderBy('items_count', 'desc')
            ->take(10)
            ->get();

        // Get documents by year for chart
        $documentsByYear = Item::select(DB::raw('year, count(*) as count'))
            ->whereNotNull('year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->take(10)
            ->get();

        // Get documents by language for chart
        $documentsByLanguage = Language::withCount('items')
            ->orderBy('items_count', 'desc')
            ->take(10)
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'recentDocuments',
            'documentsByCategory',
            'documentsByType',
            'documentsByYear',
            'documentsByLanguage',
            'fileStats'
        ));
    }

    /**
     * Get file statistics for the dashboard
     */
    private function getFileStats()
    {
        // จำนวนและขนาดรูปภาพ
        $imageStats = $this->getDirectoryStats(['images', 'categories', 'item-types']);

        // จำนวนและขนาดไฟล์อื่นๆจากโฟลเดอร์
        $fileStats = $this->getDirectoryStats(['files']);

        // ดึงข้อมูลจำนวนไฟล์จากฐานข้อมูล
        $dbStats = [
            'items' => Item::count(),
            'images' => ItemImage::count(),
            'files' => ItemFile::count()
        ];

        // ดึงข้อมูลขนาดไฟล์จาก item_files
        $itemFilesSize = 0;
        $itemFilesCount = 0;

        $itemFiles = ItemFile::all();
        foreach ($itemFiles as $file) {
            if (Storage::disk('public')->exists($file->file_path)) {
                $itemFilesSize += Storage::disk('public')->size($file->file_path);
                $itemFilesCount++;
            }
        }

        // รวมขนาดและจำนวนไฟล์อื่นๆ (จากโฟลเดอร์ files และ item_files)
        $totalFileSize = $fileStats['size'] + $itemFilesSize;
        $totalFileCount = $fileStats['count'] + $itemFilesCount;

        // อัปเดตข้อมูลไฟล์อื่นๆ
        $fileStats = [
            'count' => $totalFileCount,
            'size' => $totalFileSize
        ];

        // รวมทั้งหมด
        $totalFiles = $imageStats['count'] + $fileStats['count'];
        $totalSize = $imageStats['size'] + $fileStats['size'];

        return [
            'images' => [
                'count' => $imageStats['count'],
                'size' => $imageStats['size'],
                'sizeFormatted' => $this->formatBytes($imageStats['size'])
            ],
            'files' => [
                'count' => $fileStats['count'],
                'size' => $fileStats['size'],
                'sizeFormatted' => $this->formatBytes($fileStats['size'])
            ],
            'total' => [
                'count' => $totalFiles,
                'size' => $totalSize,
                'sizeFormatted' => $this->formatBytes($totalSize)
            ],
            'dbStats' => $dbStats,
            'itemFiles' => [
                'count' => $itemFilesCount,
                'size' => $itemFilesSize,
                'sizeFormatted' => $this->formatBytes($itemFilesSize)
            ]
        ];
    }

    /**
     * ดึงข้อมูลสถิติไฟล์ในโฟลเดอร์ที่กำหนด
     */
    private function getDirectoryStats($directories)
    {
        $count = 0;
        $size = 0;

        foreach ($directories as $directory) {
            if (Storage::disk('public')->exists($directory)) {
                $files = Storage::disk('public')->allFiles($directory);

                foreach ($files as $file) {
                    // ข้ามไฟล์ .gitignore และไฟล์ซ่อนอื่นๆ
                    if (strpos(basename($file), '.') === 0) {
                        continue;
                    }

                    // ข้ามไฟล์ในโฟลเดอร์ defaults
                    if (strpos($file, 'defaults/') !== false) {
                        continue;
                    }

                    $count++;
                    $size += Storage::disk('public')->size($file);
                }
            }
        }

        return [
            'count' => $count,
            'size' => $size
        ];
    }

    /**
     * แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}
