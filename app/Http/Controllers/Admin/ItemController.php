<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Item;
use App\Models\ItemFile;
use App\Models\ItemImage;
use App\Models\ItemType;
use App\Models\Language;
use App\Models\Material;
use App\Models\Script;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ItemController extends Controller
{
    /**
     * Display a listing of the items.
     */
    public function index()
    {
        $items = Item::with(['itemType', 'category', 'material', 'language', 'script'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.items.index', compact('items'));
    }

    /**
     * Show the form for creating a new item.
     */
    public function create()
    {
        $itemTypes = ItemType::all();
        $categories = Category::all();
        $materials = Material::all();
        $languages = Language::all();
        $scripts = Script::all();

        return view('admin.items.create', compact('itemTypes', 'categories', 'materials', 'languages', 'scripts'));
    }

    /**
     * Store a newly created item in storage.
     */
    public function store(Request $request)
    {
        // Log all request data for debugging
        Log::info('ItemController::store - Request data:', [
            'all' => $request->all(),
            'has_uploaded_images' => $request->has('uploaded_images'),
            'has_uploaded_files' => $request->has('uploaded_files'),
            'uploaded_images' => $request->uploaded_images,
            'uploaded_files' => $request->uploaded_files
        ]);

        // Log request headers to check for any file-related headers
        Log::info('ItemController::store - Request headers:', [
            'content_type' => $request->header('Content-Type'),
            'accept' => $request->header('Accept'),
            'x_file_type' => $request->header('X-File-Type'),
            'x_csrf_token' => $request->header('X-CSRF-TOKEN')
        ]);

        // Debug log to a separate file for easier tracking
        file_put_contents(storage_path('logs/item_store_debug.log'),
            date('Y-m-d H:i:s') . " - ItemController::store called\n" .
            "Request data: " . json_encode($request->all(), JSON_PRETTY_PRINT) . "\n" .
            "Has uploaded_images: " . ($request->has('uploaded_images') ? 'Yes' : 'No') . "\n" .
            "Has uploaded_files: " . ($request->has('uploaded_files') ? 'Yes' : 'No') . "\n" .
            "uploaded_images: " . ($request->uploaded_images ?? 'null') . "\n" .
            "uploaded_files: " . ($request->uploaded_files ?? 'null') . "\n\n",
            FILE_APPEND);

        // ตรวจสอบว่าข้อมูลรูปภาพและไฟล์ถูกส่งมาหรือไม่
        $uploadedImagesData = [];
        if ($request->has('uploaded_images')) {
            try {
                // บันทึกข้อมูลดิบลง log เพื่อตรวจสอบ
                Log::debug('Raw uploaded_images data in store method:', [
                    'raw_data' => $request->uploaded_images,
                    'raw_data_type' => gettype($request->uploaded_images),
                    'request_all' => $request->all()
                ]);

                $uploadedImagesData = json_decode($request->uploaded_images, true);
                if (!is_array($uploadedImagesData)) {
                    Log::warning('uploaded_images is not a valid JSON array, setting to empty array');
                    Log::warning('JSON decode error: ' . json_last_error_msg());
                    $uploadedImagesData = [];
                }
            } catch (\Exception $e) {
                Log::error('Error decoding uploaded_images JSON: ' . $e->getMessage());
                Log::error('Exception trace: ' . $e->getTraceAsString());
                $uploadedImagesData = [];
            }

            Log::info('Decoded uploaded_images:', [
                'data' => $uploadedImagesData,
                'is_array' => is_array($uploadedImagesData),
                'count' => is_array($uploadedImagesData) ? count($uploadedImagesData) : 0,
                'first_item' => is_array($uploadedImagesData) && count($uploadedImagesData) > 0 ? $uploadedImagesData[0] : null
            ]);
        } else {
            Log::warning('No uploaded_images found in request');
            Log::debug('Request data:', [
                'has_uploaded_images' => $request->has('uploaded_images'),
                'request_all' => $request->all(),
                'request_keys' => array_keys($request->all())
            ]);
        }

        $uploadedFilesData = [];
        if ($request->has('uploaded_files')) {
            try {
                // บันทึกข้อมูลดิบลง log เพื่อตรวจสอบ
                Log::debug('Raw uploaded_files data in store method:', [
                    'raw_data' => $request->uploaded_files,
                    'raw_data_type' => gettype($request->uploaded_files),
                    'request_all' => $request->all()
                ]);

                $uploadedFilesData = json_decode($request->uploaded_files, true);
                if (!is_array($uploadedFilesData)) {
                    Log::warning('uploaded_files is not a valid JSON array, setting to empty array');
                    Log::warning('JSON decode error: ' . json_last_error_msg());
                    $uploadedFilesData = [];
                }
            } catch (\Exception $e) {
                Log::error('Error decoding uploaded_files JSON: ' . $e->getMessage());
                Log::error('Exception trace: ' . $e->getTraceAsString());
                $uploadedFilesData = [];
            }

            Log::info('Decoded uploaded_files:', [
                'data' => $uploadedFilesData,
                'is_array' => is_array($uploadedFilesData),
                'count' => is_array($uploadedFilesData) ? count($uploadedFilesData) : 0,
                'first_item' => is_array($uploadedFilesData) && count($uploadedFilesData) > 0 ? $uploadedFilesData[0] : null
            ]);
        } else {
            Log::warning('No uploaded_files found in request');
            Log::debug('Request data:', [
                'has_uploaded_files' => $request->has('uploaded_files'),
                'request_all' => $request->all(),
                'request_keys' => array_keys($request->all())
            ]);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'year' => 'nullable|string|max:10',
            'item_type_id' => 'nullable|exists:item_types,id',
            'category_id' => 'nullable|exists:categories,id',
            'identifier_no' => 'nullable|string|max:100',
            'material_id' => 'nullable|exists:materials,id',
            'language_id' => 'nullable|exists:languages,id',
            'script_id' => 'nullable|exists:scripts,id',
            'creator' => 'nullable|string|max:255',
            'author' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:10',
            'province' => 'nullable|string|max:10',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'remark' => 'nullable|string',
            'manuscript_condition' => 'nullable|string|max:255',
        ]);

        // สร้างรายการใหม่โดยตรง
        $itemData = $request->all();

        // Dump the raw request data to a file for debugging
        file_put_contents(storage_path('logs/item_store_raw_request.log'),
            date('Y-m-d H:i:s') . " - Raw request data\n" .
            "Content type: " . $request->header('Content-Type') . "\n" .
            "Request method: " . $request->method() . "\n" .
            "Request URI: " . $request->getRequestUri() . "\n" .
            "Request data: " . json_encode($request->all(), JSON_PRETTY_PRINT) . "\n" .
            "Request files: " . json_encode($request->allFiles(), JSON_PRETTY_PRINT) . "\n\n",
            FILE_APPEND);

        // ตรวจสอบว่ามีข้อมูลรูปภาพและไฟล์หรือไม่
        Log::info('Creating new item with data', [
            'has_uploaded_images' => isset($itemData['uploaded_images']),
            'has_uploaded_files' => isset($itemData['uploaded_files']),
            'item_data_keys' => array_keys($itemData),
            'title' => $itemData['title'] ?? 'No title',
            'description' => isset($itemData['description']) ? substr($itemData['description'], 0, 100) . '...' : 'No description'
        ]);

        // Remove uploaded_images and uploaded_files from itemData to prevent storing JSON in the item record
        $uploadedImagesBackup = $itemData['uploaded_images'] ?? null;
        $uploadedFilesBackup = $itemData['uploaded_files'] ?? null;

        if (isset($itemData['uploaded_images'])) {
            unset($itemData['uploaded_images']);
            Log::info('Removed uploaded_images from itemData before creating item');
        }

        if (isset($itemData['uploaded_files'])) {
            unset($itemData['uploaded_files']);
            Log::info('Removed uploaded_files from itemData before creating item');
        }

        try {
            $item = Item::create($itemData);
            Log::info('Created new item successfully', [
                'item_id' => $item->id,
                'title' => $item->title,
                'item_type_id' => $item->item_type_id,
                'category_id' => $item->category_id
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating new item: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e; // Re-throw to handle at higher level
        }

        // Restore the uploaded data for processing
        if ($uploadedImagesBackup) {
            $request->merge(['uploaded_images' => $uploadedImagesBackup]);
        }

        if ($uploadedFilesBackup) {
            $request->merge(['uploaded_files' => $uploadedFilesBackup]);
        }

        // บันทึกข้อมูลรูปภาพ
        if ($request->has('uploaded_images')) {
            try {
                // บันทึกข้อมูลดิบลง log เพื่อตรวจสอบ
                Log::debug('Raw uploaded_images data before saving to database:', [
                    'raw_data' => $request->uploaded_images,
                    'raw_data_type' => gettype($request->uploaded_images)
                ]);

                // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                $uploadedImagesStr = $request->uploaded_images;
                if (is_string($uploadedImagesStr)) {
                    // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                    if (json_decode($uploadedImagesStr) === null && json_last_error() !== JSON_ERROR_NONE) {
                        Log::error('Invalid JSON in uploaded_images: ' . json_last_error_msg());
                        // ลองแก้ไข JSON ที่ไม่ถูกต้อง
                        $uploadedImagesStr = str_replace("'", '"', $uploadedImagesStr);
                        $uploadedImagesStr = preg_replace('/([{,])(\s*)([^"}\s]+)(\s*):/i', '$1$2"$3"$4:', $uploadedImagesStr);
                        Log::info('Attempted to fix JSON: ' . substr($uploadedImagesStr, 0, 200) . '...');
                    }
                }

                $uploadedImages = json_decode($uploadedImagesStr, true);
                if (!is_array($uploadedImages)) {
                    Log::warning('uploaded_images is not a valid JSON array in save section, setting to empty array');
                    Log::warning('JSON decode error: ' . json_last_error_msg());
                    $uploadedImages = [];
                }
            } catch (\Exception $e) {
                Log::error('Error decoding uploaded_images JSON in save section: ' . $e->getMessage());
                Log::error('Exception trace: ' . $e->getTraceAsString());
                $uploadedImages = [];
            }

            // บันทึกข้อมูลลง log file และ Laravel log
            $logMessage = "Processing uploaded images: Item ID: " . $item->id . ", Images count: " .
                (is_array($uploadedImages) ? count($uploadedImages) : 0);

            file_put_contents(storage_path('logs/upload_debug.log'),
                date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                FILE_APPEND);

            Log::info($logMessage, [
                'item_id' => $item->id,
                'images_count' => is_array($uploadedImages) ? count($uploadedImages) : 0,
                'first_image' => is_array($uploadedImages) && count($uploadedImages) > 0 ? $uploadedImages[0] : null
            ]);

            if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                foreach ($uploadedImages as $index => $imageData) {
                    try {
                        // บันทึกข้อมูลลง log file และ Laravel log
                        $logMessage = "Processing image: Index: " . $index . ", Path: " . ($imageData['path'] ?? 'no_path');

                        file_put_contents(storage_path('logs/upload_debug.log'),
                            date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                            FILE_APPEND);

                        Log::info($logMessage, [
                            'index' => $index,
                            'image_data' => $imageData,
                            'item_id' => $item->id
                        ]);

                        // ตรวจสอบว่ามีข้อมูลที่จำเป็นหรือไม่
                        if (empty($imageData['path'])) {
                            $errorMsg = "Missing required path for image";
                            file_put_contents(storage_path('logs/upload_debug.log'),
                                date('Y-m-d H:i:s') . " - " . $errorMsg . "\n",
                                FILE_APPEND);
                            Log::error($errorMsg, ['index' => $index, 'image_data' => $imageData]);
                            continue;
                        }

                        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                        $imagePath = public_path($imageData['path']);
                        $storageImagePath = storage_path('app/public/' . str_replace('storage/', '', $imageData['path']));

                        Log::info('Checking if image file exists', [
                            'public_path' => $imagePath,
                            'public_exists' => file_exists($imagePath),
                            'storage_path' => $storageImagePath,
                            'storage_exists' => file_exists($storageImagePath)
                        ]);

                        // ตรวจสอบว่าเป็นรูปภาพหรือไม่
                        $fileType = $imageData['type'] ?? $imageData['file_type'] ?? '';
                        $fileExtension = $imageData['extension'] ?? $imageData['file_extension'] ?? '';
                        $detectedType = $imageData['detected_type'] ?? '';
                        $folder = $imageData['folder'] ?? '';

                        $isImageByType = strpos($fileType, 'image/') === 0;
                        $isImageByExt = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
                        $isImageByDetected = $detectedType === 'image';
                        $isImageByFolder = $folder === 'images';

                        $isImage = $isImageByType || $isImageByExt || $isImageByDetected || $isImageByFolder;

                        if (!$isImage) {
                            Log::warning('Skipping non-image file in images section: ' . ($imageData['name'] ?? 'unknown'));
                            continue;
                        }

                        // ตรวจสอบว่า path ของไฟล์อยู่ในโฟลเดอร์ images หรือไม่
                        $filePath = $imageData['path'] ?? '';
                        if (!empty($filePath) && !strpos($filePath, '/images/')) {
                            // ปรับปรุง path ให้อยู่ในโฟลเดอร์ images
                            $pathParts = explode('/', $filePath);
                            $fileName = end($pathParts);
                            $imageData['path'] = 'storage/images/' . $fileName;

                            Log::info('Adjusted image path to images folder in ItemController:', [
                                'original_path' => $filePath,
                                'new_path' => $imageData['path']
                            ]);
                        }

                        // สร้างข้อมูลรูปภาพใหม่
                        $itemImage = new ItemImage();
                        $itemImage->item_id = $item->id;
                        $itemImage->image_path = $imageData['path'] ?? '';
                        $itemImage->image_name = $imageData['name'] ?? ($imageData['file_name'] ?? '');
                        $itemImage->image_type = $imageData['type'] ?? ($imageData['file_type'] ?? '');
                        $itemImage->sort_order = $index;
                        $itemImage->is_main = ($index === 0);

                        // บันทึกข้อมูลโดยใช้ Eloquent
                        $saveResult = $itemImage->save();

                        // ถ้าบันทึกด้วย Eloquent ไม่สำเร็จ ให้ลองใช้ DB Query Builder
                        if (!$saveResult) {
                            Log::warning('Failed to save image with Eloquent, trying DB Query Builder', [
                                'item_id' => $item->id,
                                'image_path' => $imageData['path'] ?? ''
                            ]);

                            // บันทึกข้อมูลโดยตรงลงฐานข้อมูล
                            $result = \DB::table('item_images')->insert([
                                'item_id' => $item->id,
                                'image_path' => $imageData['path'] ?? '',
                                'image_name' => $imageData['name'] ?? ($imageData['file_name'] ?? ''),
                                'image_type' => $imageData['type'] ?? ($imageData['file_type'] ?? ''),
                                'sort_order' => $index,
                                'is_main' => ($index === 0) ? 1 : 0,
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);

                            // บันทึกข้อมูลลง log file และ Laravel log
                            $logMessage = "Image save with DB result: " . ($result ? 'success' : 'failed') . ", Item ID: " . $item->id;

                            file_put_contents(storage_path('logs/upload_debug.log'),
                                date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                                FILE_APPEND);

                            Log::info($logMessage, [
                                'result' => $result,
                                'item_id' => $item->id,
                                'image_path' => $imageData['path'] ?? ''
                            ]);
                        } else {
                            // บันทึกข้อมูลลง log file และ Laravel log
                            $logMessage = "Image save with Eloquent result: success, Image ID: " . $itemImage->id . ", Item ID: " . $item->id;

                            file_put_contents(storage_path('logs/upload_debug.log'),
                                date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                                FILE_APPEND);

                            Log::info($logMessage, [
                                'image_id' => $itemImage->id,
                                'item_id' => $item->id,
                                'image_path' => $itemImage->image_path
                            ]);
                        }
                    } catch (\Exception $e) {
                        // บันทึกข้อมูลลง log file และ Laravel log
                        $errorMsg = "Error saving image: " . $e->getMessage();

                        file_put_contents(storage_path('logs/upload_debug.log'),
                            date('Y-m-d H:i:s') . " - " . $errorMsg . "\n" . $e->getTraceAsString() . "\n",
                            FILE_APPEND);

                        Log::error($errorMsg, [
                            'exception' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id,
                            'index' => $index,
                            'image_data' => $imageData ?? 'No image data'
                        ]);
                    }
                }
            }
        }

        // บันทึกข้อมูลไฟล์
        if ($request->has('uploaded_files')) {
            try {
                // บันทึกข้อมูลดิบลง log เพื่อตรวจสอบ
                Log::debug('Raw uploaded_files data before saving to database:', [
                    'raw_data' => $request->uploaded_files,
                    'raw_data_type' => gettype($request->uploaded_files)
                ]);

                // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                $uploadedFilesStr = $request->uploaded_files;
                if (is_string($uploadedFilesStr)) {
                    // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                    if (json_decode($uploadedFilesStr) === null && json_last_error() !== JSON_ERROR_NONE) {
                        Log::error('Invalid JSON in uploaded_files: ' . json_last_error_msg());
                        // ลองแก้ไข JSON ที่ไม่ถูกต้อง
                        $uploadedFilesStr = str_replace("'", '"', $uploadedFilesStr);
                        $uploadedFilesStr = preg_replace('/([{,])(\s*)([^"}\s]+)(\s*):/i', '$1$2"$3"$4:', $uploadedFilesStr);
                        Log::info('Attempted to fix JSON: ' . substr($uploadedFilesStr, 0, 200) . '...');
                    }
                }

                $uploadedFiles = json_decode($uploadedFilesStr, true);
                if (!is_array($uploadedFiles)) {
                    Log::warning('uploaded_files is not a valid JSON array in save section, setting to empty array');
                    Log::warning('JSON decode error: ' . json_last_error_msg());
                    $uploadedFiles = [];
                }
            } catch (\Exception $e) {
                Log::error('Error decoding uploaded_files JSON in save section: ' . $e->getMessage());
                Log::error('Exception trace: ' . $e->getTraceAsString());
                $uploadedFiles = [];
            }

            // บันทึกข้อมูลลง log file และ Laravel log
            $logMessage = "Processing uploaded files: Item ID: " . $item->id . ", Files count: " .
                (is_array($uploadedFiles) ? count($uploadedFiles) : 0);

            file_put_contents(storage_path('logs/upload_debug.log'),
                date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                FILE_APPEND);

            Log::info($logMessage, [
                'item_id' => $item->id,
                'files_count' => is_array($uploadedFiles) ? count($uploadedFiles) : 0,
                'first_file' => is_array($uploadedFiles) && count($uploadedFiles) > 0 ? $uploadedFiles[0] : null
            ]);

            if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                foreach ($uploadedFiles as $index => $fileData) {
                    try {
                        // บันทึกข้อมูลลง log file และ Laravel log
                        $logMessage = "Processing file: Index: " . $index . ", Path: " . ($fileData['path'] ?? 'no_path');

                        file_put_contents(storage_path('logs/upload_debug.log'),
                            date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                            FILE_APPEND);

                        Log::info($logMessage, [
                            'index' => $index,
                            'file_data' => $fileData,
                            'item_id' => $item->id
                        ]);

                        // ตรวจสอบว่ามีข้อมูลที่จำเป็นหรือไม่
                        if (empty($fileData['path'])) {
                            $errorMsg = "Missing required path for file";
                            file_put_contents(storage_path('logs/upload_debug.log'),
                                date('Y-m-d H:i:s') . " - " . $errorMsg . "\n",
                                FILE_APPEND);
                            Log::error($errorMsg, ['index' => $index, 'file_data' => $fileData]);
                            continue;
                        }

                        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                        $filePath = public_path($fileData['path']);
                        $storageFilePath = storage_path('app/public/' . str_replace('storage/', '', $fileData['path']));

                        Log::info('Checking if file exists', [
                            'public_path' => $filePath,
                            'public_exists' => file_exists($filePath),
                            'storage_path' => $storageFilePath,
                            'storage_exists' => file_exists($storageFilePath)
                        ]);

                        // ตรวจสอบว่าเป็นไฟล์ (ไม่ใช่รูปภาพ) หรือไม่
                        $fileType = $fileData['type'] ?? $fileData['file_type'] ?? '';
                        $fileExtension = $fileData['extension'] ?? $fileData['file_extension'] ?? '';
                        $detectedType = $fileData['detected_type'] ?? '';
                        $folder = $fileData['folder'] ?? '';

                        $isImageByType = strpos($fileType, 'image/') === 0;
                        $isImageByExt = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
                        $isImageByDetected = $detectedType === 'image';
                        $isImageByFolder = $folder === 'images';

                        $isImage = $isImageByType || $isImageByExt || $isImageByDetected || $isImageByFolder;

                        if ($isImage) {
                            Log::warning('Skipping image file in files section: ' . ($fileData['name'] ?? 'unknown'));
                            continue;
                        }

                        // ตรวจสอบว่า path ของไฟล์อยู่ในโฟลเดอร์ files หรือไม่
                        $filePath = $fileData['path'] ?? '';
                        if (!empty($filePath) && !strpos($filePath, '/files/')) {
                            // ปรับปรุง path ให้อยู่ในโฟลเดอร์ files
                            $pathParts = explode('/', $filePath);
                            $fileName = end($pathParts);
                            $fileData['path'] = 'storage/files/' . $fileName;

                            Log::info('Adjusted file path to files folder in ItemController:', [
                                'original_path' => $filePath,
                                'new_path' => $fileData['path']
                            ]);
                        }

                        // สร้างข้อมูลไฟล์ใหม่
                        $itemFile = new ItemFile();
                        $itemFile->item_id = $item->id;
                        $itemFile->file_path = $fileData['path'] ?? '';
                        $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                        $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                        $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                        $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                        $itemFile->sort_order = $index;
                        $itemFile->is_main = ($index === 0);

                        // Log file data before saving
                        Log::info('File data before saving', [
                            'item_id' => $itemFile->item_id,
                            'file_path' => $itemFile->file_path,
                            'file_name' => $itemFile->file_name,
                            'file_type' => $itemFile->file_type,
                            'file_extension' => $itemFile->file_extension,
                            'file_size' => $itemFile->file_size,
                            'is_main' => $itemFile->is_main
                        ]);

                        // บันทึกข้อมูลโดยใช้ Eloquent
                        $saveResult = $itemFile->save();

                        // ถ้าบันทึกด้วย Eloquent ไม่สำเร็จ ให้ลองใช้ DB Query Builder
                        if (!$saveResult) {
                            Log::warning('Failed to save file with Eloquent, trying DB Query Builder', [
                                'item_id' => $item->id,
                                'file_path' => $fileData['path'] ?? ''
                            ]);

                            // บันทึกข้อมูลโดยตรงลงฐานข้อมูล
                            $result = \DB::table('item_files')->insert([
                                'item_id' => $item->id,
                                'file_path' => $fileData['path'] ?? '',
                                'file_name' => $fileData['name'] ?? ($fileData['file_name'] ?? ''),
                                'file_type' => $fileData['type'] ?? ($fileData['file_type'] ?? ''),
                                'file_extension' => $fileData['extension'] ?? ($fileData['file_extension'] ?? ''),
                                'file_size' => $fileData['size'] ?? ($fileData['file_size'] ?? 0),
                                'sort_order' => $index,
                                'is_main' => ($index === 0) ? 1 : 0,
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);

                            // บันทึกข้อมูลลง log file และ Laravel log
                            $logMessage = "File save with DB result: " . ($result ? 'success' : 'failed') . ", Item ID: " . $item->id;

                            file_put_contents(storage_path('logs/upload_debug.log'),
                                date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                                FILE_APPEND);

                            Log::info($logMessage, [
                                'result' => $result,
                                'item_id' => $item->id,
                                'file_path' => $fileData['path'] ?? ''
                            ]);
                        } else {
                            // บันทึกข้อมูลลง log file และ Laravel log
                            $logMessage = "File save with Eloquent result: success, File ID: " . $itemFile->id . ", Item ID: " . $item->id;

                            file_put_contents(storage_path('logs/upload_debug.log'),
                                date('Y-m-d H:i:s') . " - " . $logMessage . "\n",
                                FILE_APPEND);

                            Log::info($logMessage, [
                                'file_id' => $itemFile->id,
                                'item_id' => $item->id,
                                'file_path' => $itemFile->file_path,
                                'file_name' => $itemFile->file_name,
                                'file_type' => $itemFile->file_type
                            ]);
                        }
                    } catch (\Exception $e) {
                        // บันทึกข้อมูลลง log file และ Laravel log
                        $errorMsg = "Error saving file: " . $e->getMessage();

                        file_put_contents(storage_path('logs/upload_debug.log'),
                            date('Y-m-d H:i:s') . " - " . $errorMsg . "\n" . $e->getTraceAsString() . "\n",
                            FILE_APPEND);

                        Log::error($errorMsg, [
                            'exception' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'item_id' => $item->id,
                            'index' => $index,
                            'file_data' => $fileData ?? 'No file data'
                        ]);
                    }
                }
            }
        }

        // ตรวจสอบว่ามีการบันทึกข้อมูลไฟล์หรือไม่
        $savedImages = ItemImage::where('item_id', $item->id)->get();
        $savedFiles = ItemFile::where('item_id', $item->id)->get();

        // Check for Livewire components directly
        try {
            $livewireManager = app(\Livewire\LivewireManager::class);
            $components = $livewireManager->getComponentsByName();

            Log::info('Found Livewire components', [
                'components' => array_keys($components)
            ]);

            file_put_contents(storage_path('logs/item_store_debug.log'),
                date('Y-m-d H:i:s') . " - Found Livewire components\n" .
                "Components: " . implode(', ', array_keys($components)) . "\n\n",
                FILE_APPEND);
        } catch (\Exception $e) {
            Log::error('Error getting Livewire components: ' . $e->getMessage());
        }

        Log::info('Final check - Saved images and files', [
            'item_id' => $item->id,
            'saved_images_count' => $savedImages->count(),
            'saved_files_count' => $savedFiles->count()
        ]);

        // Debug log to a separate file for easier tracking
        file_put_contents(storage_path('logs/item_store_debug.log'),
            date('Y-m-d H:i:s') . " - Final check - Saved images and files\n" .
            "Item ID: " . $item->id . "\n" .
            "Saved images count: " . $savedImages->count() . "\n" .
            "Saved files count: " . $savedFiles->count() . "\n\n",
            FILE_APPEND);

        // Check if we need to get temporary images from Livewire components
        if ($savedImages->count() === 0 || $savedFiles->count() === 0) {
            Log::info('Checking for temporary images and files in Livewire components');

            // Try to get temporary images from the Livewire component
            try {
                $livewireManager = app(\Livewire\LivewireManager::class);
                $imageManager = null;
                $fileManager = null;

                // Try to find the ItemImageManager component
                try {
                    $imageManager = $livewireManager->getInstance('item-image-manager');
                    if ($imageManager) {
                        Log::info('Found ItemImageManager component', [
                            'tempImagesCount' => count($imageManager->tempImages)
                        ]);

                        file_put_contents(storage_path('logs/item_store_debug.log'),
                            date('Y-m-d H:i:s') . " - Found ItemImageManager component\n" .
                            "Temp images count: " . count($imageManager->tempImages) . "\n" .
                            "Temp images: " . json_encode($imageManager->tempImages, JSON_PRETTY_PRINT) . "\n\n",
                            FILE_APPEND);
                    }
                } catch (\Exception $e) {
                    Log::error('Error getting ItemImageManager component: ' . $e->getMessage());
                }

                // Try to find the ItemFileManager component
                try {
                    $fileManager = $livewireManager->getInstance('item-file-manager');
                    if ($fileManager) {
                        Log::info('Found ItemFileManager component', [
                            'tempFilesCount' => count($fileManager->tempFiles)
                        ]);

                        file_put_contents(storage_path('logs/item_store_debug.log'),
                            date('Y-m-d H:i:s') . " - Found ItemFileManager component\n" .
                            "Temp files count: " . count($fileManager->tempFiles) . "\n" .
                            "Temp files: " . json_encode($fileManager->tempFiles, JSON_PRETTY_PRINT) . "\n\n",
                            FILE_APPEND);
                    }
                } catch (\Exception $e) {
                    Log::error('Error getting ItemFileManager component: ' . $e->getMessage());
                }
            } catch (\Exception $e) {
                Log::error('Error accessing Livewire components: ' . $e->getMessage());
            }
        }

        // Try to get temporary images from the session
        try {
            $sessionTempImages = session('temp_images', []);
            $sessionTempFiles = session('temp_files', []);

            Log::info('Checking for temporary images and files in session', [
                'session_temp_images_count' => count($sessionTempImages),
                'session_temp_files_count' => count($sessionTempFiles)
            ]);

            file_put_contents(storage_path('logs/item_store_debug.log'),
                date('Y-m-d H:i:s') . " - Checking for temporary images and files in session\n" .
                "Session temp images count: " . count($sessionTempImages) . "\n" .
                "Session temp files count: " . count($sessionTempFiles) . "\n" .
                "Session temp images: " . json_encode($sessionTempImages, JSON_PRETTY_PRINT) . "\n" .
                "Session temp files: " . json_encode($sessionTempFiles, JSON_PRETTY_PRINT) . "\n\n",
                FILE_APPEND);

            // If we have temporary images in the session but none saved to the database, save them now
            if (count($sessionTempImages) > 0 && $savedImages->count() === 0) {
                Log::info('Found temporary images in session, saving to database');

                foreach ($sessionTempImages as $index => $imageData) {
                    try {
                        // Create item image record
                        $itemImage = ItemImage::create([
                            'item_id' => $item->id,
                            'image_path' => $imageData['path'] ?? $imageData['image_path'] ?? '',
                            'image_name' => $imageData['name'] ?? $imageData['image_name'] ?? '',
                            'image_type' => $imageData['type'] ?? $imageData['image_type'] ?? '',
                            'sort_order' => $index,
                            'is_main' => ($index === 0)
                        ]);

                        Log::info('Saved session temporary image to database', [
                            'image_id' => $itemImage->id,
                            'item_id' => $item->id,
                            'image_path' => $itemImage->image_path
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving session temporary image: ' . $e->getMessage());
                    }
                }

                // Refresh saved images
                $savedImages = ItemImage::where('item_id', $item->id)->get();
            }

            // If we have temporary files in the session but none saved to the database, save them now
            if (count($sessionTempFiles) > 0 && $savedFiles->count() === 0) {
                Log::info('Found temporary files in session, saving to database');

                foreach ($sessionTempFiles as $index => $fileData) {
                    try {
                        // Create item file record
                        $itemFile = ItemFile::create([
                            'item_id' => $item->id,
                            'file_path' => $fileData['path'] ?? $fileData['file_path'] ?? '',
                            'file_name' => $fileData['name'] ?? $fileData['file_name'] ?? '',
                            'file_type' => $fileData['type'] ?? $fileData['file_type'] ?? '',
                            'file_extension' => $fileData['extension'] ?? $fileData['file_extension'] ?? '',
                            'file_size' => $fileData['size'] ?? $fileData['file_size'] ?? 0,
                            'sort_order' => $index,
                            'is_main' => ($index === 0)
                        ]);

                        Log::info('Saved session temporary file to database', [
                            'file_id' => $itemFile->id,
                            'item_id' => $item->id,
                            'file_path' => $itemFile->file_path
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error saving session temporary file: ' . $e->getMessage());
                    }
                }

                // Refresh saved files
                $savedFiles = ItemFile::where('item_id', $item->id)->get();
            }
        } catch (\Exception $e) {
            Log::error('Error processing session temporary files: ' . $e->getMessage());
        }

        // ถ้าไม่มีข้อมูลรูปภาพและไฟล์ที่บันทึกแล้ว ให้ลองบันทึกโดยตรงจาก ItemImage::create และ ItemFile::create
        if ($savedImages->count() === 0 && $savedFiles->count() === 0) {
            Log::info('No saved images or files found, trying to save directly using create method');

            // ตรวจสอบว่ามีข้อมูลรูปภาพใน request หรือไม่
            if ($request->has('uploaded_images') && !empty($request->uploaded_images)) {
                try {
                    // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                    $uploadedImagesStr = $request->uploaded_images;
                    if (is_string($uploadedImagesStr)) {
                        // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                        if (json_decode($uploadedImagesStr) === null && json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('Invalid JSON in uploaded_images: ' . json_last_error_msg());
                            // ลองแก้ไข JSON ที่ไม่ถูกต้อง
                            $uploadedImagesStr = str_replace("'", '"', $uploadedImagesStr);
                            $uploadedImagesStr = preg_replace('/([{,])(\s*)([^"}\s]+)(\s*):/i', '$1$2"$3"$4:', $uploadedImagesStr);
                        }
                    }

                    $uploadedImages = json_decode($uploadedImagesStr, true);
                    if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                        Log::info('Found uploaded_images in request, saving directly with create method', [
                            'count' => count($uploadedImages)
                        ]);

                        foreach ($uploadedImages as $index => $imageData) {
                            if (!empty($imageData['path'])) {
                                try {
                                    // สร้างข้อมูลรูปภาพใหม่โดยใช้ create method
                                    $itemImage = ItemImage::create([
                                        'item_id' => $item->id,
                                        'image_path' => $imageData['path'] ?? '',
                                        'image_name' => $imageData['name'] ?? ($imageData['file_name'] ?? ''),
                                        'image_type' => $imageData['type'] ?? ($imageData['file_type'] ?? ''),
                                        'sort_order' => $index,
                                        'is_main' => ($index === 0) ? 1 : 0
                                    ]);

                                    Log::info('Saved image directly with create method', [
                                        'image_id' => $itemImage->id,
                                        'item_id' => $item->id
                                    ]);
                                } catch (\Exception $e) {
                                    Log::error('Error saving individual image with create method: ' . $e->getMessage());

                                    // ถ้าไม่สำเร็จ ให้ลองใช้ DB Query Builder
                                    try {
                                        $result = \DB::table('item_images')->insert([
                                            'item_id' => $item->id,
                                            'image_path' => $imageData['path'] ?? '',
                                            'image_name' => $imageData['name'] ?? ($imageData['file_name'] ?? ''),
                                            'image_type' => $imageData['type'] ?? ($imageData['file_type'] ?? ''),
                                            'sort_order' => $index,
                                            'is_main' => ($index === 0) ? 1 : 0,
                                            'created_at' => now(),
                                            'updated_at' => now()
                                        ]);

                                        Log::info('Saved image with DB query builder', [
                                            'result' => $result,
                                            'item_id' => $item->id
                                        ]);
                                    } catch (\Exception $e2) {
                                        Log::error('Error saving individual image with DB query builder: ' . $e2->getMessage());
                                    }
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error saving images from request data: ' . $e->getMessage());
                }
            }

            // ตรวจสอบว่ามีข้อมูลไฟล์ใน request หรือไม่
            if ($request->has('uploaded_files') && !empty($request->uploaded_files)) {
                try {
                    // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                    $uploadedFilesStr = $request->uploaded_files;
                    if (is_string($uploadedFilesStr)) {
                        // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                        if (json_decode($uploadedFilesStr) === null && json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('Invalid JSON in uploaded_files: ' . json_last_error_msg());
                            // ลองแก้ไข JSON ที่ไม่ถูกต้อง
                            $uploadedFilesStr = str_replace("'", '"', $uploadedFilesStr);
                            $uploadedFilesStr = preg_replace('/([{,])(\s*)([^"}\s]+)(\s*):/i', '$1$2"$3"$4:', $uploadedFilesStr);
                        }
                    }

                    $uploadedFiles = json_decode($uploadedFilesStr, true);
                    if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                        Log::info('Found uploaded_files in request, saving directly with create method', [
                            'count' => count($uploadedFiles)
                        ]);

                        foreach ($uploadedFiles as $index => $fileData) {
                            if (!empty($fileData['path'])) {
                                try {
                                    // สร้างข้อมูลไฟล์ใหม่โดยใช้ create method
                                    $itemFile = ItemFile::create([
                                        'item_id' => $item->id,
                                        'file_path' => $fileData['path'] ?? '',
                                        'file_name' => $fileData['name'] ?? ($fileData['file_name'] ?? ''),
                                        'file_type' => $fileData['type'] ?? ($fileData['file_type'] ?? ''),
                                        'file_extension' => $fileData['extension'] ?? ($fileData['file_extension'] ?? ''),
                                        'file_size' => $fileData['size'] ?? ($fileData['file_size'] ?? 0),
                                        'sort_order' => $index,
                                        'is_main' => ($index === 0) ? 1 : 0
                                    ]);

                                    Log::info('Saved file directly with create method', [
                                        'file_id' => $itemFile->id,
                                        'item_id' => $item->id
                                    ]);
                                } catch (\Exception $e) {
                                    Log::error('Error saving individual file with create method: ' . $e->getMessage());

                                    // ถ้าไม่สำเร็จ ให้ลองใช้ DB Query Builder
                                    try {
                                        $result = \DB::table('item_files')->insert([
                                            'item_id' => $item->id,
                                            'file_path' => $fileData['path'] ?? '',
                                            'file_name' => $fileData['name'] ?? ($fileData['file_name'] ?? ''),
                                            'file_type' => $fileData['type'] ?? ($fileData['file_type'] ?? ''),
                                            'file_extension' => $fileData['extension'] ?? ($fileData['file_extension'] ?? ''),
                                            'file_size' => $fileData['size'] ?? ($fileData['file_size'] ?? 0),
                                            'sort_order' => $index,
                                            'is_main' => ($index === 0) ? 1 : 0,
                                            'created_at' => now(),
                                            'updated_at' => now()
                                        ]);

                                        Log::info('Saved file with DB query builder', [
                                            'result' => $result,
                                            'item_id' => $item->id
                                        ]);
                                    } catch (\Exception $e2) {
                                        Log::error('Error saving individual file with DB query builder: ' . $e2->getMessage());
                                    }
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error saving files from request data: ' . $e->getMessage());
                }
            }

            // ตรวจสอบอีกครั้งว่ามีการบันทึกข้อมูลไฟล์หรือไม่
            $savedImages = ItemImage::where('item_id', $item->id)->get();
            $savedFiles = ItemFile::where('item_id', $item->id)->get();

            Log::info('After direct save with create method - Saved images and files', [
                'item_id' => $item->id,
                'saved_images_count' => $savedImages->count(),
                'saved_files_count' => $savedFiles->count()
            ]);
        }

        // ถ้าไม่มีข้อมูลรูปภาพและไฟล์ที่บันทึกแล้ว ให้ลองบันทึกอีกครั้งจาก request data
        if ($savedImages->count() === 0 && $savedFiles->count() === 0) {
            Log::info('No saved images or files found, trying to save directly from request data');

            // ตรวจสอบว่ามีข้อมูลรูปภาพใน request หรือไม่
            if ($request->has('uploaded_images') && !empty($request->uploaded_images)) {
                try {
                    // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                    $uploadedImagesStr = $request->uploaded_images;
                    if (is_string($uploadedImagesStr)) {
                        // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                        if (json_decode($uploadedImagesStr) === null && json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('Invalid JSON in uploaded_images: ' . json_last_error_msg());
                            // ลองแก้ไข JSON ที่ไม่ถูกต้อง
                            $uploadedImagesStr = str_replace("'", '"', $uploadedImagesStr);
                            $uploadedImagesStr = preg_replace('/([{,])(\s*)([^"}\s]+)(\s*):/i', '$1$2"$3"$4:', $uploadedImagesStr);
                        }
                    }

                    $uploadedImages = json_decode($uploadedImagesStr, true);
                    if (is_array($uploadedImages) && count($uploadedImages) > 0) {
                        Log::info('Found uploaded_images in request, saving directly', [
                            'count' => count($uploadedImages)
                        ]);

                        foreach ($uploadedImages as $index => $imageData) {
                            if (!empty($imageData['path'])) {
                                try {
                                    // สร้างข้อมูลรูปภาพใหม่
                                    $itemImage = new ItemImage();
                                    $itemImage->item_id = $item->id;
                                    $itemImage->image_path = $imageData['path'] ?? '';
                                    $itemImage->image_name = $imageData['name'] ?? ($imageData['file_name'] ?? '');
                                    $itemImage->image_type = $imageData['type'] ?? ($imageData['file_type'] ?? '');
                                    $itemImage->sort_order = $index;
                                    $itemImage->is_main = ($index === 0);
                                    $saveResult = $itemImage->save();

                                    if (!$saveResult) {
                                        // บันทึกข้อมูลโดยตรงลงฐานข้อมูล
                                        $result = \DB::table('item_images')->insert([
                                            'item_id' => $item->id,
                                            'image_path' => $imageData['path'] ?? '',
                                            'image_name' => $imageData['name'] ?? ($imageData['file_name'] ?? ''),
                                            'image_type' => $imageData['type'] ?? ($imageData['file_type'] ?? ''),
                                            'sort_order' => $index,
                                            'is_main' => ($index === 0) ? 1 : 0,
                                            'created_at' => now(),
                                            'updated_at' => now()
                                        ]);

                                        Log::info('Saved image with DB query builder', [
                                            'result' => $result,
                                            'item_id' => $item->id
                                        ]);
                                    } else {
                                        Log::info('Saved image directly from request data', [
                                            'image_id' => $itemImage->id,
                                            'item_id' => $item->id
                                        ]);
                                    }
                                } catch (\Exception $e) {
                                    Log::error('Error saving individual image: ' . $e->getMessage());
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error saving images from request data: ' . $e->getMessage());
                }
            }

            // ตรวจสอบว่ามีข้อมูลไฟล์ใน request หรือไม่
            if ($request->has('uploaded_files') && !empty($request->uploaded_files)) {
                try {
                    // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                    $uploadedFilesStr = $request->uploaded_files;
                    if (is_string($uploadedFilesStr)) {
                        // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                        if (json_decode($uploadedFilesStr) === null && json_last_error() !== JSON_ERROR_NONE) {
                            Log::error('Invalid JSON in uploaded_files: ' . json_last_error_msg());
                            // ลองแก้ไข JSON ที่ไม่ถูกต้อง
                            $uploadedFilesStr = str_replace("'", '"', $uploadedFilesStr);
                            $uploadedFilesStr = preg_replace('/([{,])(\s*)([^"}\s]+)(\s*):/i', '$1$2"$3"$4:', $uploadedFilesStr);
                        }
                    }

                    $uploadedFiles = json_decode($uploadedFilesStr, true);
                    if (is_array($uploadedFiles) && count($uploadedFiles) > 0) {
                        Log::info('Found uploaded_files in request, saving directly', [
                            'count' => count($uploadedFiles)
                        ]);

                        foreach ($uploadedFiles as $index => $fileData) {
                            if (!empty($fileData['path'])) {
                                try {
                                    // สร้างข้อมูลไฟล์ใหม่
                                    $itemFile = new ItemFile();
                                    $itemFile->item_id = $item->id;
                                    $itemFile->file_path = $fileData['path'] ?? '';
                                    $itemFile->file_name = $fileData['name'] ?? ($fileData['file_name'] ?? '');
                                    $itemFile->file_type = $fileData['type'] ?? ($fileData['file_type'] ?? '');
                                    $itemFile->file_extension = $fileData['extension'] ?? ($fileData['file_extension'] ?? '');
                                    $itemFile->file_size = $fileData['size'] ?? ($fileData['file_size'] ?? 0);
                                    $itemFile->sort_order = $index;
                                    $itemFile->is_main = ($index === 0);
                                    $saveResult = $itemFile->save();

                                    if (!$saveResult) {
                                        // บันทึกข้อมูลโดยตรงลงฐานข้อมูล
                                        $result = \DB::table('item_files')->insert([
                                            'item_id' => $item->id,
                                            'file_path' => $fileData['path'] ?? '',
                                            'file_name' => $fileData['name'] ?? ($fileData['file_name'] ?? ''),
                                            'file_type' => $fileData['type'] ?? ($fileData['file_type'] ?? ''),
                                            'file_extension' => $fileData['extension'] ?? ($fileData['file_extension'] ?? ''),
                                            'file_size' => $fileData['size'] ?? ($fileData['file_size'] ?? 0),
                                            'sort_order' => $index,
                                            'is_main' => ($index === 0) ? 1 : 0,
                                            'created_at' => now(),
                                            'updated_at' => now()
                                        ]);

                                        Log::info('Saved file with DB query builder', [
                                            'result' => $result,
                                            'item_id' => $item->id
                                        ]);
                                    } else {
                                        Log::info('Saved file directly from request data', [
                                            'file_id' => $itemFile->id,
                                            'item_id' => $item->id
                                        ]);
                                    }
                                } catch (\Exception $e) {
                                    Log::error('Error saving individual file: ' . $e->getMessage());
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error saving files from request data: ' . $e->getMessage());
                }
            }

            // ตรวจสอบอีกครั้งว่ามีการบันทึกข้อมูลไฟล์หรือไม่
            $savedImages = ItemImage::where('item_id', $item->id)->get();
            $savedFiles = ItemFile::where('item_id', $item->id)->get();

            Log::info('After direct save - Saved images and files', [
                'item_id' => $item->id,
                'saved_images_count' => $savedImages->count(),
                'saved_files_count' => $savedFiles->count()
            ]);
        }

        return redirect()->route('admin.items')
            ->with('success', 'รายการถูกสร้างเรียบร้อยแล้ว');
    }

    // ส่วนที่เหลือของคลาสยังคงเหมือนเดิม
}
