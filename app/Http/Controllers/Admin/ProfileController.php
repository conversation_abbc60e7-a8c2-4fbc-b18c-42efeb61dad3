<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProfileController extends Controller
{
    /**
     * Show the form for editing the user's profile.
     */
    public function edit()
    {
        $user = Auth::user();
        
        return view('admin.profile.edit', compact('user'));
    }

    /**
     * Update the user's profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'profile_image' => 'nullable|image|max:2048',
            'bio' => 'nullable|string',
        ]);
        
        $user->name = $request->name;
        $user->email = $request->email;
        $user->bio = $request->bio;
        
        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            // Delete old image if exists
            if ($user->profile_image && Storage::exists('public/' . $user->profile_image)) {
                Storage::delete('public/' . $user->profile_image);
            }
            
            $image = $request->file('profile_image');
            $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('public/profiles', $imageName);
            $user->profile_image = str_replace('public/', '', $path);
        }
        
        $user->save();
        
        return redirect()->route('admin.profile')
            ->with('success', 'โปรไฟล์ถูกอัปเดตเรียบร้อยแล้ว');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'current_password' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);
        
        // Check current password
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->route('admin.profile')
                ->with('error', 'รหัสผ่านปัจจุบันไม่ถูกต้อง');
        }
        
        $user->password = Hash::make($request->password);
        $user->save();
        
        return redirect()->route('admin.profile')
            ->with('success', 'รหัสผ่านถูกอัปเดตเรียบร้อยแล้ว');
    }
}
