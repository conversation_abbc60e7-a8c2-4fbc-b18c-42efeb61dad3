<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Item;
use App\Models\ItemType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;

class ApiItemController extends Controller
{
    /**
     * Return all items in JSON format
     */
    public function index()
    {
        $items = Item::with(['itemType', 'material', 'language', 'script'])
            ->orderBy('created_at', 'desc')
            ->get();

        $result = [];

        foreach ($items as $item) {
            // Get the first image
            $imagePath = $item->getFirstImageAttribute();
            $imageUrl = '';

            // Only set image URL if it's not the default image
            if ($imagePath !== 'defaults/item-default.svg' && $imagePath !== 'defaults/document-default.svg') {
                $imageUrl = get_image_url($imagePath);
            }

            // Get item type name
            $itemType = null;
            if ($item->item_type_id) {
                $itemType = ItemType::find($item->item_type_id);
            }

            // Get material name
            $material = null;
            if ($item->material_id) {
                $material = Material::find($item->material_id);
            }

            // Get language name
            $language = null;
            if ($item->language_id) {
                $language = Language::find($item->language_id);
            }

            // Get script name
            $script = null;
            if ($item->script_id) {
                $script = Script::find($item->script_id);
            }

            $result[] = [
                'id' => $item->identifier_no ?? $item->id,
                'title' => $item->title,
                'alt.title' => $item->other_title1 ?? '',
                'type' => $itemType ? $itemType->name : '',
                'subject' => $item->category ? $item->category->name : '',
                'language' => $language ? $language->name : '',
                'script' => $script ? $script->name : '',
                'author' => $item->author ?? '',
                'year' => $item->year ?? '',
                'description' => $item->description,
                'spatial' => $item->location ?? '',
                'country' => $item->country ?? '',
                'province' => $item->province_name ?? '',
                'source.uri' => route('items.show', $item->id),
                'image' => $imageUrl,
            ];
        }

        return response()->json([
            'status' => 200,
            'result' => $result
        ]);
    }

    /**
     * Return a specific item in JSON format
     */
    public function show($id)
    {
        $item = Item::with(['itemType', 'material', 'language', 'script', 'images'])
            ->findOrFail($id);

        // Get the first image
        $imagePath = $item->getFirstImageAttribute();
        $imageUrl = '';

        // Only set image URL if it's not one of the default images
        if ($imagePath !== 'defaults/item-default.svg' &&
            $imagePath !== 'defaults/document-default.svg' &&
            $imagePath !== 'defaults/item-no-file.svg' &&
            $imagePath !== 'defaults/item-no-file-th.svg') {
            $imageUrl = get_image_url($imagePath);
        }

        // Get item type name
        $itemType = null;
        if ($item->item_type_id) {
            $itemType = ItemType::find($item->item_type_id);
        }

        // Get material name
        $material = null;
        if ($item->material_id) {
            $material = Material::find($item->material_id);
        }

        // Get language name
        $language = null;
        if ($item->language_id) {
            $language = Language::find($item->language_id);
        }

        // Get script name
        $script = null;
        if ($item->script_id) {
            $script = Script::find($item->script_id);
        }

        // Get all images
        $images = [];
        foreach ($item->images as $image) {
            // Skip default images
            if ($image->image_path !== 'defaults/item-default.svg' && $image->image_path !== 'defaults/document-default.svg') {
                $images[] = get_image_url($image->image_path);
            }
        }

        // Get all files
        $files = [];
        foreach ($item->files as $file) {
            $files[] = [
                'name' => $file->file_name,
                'path' => url('storage/' . $file->file_path),
                'type' => $file->file_type,
                'size' => $file->file_size,
                'is_main' => $file->is_main,
            ];
        }

        $result = [
            'id' => $item->identifier_no ?? $item->id,
            'title' => $item->title,
            'alt.title' => $item->other_title1 ?? '',
            'type' => $itemType ? $itemType->name : '',
            'subject' => $item->category ? $item->category->name : '',
            'language' => $language ? $language->name : '',
            'script' => $script ? $script->name : '',
            'author' => $item->author ?? '',
            'year' => $item->year ?? '',
            'description' => $item->description,
            'spatial' => $item->location ?? '',
            'country' => $item->country ?? '',
            'province' => $item->province_name ?? '',
            'source.uri' => route('items.show', $item->id),
            'image' => $imageUrl,
            'images' => $images,
            'files' => $files,
            'created_at' => $item->created_at,
            'updated_at' => $item->updated_at,
        ];

        return response()->json([
            'status' => 200,
            'result' => [$result]
        ]);
    }
}
