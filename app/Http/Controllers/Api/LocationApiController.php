<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\Province;
use App\Models\Amphure;
use App\Models\District;
use App\Models\Geography;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class LocationApiController extends Controller
{
    /**
     * Get all geographies.
     */
    public function getGeographies()
    {
        // Cache geographies for 24 hours
        $geographies = Cache::remember('geographies', 86400, function () {
            return Geography::with('country')->orderBy('name')->get();
        });

        // Log the geographies for debugging
        \Log::info('Geographies retrieved: ' . $geographies->count());
        \Log::info($geographies->toJson());

        return response()->json($geographies);
    }

    /**
     * Get all countries.
     */
    public function getCountries()
    {
        // Cache countries for 24 hours
        $countries = Cache::remember('countries', 86400, function () {
            return Country::orderBy('name')->get();
        });

        return response()->json($countries);
    }

    /**
     * Get provinces by country code.
     */
    public function getProvincesByCountry($countryCode)
    {
        // Log the request for debugging
        \Log::info('API: Provinces requested for country code: ' . $countryCode);

        // Cache provinces for 24 hours
        $provinces = Cache::remember('provinces_' . $countryCode, 86400, function () use ($countryCode) {
            $provinces = Province::where('country_code', $countryCode)
                ->orderBy('name_th')
                ->get(['id', 'name_th', 'name_en', 'code']);

            // Log the provinces for debugging
            \Log::info('API: Provinces retrieved for country ' . $countryCode . ': ' . $provinces->count());
            \Log::info($provinces->toJson());

            return $provinces;
        });

        return response()->json($provinces);
    }

    /**
     * Get amphures (districts) by province ID.
     */
    public function getAmphuresByProvince($provinceId)
    {
        // Log the request for debugging
        \Log::info('API: Amphures requested for province ID: ' . $provinceId);

        // Cache amphures for 24 hours
        $amphures = Cache::remember('amphures_' . $provinceId, 86400, function () use ($provinceId) {
            $amphures = Amphure::where('province_id', $provinceId)
                ->orderBy('name_th')
                ->get();

            // Log the amphures for debugging
            \Log::info('API: Amphures retrieved for province ' . $provinceId . ': ' . $amphures->count());

            return $amphures;
        });

        return response()->json($amphures);
    }

    /**
     * Get districts (sub-districts) by amphure ID.
     */
    public function getDistrictsByAmphure($amphureId)
    {
        // Log the request for debugging
        \Log::info('API: Districts requested for amphure ID: ' . $amphureId);

        // Cache districts for 24 hours
        $districts = Cache::remember('districts_' . $amphureId, 86400, function () use ($amphureId) {
            $districts = District::where('amphure_id', $amphureId)
                ->orderBy('name_th')
                ->get();

            // Log the districts for debugging
            \Log::info('API: Districts retrieved for amphure ' . $amphureId . ': ' . $districts->count());

            return $districts;
        });

        return response()->json($districts);
    }
}
