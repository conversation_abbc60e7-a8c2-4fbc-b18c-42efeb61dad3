<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    public function index()
    {
        try {
            // ดึงรายการที่เผยแพร่แล้ว หรือทั้งหมดถ้าไม่มี status field
            $items = Item::orderBy('updated_at', 'desc')->get();

            $categories = Category::orderBy('updated_at', 'desc')->get();

            $content = view('sitemap.index', compact('items', 'categories'))->render();

            return response($content, 200)
                ->header('Content-Type', 'text/xml');
        } catch (\Exception $e) {
            \Log::error('Sitemap generation error: ' . $e->getMessage());

            // Return basic sitemap if error occurs
            $basicSitemap = '<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>' . route('home') . '</loc>
        <lastmod>' . now()->toISOString() . '</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
</urlset>';

            return response($basicSitemap, 200)
                ->header('Content-Type', 'text/xml');
        }
    }
}
