<?php
// Set up autoloading
require __DIR__ . '/vendor/autoload.php';

// Load the .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Create a new Laravel application
$app = require_once __DIR__ . '/bootstrap/app.php';

// Bootstrap the application
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

echo "Starting category ID update process...\n";

// Get all items with identifier_no
$items = DB::table('items')
    ->whereNotNull('identifier_no')
    ->get();

echo "Found " . $items->count() . " items with identifier_no.\n";

// Get all categories
$categories = DB::table('categories')
    ->select('id', 'name')
    ->get();

echo "Found " . $categories->count() . " categories.\n";

// Create a complete mapping for manuscript category codes to category IDs
// Based on the SQL insert statements provided
$categoryMapping = [
    'A-1' => 1,  // พระไตรปิฎก - พระวินัยปิฎก
    'A-2' => 2,  // พระไตรปิฎก - พระสุดตันตปีฎก
    'A-3' => 3,  // พระไตรปิฎก - พระอภิธรรมปีฎก
    'A-4' => 4,  // พระไตรปิฎกแบบย่อ
    'A-5' => 5,  // อรรภกถา ฎีกา และปกรณ์วิเสสต่างๆ
    'A-6' => 6,  // ชาดก
    'A-7' => 7,  // ธรรมทั่วไป
    'C-1' => 8,  // ตำนาน-พุทธตำนาน
    'B' => 9,    // ประวัติศาสตร์
    'C-2' => 10, // ตำนาน-พระสาวก
    'C-3' => 11, // ตำนานปูชนียสถาน - ปูชนียวัตถุ
    'C-4' => 12, // ตำนาน-บุคคลสำคัญ
    'F' => 13,   // นิทานพื้นบ้าน
    'G' => 14,   // การพยากรณ์เหตุการณ์อนาคต
    'H' => 15,   // โหราศาสตร์
    'I' => 16,   // จักรวาลวิทยา
    'J' => 17,   // ไสยศาสตร์
    'E' => 18,   // จริยศาสตร์
    'D' => 19,   // อานิสงส์
    'K' => 20,   // ภาษาศาสตร์
    'L' => 21,   // กฎหมาย
    'M' => 22,   // เวชศาสตร์
    'P' => 23,   // กวีนิพนธ์
    'Q' => 24,   // บทสวด, คำไหวต่างๆ
    'R' => 25,   // ปกิณกะ
    'O' => 26,   // พิธีกรรมสงฆ์
    'N' => 27,   // พิธีกรรมท้องถิ่น
];

echo "Created category mapping with " . count($categoryMapping) . " entries.\n";

// Display some sample category mappings
echo "Sample category mappings:\n";
$i = 0;
foreach ($categoryMapping as $code => $id) {
    $categoryName = "Unknown";
    foreach ($categories as $category) {
        if ($category->id == $id) {
            $categoryName = $category->name;
            break;
        }
    }
    echo "  - Code: $code, ID: $id, Name: $categoryName\n";
    $i++;
    if ($i >= 5) break;
}

// Connect to the culture_digitalcollection database
try {
    $manuscriptDb = DB::connection('mysql2');
    echo "Connected to culture_digitalcollection database.\n";
} catch (\Exception $e) {
    echo "Error connecting to culture_digitalcollection database: " . $e->getMessage() . "\n";
    echo "Checking if the connection is defined in config/database.php...\n";
    
    // Check if the connection is defined
    $connections = config('database.connections');
    if (!isset($connections['mysql2'])) {
        echo "The mysql2 connection is not defined in config/database.php.\n";
        echo "Adding the connection configuration...\n";
        
        // Add the connection configuration
        config(['database.connections.mysql2' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => 'culture_digitalcollection',
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
        ]]);
        
        // Try connecting again
        try {
            $manuscriptDb = DB::connection('mysql2');
            echo "Connected to culture_digitalcollection database after adding configuration.\n";
        } catch (\Exception $e) {
            echo "Error connecting to culture_digitalcollection database after adding configuration: " . $e->getMessage() . "\n";
            exit(1);
        }
    } else {
        echo "The mysql2 connection is defined in config/database.php, but there was an error connecting: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Count of updated items
$updatedCount = 0;
$notFoundCount = 0;
$noCategoryCount = 0;
$noMatchingCategoryCount = 0;

// Process each item
foreach ($items as $item) {
    // Get the identifier_no
    $identifierNo = $item->identifier_no;
    
    // Find the matching manuscript
    $manuscript = $manuscriptDb->table('manuscript')
        ->where('identifier_no', $identifierNo)
        ->first();
    
    if (!$manuscript) {
        echo "No matching manuscript found for identifier_no: $identifierNo\n";
        $notFoundCount++;
        continue;
    }
    
    // Get the category from the manuscript
    $categoryCode = $manuscript->category ?? null;
    
    if (empty($categoryCode)) {
        echo "No category found for manuscript with identifier_no: $identifierNo\n";
        $noCategoryCount++;
        continue;
    }
    
    echo "Found category code: $categoryCode for identifier_no: $identifierNo\n";
    
    // Find the matching category ID
    $categoryId = $categoryMapping[$categoryCode] ?? null;
    
    if ($categoryId === null) {
        echo "No matching category found for code: $categoryCode\n";
        $noMatchingCategoryCount++;
        continue;
    }
    
    // Verify that the category ID exists in the categories table
    $categoryExists = false;
    foreach ($categories as $category) {
        if ($category->id == $categoryId) {
            $categoryExists = true;
            break;
        }
    }
    
    if (!$categoryExists) {
        echo "Category ID $categoryId does not exist in the categories table\n";
        $noMatchingCategoryCount++;
        continue;
    }
    
    // Update the item's category_id
    DB::table('items')
        ->where('id', $item->id)
        ->update(['category_id' => $categoryId]);
    
    echo "Updated item ID: {$item->id}, identifier_no: $identifierNo with category_id: $categoryId\n";
    $updatedCount++;
}

echo "\nUpdate summary:\n";
echo "Total items processed: " . $items->count() . "\n";
echo "Items updated: $updatedCount\n";
echo "Items with no matching manuscript: $notFoundCount\n";
echo "Items with no category in manuscript: $noCategoryCount\n";
echo "Items with no matching category code: $noMatchingCategoryCount\n";
