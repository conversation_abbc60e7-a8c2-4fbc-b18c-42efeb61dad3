<?php

/**
 * Laravel - A PHP Framework For Web Artisans
 *
 * This file includes the Laravel application from outside the public_html directory
 * for better security.
 */

// Define the path to the Laravel application directory
$laravelPath = __DIR__.'/../../digital-collections-laravel';

// Get current directory name as default subfolder
$currentDirName = basename(__DIR__);

// Load .env file to get APP_SUBFOLDER
if (file_exists($laravelPath . '/.env')) {
    $envFile = file_get_contents($laravelPath . '/.env');
    preg_match('/APP_SUBFOLDER=(.*)/', $envFile, $matches);
    $configSubfolder = isset($matches[1]) ? trim($matches[1]) : $currentDirName;
} else {
    $configSubfolder = $currentDirName;
}

// Store the original request URI
$originalRequestUri = $_SERVER['REQUEST_URI'];

// Check if this is a request for Livewire assets or AJAX requests
if (strpos($originalRequestUri, '/vendor/livewire/') !== false || strpos($originalRequestUri, '/livewire/') !== false) {
    // Always pass Livewire requests through to Laravel
    // Set the script name and filename to point to Laravel's public/index.php
    $_SERVER['SCRIPT_NAME'] = '/index.php';
    $_SERVER['PHP_SELF'] = '/index.php';
    $_SERVER['SCRIPT_FILENAME'] = $laravelPath . '/public/index.php';

    // Remove the subfolder prefix from the request URI
    $scriptName = '/' . $configSubfolder;
    if (strpos($originalRequestUri, $scriptName) === 0) {
        $_SERVER['REQUEST_URI'] = substr($originalRequestUri, strlen($scriptName));
    }

    // Change directory to the Laravel public directory
    chdir($laravelPath . '/public');

    // Include the Laravel public index.php
    require $laravelPath . '/public/index.php';
    exit;
}

// If the request is for the subfolder, adjust the URI
$scriptName = '/' . $configSubfolder;
if (strpos($originalRequestUri, $scriptName) === 0) {
    // Remove the script name from the request URI
    $requestUri = substr($originalRequestUri, strlen($scriptName));
    // If empty, set to /
    if (empty($requestUri)) {
        $requestUri = '/';
    }
    // Set the new request URI
    $_SERVER['REQUEST_URI'] = $requestUri;
}

// Set the script name and filename
$_SERVER['SCRIPT_NAME'] = '/index.php';
$_SERVER['PHP_SELF'] = '/index.php';
$_SERVER['SCRIPT_FILENAME'] = $laravelPath . '/public/index.php';

// Change directory to the Laravel public directory
chdir($laravelPath . '/public');

// Include the Laravel public index.php
require $laravelPath . '/public/index.php';
