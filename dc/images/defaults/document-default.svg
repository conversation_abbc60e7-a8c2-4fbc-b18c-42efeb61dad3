<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400px" height="300px" viewBox="0 0 400 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Document Default</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F5F7FA" offset="100%"></stop>
        </linearGradient>
        <filter x="-5.0%" y="-5.0%" width="110.0%" height="110.0%" filterUnits="objectBoundingBox" id="shadow-1">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.15 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#4776E6" offset="0%"></stop>
            <stop stop-color="#8E54E9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="#F0F4F8" x="0" y="0" width="400" height="300"></rect>
        <g id="document-container" filter="url(#shadow-1)" transform="translate(75, 25)">
            <!-- Main document background -->
            <path d="M20,0 L180,0 L230,50 L230,250 C230,261.05 221.05,270 210,270 L20,270 C8.95,270 0,261.05 0,250 L0,20 C0,8.95 8.95,0 20,0 Z" fill="url(#linearGradient-1)"></path>
            
            <!-- Folded corner -->
            <path d="M180,0 L230,50 L200,50 C189.33,50 180,40.67 180,30 L180,0 Z" fill="#E6EBF2"></path>
            
            <!-- Document header -->
            <rect id="header" fill="url(#linearGradient-2)" x="30" y="70" width="170" height="30" rx="4"></rect>
            <rect id="header-text-1" fill="#FFFFFF" opacity="0.8" x="40" y="80" width="100" height="4" rx="2"></rect>
            <rect id="header-text-2" fill="#FFFFFF" opacity="0.6" x="40" y="90" width="60" height="3" rx="1.5"></rect>
            
            <!-- Document content -->
            <rect id="content-line-1" fill="#D8DEE9" x="30" y="115" width="170" height="5" rx="2.5"></rect>
            <rect id="content-line-2" fill="#D8DEE9" x="30" y="130" width="170" height="5" rx="2.5"></rect>
            <rect id="content-line-3" fill="#D8DEE9" x="30" y="145" width="170" height="5" rx="2.5"></rect>
            <rect id="content-line-4" fill="#D8DEE9" x="30" y="160" width="170" height="5" rx="2.5"></rect>
            <rect id="content-line-5" fill="#D8DEE9" x="30" y="175" width="130" height="5" rx="2.5"></rect>
            
            <!-- Document icon -->
            <g id="document-icon" transform="translate(100, 20)">
                <circle fill="url(#linearGradient-2)" cx="15" cy="15" r="15"></circle>
                <path d="M15,7.5 C15.55,7.5 16,7.95 16,8.5 L16,21.5 C16,22.05 15.55,22.5 15,22.5 C14.45,22.5 14,22.05 14,21.5 L14,8.5 C14,7.95 14.45,7.5 15,7.5 Z" fill="#FFFFFF"></path>
                <path d="M15,7.5 C15.55,7.5 16,7.95 16,8.5 L16,21.5 C16,22.05 15.55,22.5 15,22.5 C14.45,22.5 14,22.05 14,21.5 L14,8.5 C14,7.95 14.45,7.5 15,7.5 Z" fill="#FFFFFF" transform="translate(15, 15) rotate(90) translate(-15, -15)"></path>
            </g>
            
            <!-- Bottom section -->
            <rect id="bottom-section" fill="#F0F4F8" x="30" y="200" width="170" height="50" rx="4"></rect>
            <rect id="bottom-line-1" fill="#D8DEE9" x="40" y="210" width="150" height="4" rx="2"></rect>
            <rect id="bottom-line-2" fill="#D8DEE9" x="40" y="220" width="150" height="4" rx="2"></rect>
            <rect id="bottom-line-3" fill="#D8DEE9" x="40" y="230" width="100" height="4" rx="2"></rect>
            
            <!-- Footer -->
            <rect id="footer" fill="#E6EBF2" x="0" y="260" width="230" height="10"></rect>
        </g>
    </g>
</svg>
