<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Video Default</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F8FAFC" offset="0%"></stop>
            <stop stop-color="#EDF2F7" offset="100%"></stop>
        </linearGradient>
        <filter x="-5.0%" y="-5.0%" width="110.0%" height="110.0%" filterUnits="objectBoundingBox" id="shadow-1">
            <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="6" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#EF4444" offset="0%"></stop>
            <stop stop-color="#B91C1C" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#1E293B" offset="0%"></stop>
            <stop stop-color="#0F172A" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="#F0F4F8" x="0" y="0" width="800" height="600"></rect>
        <g id="video-container" filter="url(#shadow-1)" transform="translate(150, 100)">
            <!-- Background -->
            <rect id="background" fill="url(#linearGradient-1)" x="0" y="0" width="500" height="400" rx="12"></rect>
            
            <!-- Video player -->
            <g id="video-player" transform="translate(75, 80)">
                <rect id="video-bg" fill="url(#linearGradient-3)" x="0" y="0" width="350" height="200" rx="8"></rect>
                
                <!-- Play button -->
                <g id="play-button" transform="translate(150, 75)">
                    <circle fill="url(#linearGradient-2)" cx="25" cy="25" r="25"></circle>
                    <path d="M20,15 L35,25 L20,35 L20,15 Z" fill="#FFFFFF"></path>
                </g>
                
                <!-- Video controls -->
                <g id="video-controls" transform="translate(25, 170)">
                    <rect fill="#64748B" opacity="0.5" x="0" y="0" width="300" height="20" rx="4"></rect>
                    <rect fill="#FFFFFF" x="0" y="0" width="120" height="20" rx="4"></rect>
                    <circle fill="#FFFFFF" cx="120" cy="10" r="6"></circle>
                </g>
            </g>
            
            <!-- Video thumbnails -->
            <g id="video-thumbnails" transform="translate(75, 300)">
                <rect fill="#E2E8F0" x="0" y="0" width="80" height="45" rx="4"></rect>
                <rect fill="#E2E8F0" x="90" y="0" width="80" height="45" rx="4"></rect>
                <rect fill="#E2E8F0" x="180" y="0" width="80" height="45" rx="4"></rect>
                <rect fill="#E2E8F0" x="270" y="0" width="80" height="45" rx="4"></rect>
                
                <!-- Active thumbnail indicator -->
                <rect stroke="#EF4444" stroke-width="2" x="-1" y="-1" width="82" height="47" rx="5"></rect>
            </g>
            
            <!-- Video text -->
            <text id="text" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#EF4444" text-anchor="middle" x="250" y="370">
                VIDEO FILE
            </text>
        </g>
    </g>
</svg>
