<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400px" height="300px" viewBox="0 0 400 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Image Placeholder</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F8FAFC" offset="0%"></stop>
            <stop stop-color="#EDF2F7" offset="100%"></stop>
        </linearGradient>
        <filter x="-5.0%" y="-5.0%" width="110.0%" height="110.0%" filterUnits="objectBoundingBox" id="shadow-1">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#4776E6" offset="0%"></stop>
            <stop stop-color="#8E54E9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect fill="#F0F4F8" x="0" y="0" width="400" height="300"></rect>
        <g id="image-container" filter="url(#shadow-1)" transform="translate(75, 50)">
            <!-- Main image frame -->
            <rect id="frame" fill="url(#linearGradient-1)" x="0" y="0" width="250" height="200" rx="8"></rect>
            
            <!-- Mountains background -->
            <path d="M0,140 L50,90 L75,115 L125,65 L175,115 L200,90 L250,140 L250,200 L0,200 L0,140 Z" fill="#E2E8F0"></path>
            
            <!-- Sun -->
            <circle id="sun" fill="#FCD34D" cx="200" cy="50" r="20"></circle>
            
            <!-- Image icon -->
            <g id="image-icon" transform="translate(87.5, 60)">
                <rect id="icon-bg" fill="url(#linearGradient-2)" x="0" y="0" width="75" height="75" rx="5"></rect>
                <path d="M37.5,25 C41.64,25 45,28.36 45,32.5 C45,36.64 41.64,40 37.5,40 C33.36,40 30,36.64 30,32.5 C30,28.36 33.36,25 37.5,25 Z" fill="#FFFFFF"></path>
                <path d="M55,50 L45,40 L30,55 L20,45 L10,55 L10,60 C10,62.76 12.24,65 15,65 L60,65 C62.76,65 65,62.76 65,60 L65,50 L55,40 L55,50 Z" fill="#FFFFFF"></path>
            </g>
            
            <!-- Text -->
            <text id="text" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#64748B" text-anchor="middle" x="125" y="170">
                NO IMAGE
            </text>
        </g>
    </g>
</svg>
