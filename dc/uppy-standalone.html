<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Uppy Image Uploader</title>
    <link href="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.css" rel="stylesheet">
    <link href="https://releases.transloadit.com/uppy/image-editor/v2.1.1/uppy-ImageEditor.min.css" rel="stylesheet">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        #uppy-container {
            height: 450px; /* ลดความสูงของ container ลง */
            width: 100%;
            overflow: hidden;
        }

        /* Force light theme */
        .uppy-Dashboard {
            background-color: #fff !important;
            color: #333 !important;
        }

        .uppy-Dashboard-inner {
            background-color: #fff !important;
            border: 1px solid #ddd !important;
        }

        /* เพิ่มสไตล์สำหรับ grid mode */
        .uppy-Dashboard--grid .uppy-Dashboard-files {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            grid-gap: 15px !important;
        }

        .uppy-Dashboard-AddFiles {
            background-color: #fff !important;
            color: #333 !important;
        }

        .uppy-Dashboard-browse {
            color: #2541b8 !important;
        }

        .uppy-Dashboard-dropFilesTitle {
            color: #333 !important;
        }

        .uppy-Dashboard-Item {
            background-color: #f8f9fa !important;
        }

        .uppy-Dashboard-Item-name {
            color: #333 !important;
        }

        .uppy-Dashboard-Item-statusSize {
            color: #666 !important;
        }

        .uppy-Dashboard-Item-previewInnerWrap {
            background-color: #f0f0f0 !important;
        }

        .uppy-Dashboard-note {
            color: #666 !important;
        }

        /* Override any dark mode styles */
        .uppy-is-dark {
            color-scheme: light !important;
        }

        .uppy-Dashboard--isDraggingOver {
            background-color: #f0f7ff !important;
        }
    </style>
</head>
<body>
    <div id="uppy-container"></div>

    <script src="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.js"></script>
    <script src="https://releases.transloadit.com/uppy/image-editor/v2.1.1/uppy-ImageEditor.min.js"></script>
    <script src="https://releases.transloadit.com/uppy/locales/v3.0.1/th_TH.min.js"></script>

    <script>
        // Get parameters from URL
        const urlParams = new URLSearchParams(window.location.search);
        const uploadUrl = urlParams.get('uploadUrl');
        const csrfToken = urlParams.get('csrfToken');
        const documentId = urlParams.get('documentId');
        const maxFileSize = parseInt(urlParams.get('maxFileSize') || '5');
        const maxNumberOfFiles = parseInt(urlParams.get('maxNumberOfFiles') || '10');

        // Create Uppy instance
        const uppy = new Uppy.Uppy({
            debug: true,
            autoProceed: false,
            locale: Uppy.locales.th_TH,
            restrictions: {
                maxFileSize: maxFileSize * 1024 * 1024, // Convert MB to bytes
                maxNumberOfFiles: maxNumberOfFiles,
                allowedFileTypes: ['image/*']
            }
        });

        // Add Dashboard plugin
        uppy.use(Uppy.Dashboard, {
            inline: true,
            target: '#uppy-container',
            showProgressDetails: true,
            proudlyDisplayPoweredByUppy: false,
            height: 400, // ลดความสูงลงเหลือ 400px
            width: '100%',
            metaFields: [
                { id: 'name', name: 'Name', placeholder: 'ชื่อไฟล์' }
            ],
            thumbnailWidth: 150,
            theme: 'light', // บังคับใช้ธีมสว่าง (light theme) เท่านั้น
            viewMode: 'list', // แสดงในรูปแบบตาราง (grid)
            doneButtonHandler: null, // ปิดการใช้งานปุ่ม Done
            showLinkToFileUploadResult: false,
            showRemoveButtonAfterComplete: true,
            showProgressDetails: true,
            showSelectedFiles: true,
            note: 'รองรับไฟล์ JPG, PNG, GIF (สูงสุด ' + maxFileSize + 'MB ต่อไฟล์)',
        });

        // Add Image Editor plugin
        uppy.use(Uppy.ImageEditor, {
            id: 'ImageEditor',
            quality: 0.9,
            cropperOptions: {
                viewMode: 1,
                background: true,
                autoCropArea: 1,
                responsive: true,
                guides: true,
                center: true,
                highlight: true,
                cropBoxMovable: true,
                cropBoxResizable: true,
                toggleDragModeOnDblclick: true
            },
            actions: {
                revert: true,
                rotate: true,
                granularRotate: true,
                flip: true,
                zoomIn: true,
                zoomOut: true,
                cropSquare: true,
                cropWidescreen: true,
                cropWidescreenVertical: true
            },
            target: Uppy.Dashboard
        });

        // Add XHR Upload plugin
        uppy.use(Uppy.XHRUpload, {
            endpoint: uploadUrl,
            formData: true,
            fieldName: 'files[]',
            headers: {
                'X-CSRF-TOKEN': csrfToken
            },
            limit: 2, // Process 2 files simultaneously
            timeout: 60000, // 60 seconds
            withCredentials: true
        });

        // Force light theme by adding a class to the body
        document.body.classList.add('uppy-light-theme');

        // Override any dark mode detection
        const style = document.createElement('style');
        style.textContent = `
            @media (prefers-color-scheme: dark) {
                .uppy-Dashboard {
                    background-color: #fff !important;
                    color: #333 !important;
                }
                .uppy-Dashboard-inner {
                    background-color: #fff !important;
                }
                .uppy-Dashboard-AddFiles {
                    background-color: #fff !important;
                    color: #333 !important;
                }
                .uppy-Dashboard-browse {
                    color: #2541b8 !important;
                }
                .uppy-Dashboard-dropFilesTitle {
                    color: #333 !important;
                }
                .uppy-Dashboard-Item {
                    background-color: #f8f9fa !important;
                }
                .uppy-Dashboard-Item-name {
                    color: #333 !important;
                }
                .uppy-Dashboard-Item-statusSize {
                    color: #666 !important;
                }
            }
        `;
        document.head.appendChild(style);

        // Add event listeners
        uppy.on('upload-success', (file, response) => {
            console.log('Image upload success:', file.id, response);

            if (response && response.body && response.body.success) {
                const fileData = response.body.file;

                // Notify the parent window
                window.parent.postMessage({
                    type: 'uppy:upload-success',
                    fileData: fileData
                }, '*');

                // ไม่ต้องลบไฟล์ออกจาก Uppy เพื่อให้ยังแสดงใน dashboard
                // uppy.removeFile(file.id);
            }
        });

        uppy.on('upload-error', (file, error, response) => {
            console.error('Image upload error:', file.id, error, response);

            // Notify the parent window
            window.parent.postMessage({
                type: 'uppy:upload-error',
                error: error.message
            }, '*');

            // ไม่ต้องลบไฟล์ออกจาก Uppy เพื่อให้ยังแสดงใน dashboard
            // uppy.removeFile(file.id);
        });

        // Listen for messages from the parent window
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'uppy:upload') {
                uppy.upload();
            }
        });
    </script>
</body>
</html>
