# Deploying in a Subfolder

This document provides instructions for deploying the application in a subfolder (e.g., `public_html/dc`) instead of the root directory.

## Configuration Steps

1. **Set the APP_SUBFOLDER in .env file**:
   ```
   APP_SUBFOLDER=dc
   ```
   Replace `dc` with your desired subfolder name.

2. **Set the correct APP_URL in .env file**:
   ```
   APP_URL=http://example.com
   ```
   Note: You don't need to include the subfolder in the APP_URL as the application will handle this automatically.

3. **Ensure the .htaccess file exists in the root directory**:
   The application includes a root .htaccess file that redirects requests to the public directory.

4. **Deploy the application to the subfolder**:
   Copy the entire application to the subfolder in your web server's document root.
   For example, if your document root is `/public_html` and your subfolder is `dc`, copy the application to `/public_html/dc`.

5. **Run the storage link command**:
   ```
   php artisan storage:link
   ```
   This creates a symbolic link from `public/storage` to `storage/app/public`.

6. **Clear the application cache**:
   ```
   php artisan config:clear
   php artisan cache:clear
   php artisan route:clear
   php artisan view:clear
   ```

7. **Rebuild assets if using Vite**:
   ```
   npm run build
   ```

## How It Works

The application has been updated to automatically detect when it's running in a subfolder and adjust URLs accordingly:

1. **AppServiceProvider.php**: Detects the subfolder from the APP_SUBFOLDER environment variable or from the script name.
2. **index.php**: Adjusts the request URI to remove the subfolder prefix.
3. **vite.config.js**: Detects the subfolder from the APP_SUBFOLDER environment variable or from the APP_URL.

## Troubleshooting

### Images or Files Not Loading

If images or files are not loading correctly:

1. Check that the storage link is correctly set up
2. Verify that the APP_SUBFOLDER in .env is set correctly
3. Make sure file permissions are correct
4. Check the browser console for any 404 errors and verify the paths

### URL Generation Issues

If links are not working correctly:

1. Make sure you're using Laravel's URL generation functions (`url()`, `route()`, `asset()`) rather than hardcoded paths
2. Clear the application cache after making changes to the .env file

### API Endpoints

If you're using API endpoints, make sure they include the subfolder path in the base URL.

## Additional Notes

- All file upload and storage functions have been modified to work correctly in subfolder installations.
- The Vite configuration has been updated to support subfolder installations for asset compilation.
