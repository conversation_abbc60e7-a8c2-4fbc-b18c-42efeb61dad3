<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>User Default Avatar</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#4A6FFF" offset="0%"></stop>
            <stop stop-color="#3451B2" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="User-Avatar" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="url(#linearGradient-1)" cx="100" cy="100" r="100"></circle>
        <g id="User" transform="translate(50.000000, 40.000000)" fill="#FFFFFF">
            <circle id="Head" cx="50" cy="30" r="30"></circle>
            <path d="M0,120 C0,85.81722 22.38576,70 50,70 C77.61424,70 100,85.81722 100,120" id="Body"></path>
        </g>
    </g>
</svg>
