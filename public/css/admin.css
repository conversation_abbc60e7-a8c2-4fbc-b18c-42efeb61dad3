/* Admin Panel Custom Styles */

/* Base styles */
body {
    font-family: 'Sarabun', sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* Layout styles */
.wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Navbar styles */
.navbar {
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    z-index: 1040;
    height: 64px;
}

/* Logo styles */
.navbar-brand img {
    max-height: 40px;
    width: auto;
    object-fit: contain;
}

/* Sidebar styles */
.sidebar {
    position: fixed;
    top: 64px;
    left: 0;
    bottom: 0;
    width: 260px;
    background-color: #fff;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1030;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.sidebar-content {
    padding: 1.5rem 1rem;
}

.sidebar .nav-link {
    color: #495057;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(13, 110, 253, 0.05);
    color: #0d6efd;
}

.sidebar .nav-link.active {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    font-weight: 500;
}

.sidebar .nav-link i {
    color: #6c757d;
    transition: all 0.2s ease;
    width: 20px;
    text-align: center;
}

.sidebar .nav-link:hover i,
.sidebar .nav-link.active i {
    color: #0d6efd;
}

.sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1025;
}

/* Main content styles */
.main-content {
    flex: 1;
    margin-left: 260px;
    margin-top: 64px;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    min-height: calc(100vh - 64px);
    display: flex;
    flex-direction: column;
}

.content-wrapper {
    flex: 1;
    padding: 1.5rem;
}

.footer {
    background-color: #fff;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.03);
}

/* Card styles */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.5rem;
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 0.5rem;
}

.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d;
    padding: 0.75rem 1rem;
}

.table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Form styles */
.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Button styles */
.btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Chart container styles */
.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

/* Pagination styles */
.pagination {
    margin: 1rem 0;
    justify-content: center;
}

.pagination .page-item {
    margin: 0 0.125rem;
}

.pagination .page-link {
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    padding: 0.5rem 0.75rem;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
}

/* Uppy Dashboard customizations */
.uppy-Dashboard-inner {
    border: 2px dashed #d1d5db !important;
    border-radius: 0.5rem !important;
}

.uppy-Dashboard-AddFiles-title {
    font-family: 'Sarabun', sans-serif !important;
}

/* Mobile styles */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    body.sidebar-open {
        overflow: hidden;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .content-wrapper {
        padding: 1rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .chart-container {
        height: 250px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.625rem 0.75rem;
    }
}
