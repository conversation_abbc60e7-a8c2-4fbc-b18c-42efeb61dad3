/* Custom styles to match the original design */

/* Global styles */
body {
    font-family: 'Sarabun', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Home wrapper */
.home-wrapper {
    position: relative;
    padding-top: 0;
    margin-top: 0;
    overflow: hidden;
}

/* Header styles */
header {
    padding: 10px 0;
    z-index: 1050;
    position: sticky;
    top: 0;
}

.not-home header {
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Make navbar background transparent on homepage */
.home-page header {
    background-color: transparent;
    box-shadow: none;
    transition: all 0.3s ease;
    position: fixed;
    width: 100%;
}

/* Add background to navbar when scrolled */
.home-page header.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
}

/* Add padding to body when navbar is scrolled to prevent content jump */
body.navbar-scrolled .hero {
    padding-top: 180px;
}

.navbar {
    padding: 0;
    min-height: 60px;
}

@media (max-width: 767.98px) {
    .navbar {
        min-height: 50px;
    }

    .navbar-brand {
        font-size: 1.2rem;
        height: 50px;
    }

    .navbar-brand img {
        height: 40px !important;
    }
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
    padding: 0;
    height: 60px;
}

.home-page .navbar-brand {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.home-page header.scrolled .navbar-brand {
    color: #333;
    text-shadow: none;
}

.navbar-nav .nav-item {
    margin-left: 15px;
}

.navbar-nav .nav-link {
    color: #555;
    font-weight: 500;
    padding: 8px 0;
    position: relative;
}

.home-page .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.home-page header.scrolled .navbar-nav .nav-link {
    color: #555;
    text-shadow: none;
}

.navbar-nav .nav-link:hover {
    color: #007bff;
}

.home-page .navbar-nav .nav-link:hover {
    color: #fff;
}

.home-page header.scrolled .navbar-nav .nav-link:hover {
    color: #007bff;
}

.navbar-nav .nav-link.active {
    color: #007bff;
}

.home-page .navbar-nav .nav-link.active {
    color: #fff;
}

.home-page header.scrolled .navbar-nav .nav-link.active {
    color: #007bff;
}

.navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #007bff;
}

.home-page .navbar-nav .nav-link.active::after {
    background-color: #fff;
}

.home-page header.scrolled .navbar-nav .nav-link.active::after {
    background-color: #007bff;
}

/* Navbar toggler for mobile */
.home-page .navbar-toggler {
    border-color: rgba(255, 255, 255, 0.5);
}

.home-page .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.8)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.home-page header.scrolled .navbar-toggler {
    border-color: rgba(0, 0, 0, 0.1);
}

.home-page header.scrolled .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(0, 0, 0, 0.55)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Hero section */
.hero {
    background-color: #007bff;
    color: #fff;
    padding: 180px 0 20px;
    text-align: center;
    margin-bottom: 0;
    position: relative;
    min-height: 70vh;
    margin-top: -90px; /* Increased offset to account for larger logo */
}

/* Hero section for other pages */
.not-home .hero {
    padding: 120px 0 40px;
    min-height: auto;
    margin-top: -76px;
    background-color: #007bff;
    color: #fff;
    text-align: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
                rgba(20, 0, 80, 0.9) 0%,
                rgba(43, 0, 128, 0.85) 20%,
                rgba(0, 71, 171, 0.8) 40%,
                rgba(0, 123, 255, 0.75) 70%,
                rgba(0, 180, 219, 0.7) 100%);
    z-index: 1;
}

.not-home .hero::before {
    background: linear-gradient(135deg,
                rgba(20, 0, 80, 0.85) 0%,
                rgba(43, 0, 128, 0.8) 30%,
                rgba(0, 71, 171, 0.75) 60%,
                rgba(0, 123, 255, 0.7) 100%);
}

.hero .container {
    position: relative;
    z-index: 2;
    padding-top: 50px;
}

.hero h1 {
    font-size: 3.2rem;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInDown 1s ease-out;
}

.hero p {
    font-size: 1.3rem;
    max-width: 1000px;
    margin: 0 auto 20px;
    opacity: 0.95;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.search-box {
    position: relative;
    max-width: 600px;
    margin: 30px auto 10px;
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform;
    /* Remove animation that causes movement */
    /* animation: fadeIn 1.2s ease-out; */
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.search-box .form-control {
    padding-right: 40px;
    border-radius: 4px;
    height: 45px;
}

.search-box .search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Section headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    font-size: 2.2rem;
    color: #333;
    font-weight: 600;
    margin-bottom: 0;
}

.view-all {
    color: #007bff;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
}

.view-all i {
    margin-left: 5px;
}

/* Category cards */
.category-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    text-align: center;
    padding: 25px 15px;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.category-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    overflow: hidden;
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-card h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.category-card p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}


/* Item type cards */
.item-type-card, .document-type-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    text-align: center;
    padding: 25px 15px;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.item-type-card:hover, .document-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.item-type-icon, .document-type-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(0, 123, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.8rem;
}

.item-type-card h3, .document-type-card h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.item-type-card p, .document-type-card p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* Item cards */
.item-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.item-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.item-info {
    padding: 15px;
    flex-grow: 1;
}

.item-info h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: #333;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.item-info p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.9rem;
}

.item-meta {
    padding: 10px 15px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px; /* เพิ่ม gap เพื่อให้ badge มีระยะห่างที่เหมาะสม */
}

/* ปรับแต่งสี badge ให้สว่างขึ้น */
.badge.bg-secondary {
    background-color: #6c9bd1 !important; /* สีฟ้าอ่อน */
    color: #fff;
}

.badge.bg-secondary.item-badge {
    background-color: #6c9bd1 !important; /* สีฟ้าอ่อน */
    color: #fff;
}

.item-type {
    display: inline-block;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.item-date {
    font-size: 0.8rem;
    color: #6c757d;
}

/* Item viewer */
.item-viewer {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

/* Item grid and table */
.item-grid, .document-grid {
    margin-bottom: 20px;
}

.item-table, .document-table {
    margin-bottom: 20px;
}

.viewer-toolbar {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.toolbar-left, .toolbar-right {
    display: flex;
    gap: 5px;
}

.main-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f9f9f9;
    min-height: 400px;
    position: relative;
    overflow: hidden;
}

.main-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

#item-image {
    max-width: 100%;
    max-height: 400px;
    transition: transform 0.3s ease;
}

.viewer-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
}

.thumbnails {
    display: flex;
    gap: 10px;
    padding: 15px;
    overflow-x: auto;
    overflow-y: hidden;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
    scrollbar-width: thin;
    scrollbar-color: #007bff #f0f0f0;
    white-space: nowrap;
    cursor: grab;
    user-select: none;
}

.thumbnails.dragging {
    cursor: grabbing;
    scroll-behavior: auto;
}

.thumbnails::-webkit-scrollbar {
    height: 8px;
}

.thumbnails::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
}

.thumbnails::-webkit-scrollbar-thumb {
    background-color: #007bff;
    border-radius: 4px;
}

.thumbnail {
    width: 80px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-block;
    flex-shrink: 0;
    position: relative;
    z-index: 10;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
}

.thumbnail.active {
    border-color: #007bff;
}

.thumbnail:hover:not(.active) {
    border-color: #ddd;
}

/* Document tabs */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    padding: 10px 20px;
    font-weight: 500;
    color: #495057;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: transparent;
}

/* Map styles */
.map-container {
    height: 400px;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 20px;
    border: 1px solid #ddd;
    position: relative;
    z-index: 1;
}

.location-info {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #eee;
}

/* Filter section */
.filter-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
}

.filter-section h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
    color: #333;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: #555;
}

.filter-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Pagination */
.pagination {
    margin-top: 40px;
    justify-content: center;
}

.pagination .page-item .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
    padding: 8px 16px;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.pagination .page-item .page-link:hover {
    background-color: #e9ecef;
}

/* Footer */
footer {
    background-color: #343a40;
    color: #fff;
    padding: 40px 0 20px;
    margin-top: 60px;
}

footer h5 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

footer h5::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: #007bff;
}

footer ul {
    padding-left: 0;
    list-style: none;
}

footer ul li {
    margin-bottom: 10px;
}

footer a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
}

footer a:hover {
    color: #fff;
    text-decoration: none;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 30px;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: #007bff;
    transform: translateY(-3px);
}

/* Responsive styles */
@media (max-width: 991.98px) {
    .hero {
        padding: 140px 0 40px;
        min-height: 60vh;
    }

    .not-home .hero {
        padding: 100px 0 30px;
    }

    .hero h1 {
        font-size: 2.8rem;
    }

    .not-home .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.3rem;
    }

    .not-home .hero p {
        font-size: 1.2rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 767.98px) {
    .hero {
        padding: 140px 0 40px;
        min-height: 50vh;
        margin-top: -70px;
    }

    body.navbar-scrolled .hero {
        padding-top: 140px;
    }

    .not-home .hero {
        padding: 90px 0 30px;
    }

    .hero h1 {
        font-size: 2.2rem;
    }

    .not-home .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .not-home .hero p {
        font-size: 1rem;
    }

    .search-box {
        max-width: 100%;
    }

    .filter-row {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .document-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 575.98px) {
    .hero {
        padding: 120px 0 30px;
        min-height: auto;
        margin-top: -60px;
    }

    body.navbar-scrolled .hero {
        padding-top: 120px;
    }

    .hero .container {
        padding-top: 20px;
    }

    .navbar-brand {
        font-size: 1rem;
        height: 45px;
    }

    .navbar-brand img {
        height: 35px !important;
    }

    header {
        padding: 5px 0;
    }

    .not-home .hero {
        padding: 80px 0 25px;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .not-home .hero h1 {
        font-size: 1.6rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .not-home .hero p {
        font-size: 0.9rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .filter-buttons {
        flex-direction: column;
    }
}
