/**
 * Cleanup Tool - Custom Styles
 * Modern, visually appealing styles for the file cleanup tool
 */

/* Card styling */
.cleanup-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.cleanup-card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

/* File items styling */
.file-item {
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid #f0f0f0;
    margin-bottom: 10px;
}

.file-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.file-item .file-preview {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.file-item .file-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

.file-item .file-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: #f8f9fa;
}

.file-item .file-details {
    flex: 1;
}

.file-item .file-name {
    font-weight: 500;
    margin-bottom: 4px;
    word-break: break-word;
}

.file-item .file-path {
    font-size: 0.85rem;
    color: #6c757d;
    word-break: break-all;
}

.file-item .file-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.file-item .file-type-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f0f0f0;
    color: #495057;
}

/* Loading animation */
.cleanup-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.cleanup-loading .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    border-top: 4px solid #007bff;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Custom checkbox styling */
.custom-checkbox {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
    user-select: none;
}

.custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.custom-checkbox .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #f0f0f0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.custom-checkbox:hover .checkmark {
    background-color: #e0e0e0;
}

.custom-checkbox input:checked ~ .checkmark {
    background-color: #007bff;
}

.custom-checkbox .checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.custom-checkbox .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .file-item {
        flex-direction: column;
    }

    .file-item .file-preview,
    .file-item .file-icon {
        margin-bottom: 10px;
    }

    .file-actions {
        margin-top: 10px;
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
}

/* Animation for file items */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-item {
    animation: fadeInUp 0.3s ease forwards;
}

/* Hover effects for buttons */
.btn-outline-secondary:hover {
    background-color: #6c757d;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Table styling */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table th {
    font-weight: 600;
    border-top: none;
}

/* Form controls */
.form-select:focus,
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Input group styling */
.input-group {
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.input-group:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.input-group .form-select {
    border-right: none;
    flex: 1;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/* Alert styling */
.alert {
    border: none;
    border-radius: 8px;
}

.alert-info {
    background-color: rgba(0, 123, 255, 0.1);
    color: #0056b3;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
}

/* Card header styling */
.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: white;
}

/* Button styling */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
}

/* Shadow effects */
.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important;
}
