/* Document viewer styles */
/* Document header and back button */
.h3.fw-bold {
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .h3.fw-bold {
        max-width: 60%;
        font-size: 1.5rem;
    }

    .btn-outline-primary {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .h3.fw-bold {
        max-width: 50%;
        font-size: 1.25rem;
    }

    .btn-outline-primary {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Document metadata badges */
.badge.bg-light {
    background-color: #f8f9fa !important;
    color: #212529 !important;
    border: 1px solid #dee2e6;
    font-weight: 500;
    font-size: 0.95rem !important;
    padding: 0.5rem 0.75rem !important;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.badge.bg-light:hover {
    background-color: #e9ecef !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.badge.bg-light i {
    margin-right: 0.5rem;
    font-size: 1.1em;
}
.document-container {
    position: relative;
    display: flex;
    margin-bottom: 2rem;
    height: calc(100vh - 180px);
    min-height: 600px;
}

.document-main {
    flex: 1;
    min-width: 0;
    height: 100%;
    overflow: hidden;
}

.document-main .card {
    height: 100%;
    margin-bottom: 0 !important;
    display: flex;
    flex-direction: column;
}

.document-main .card-body {
    flex: 1;
    overflow: auto;
}

.document-sidebar {
    width: 350px;
    position: relative;
    margin-left: 10px;
    height: 100%;
    transition: all 0.3s ease;
}

.document-sidebar.collapsed {
    width: 0;
    margin-left: 0;
    overflow: visible;
}

.document-sidebar.collapsed .card {
    display: none;
}

.document-main {
    transition: all 0.3s ease;
}

.document-main.expanded {
    width: 100%;
}

/* Sidebar toggle button */
#sidebar-toggle i {
    transition: all 0.3s ease;
    color: #0d6efd;
}

#sidebar-toggle:hover i {
    color: #0a58ca;
}

.document-sidebar.collapsed ~ .document-main #sidebar-toggle i {
    color: #6c757d;
}

.document-sidebar .card {
    height: 100%;
    margin-bottom: 0 !important;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.document-sidebar .card-body {
    flex: 1;
    overflow: auto;
}

/* Document viewer styles */
/* Common styles for all document types */
.card-body.p-4 {
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.card-body.p-4 h5.card-title {
    padding: 1rem;
    margin-bottom: 0;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.card-body.p-4 .card-text {
    padding: 1rem;
    overflow: auto;
}

/* PDF viewer */
.document-viewer.bg-light,
.pdf-viewer.bg-light {
    flex: 1;
    padding: 0 !important;
    margin: 0 !important;
    display: flex;
    flex-direction: column;
}

.ratio.ratio-16x9 {
    flex: 1;
    height: auto !important;
}

/* Video player */
.video-player.bg-light {
    flex: 1;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.video-player video {
    max-height: 100%;
}

/* Audio player */
.audio-player.bg-light {
    padding: 1rem;
}

/* YouTube player */
.youtube-player.bg-light {
    flex: 1;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
}

/* Image viewer styles */
.document-viewer {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    background-color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.viewer-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.main-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    flex: 1;
    overflow: auto;
    position: relative;
}

.main-image {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}

#document-image {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
}

.viewer-pagination {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.thumbnails {
    display: flex;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: white;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    max-width: 100%;
    scrollbar-width: thin;
    cursor: grab;
    flex-shrink: 0;
    height: 80px;
}

.thumbnail {
    width: 80px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    display: inline-block;
    flex-shrink: 0;
}

.thumbnail.active {
    border-color: #0d6efd;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
}

/* Media queries for responsive design */
@media (max-width: 992px) {
    .document-container {
        flex-direction: column;
        height: auto;
    }

    .document-main {
        width: 100%;
        height: 70vh;
        min-height: 500px;
        margin-bottom: 1rem;
    }

    .document-sidebar {
        width: 100%;
        height: auto;
        margin-left: 0;
    }

    .document-sidebar.collapsed {
        height: 0;
        margin-bottom: 0;
    }

    .document-main .card,
    .document-sidebar .card {
        height: 100%;
    }

    .sidebar-toggle {
        position: fixed;
        top: auto;
        bottom: 20px;
        right: 20px;
        transform: none;
        width: 44px;
        height: 44px;
        border-radius: 50%;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        z-index: 1000;
        left: auto;
    }

    .document-sidebar.collapsed + .sidebar-toggle {
        right: 20px;
        left: auto;
        box-shadow: 0 3px 10px rgba(0,0,0,0.3);
        border-width: 2px;
    }

    /* No special mobile styles needed for toolbar toggle button */
}

@media (max-width: 768px) {
    .document-main {
        height: 60vh;
        min-height: 400px;
    }

    .viewer-toolbar .toolbar-left,
    .viewer-toolbar .toolbar-right {
        display: flex;
        gap: 5px;
    }

    .viewer-toolbar button {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .viewer-pagination button {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .thumbnails {
        height: 70px;
    }

    .thumbnail {
        width: 60px;
        height: 50px;
    }

    /* Responsive badges */
    .badge.bg-light {
        font-size: 0.85rem !important;
        padding: 0.4rem 0.6rem !important;
        display: inline-flex;
        align-items: center;
    }
}
