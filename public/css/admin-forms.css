/* Admin Forms Styles */

/* General styling */
body {
    font-family: 'Sarabun', sans-serif;
}

.form-label {
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5c636a;
    border-color: #565e64;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    border: none;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    padding: 1rem 1.5rem;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Dropzone custom styles - improved design */
.dropzone {
    margin-bottom: 1.5rem;
    font-family: inherit;
    background-color: #f8fafc;
    border: 2px dashed #4e73df;
    border-radius: 0.5rem;
    min-height: 150px;
    padding: 25px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.dropzone:hover {
    background-color: #eef2ff;
    border-color: #2e59d9;
}

.dropzone .dz-message {
    color: #5a5c69;
    font-size: 1.1rem;
    text-align: center;
    margin: 2em 0;
    font-weight: 500;
}

.dropzone .dz-message .note {
    font-size: 0.9rem;
    color: #858796;
    display: block;
    margin-top: 0.5rem;
}

.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    width: 120px;
    margin: 0.5em;
}

.dropzone .dz-preview .dz-image {
    border-radius: 8px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10;
    background: rgba(0, 0, 0, 0.1);
}

.dropzone .dz-preview .dz-image img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropzone .dz-preview .dz-details {
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(33, 37, 41, 0.8);
    padding: 2em 1em;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    line-height: 150%;
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dropzone .dz-preview:hover .dz-details {
    opacity: 1;
}

.dropzone .dz-preview .dz-details .dz-size {
    margin-bottom: 1em;
    font-size: 16px;
}

.dropzone .dz-preview .dz-details .dz-filename {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}

.dropzone .dz-preview .dz-progress {
    opacity: 1;
    z-index: 1000;
    pointer-events: none;
    position: absolute;
    height: 16px;
    left: 50%;
    top: 50%;
    margin-top: -8px;
    width: 80px;
    margin-left: -40px;
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1);
    border-radius: 8px;
    overflow: hidden;
}

.dropzone .dz-preview .dz-progress .dz-upload {
    background: #4e73df;
    background: linear-gradient(to bottom, #4e73df, #2e59d9);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    transition: width 300ms ease-in-out;
    border-radius: 8px;
}

.dropzone .dz-preview.dz-success .dz-progress {
    opacity: 0;
    transition: opacity 0.4s ease-in;
}

.dropzone .dz-preview .dz-error-message {
    display: block;
    position: absolute;
    top: -5px;
    left: -20px;
    right: -20px;
    background: rgba(245, 61, 61, 0.9);
    color: white;
    border-radius: 8px;
    font-size: 13px;
    padding: 0.5em 1.2em;
    text-align: center;
    opacity: 1;
    pointer-events: none;
}

.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
    pointer-events: none;
    opacity: 0;
    z-index: 500;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: -27px;
    margin-top: -27px;
    transition: opacity 0.4s ease-in;
}

.dropzone .dz-preview .dz-success-mark svg,
.dropzone .dz-preview .dz-error-mark svg {
    display: block;
    width: 54px;
    height: 54px;
}

.dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
    transition: all 0.2s linear;
}

.dropzone .dz-preview.dz-complete .dz-progress {
    opacity: 0;
    transition: opacity 0.4s ease-in;
}

.dropzone .dz-preview.dz-success .dz-success-mark {
    opacity: 1;
}

.dropzone .dz-preview.dz-error .dz-error-mark {
    opacity: 1;
}

.dropzone .dz-preview.dz-error .dz-progress .dz-upload {
    background: #be2626;
}

.dropzone .dz-preview .dz-remove {
    font-size: 14px;
    text-align: center;
    display: block;
    cursor: pointer;
    border: none;
    background: none;
    color: #4e73df;
    text-decoration: none;
    margin-top: 0.5rem;
    transition: color 0.3s ease;
}

.dropzone .dz-preview .dz-remove:hover {
    color: #2e59d9;
    text-decoration: underline;
}

/* Uppy Dashboard customizations */
.uppy-Dashboard-inner {
    width: 100% !important;
    height: 400px !important;
    border: 2px dashed #4e73df !important;
    border-radius: 0.5rem !important;
}

.uppy-Dashboard-AddFiles-title {
    font-family: 'Sarabun', sans-serif !important;
}

/* Form improvements */
.form-floating > label {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .dropzone {
        min-height: 120px;
        padding: 15px;
    }
    
    .dropzone .dz-preview {
        width: 100px;
    }
    
    .dropzone .dz-preview .dz-image {
        width: 100px;
        height: 100px;
    }
}
