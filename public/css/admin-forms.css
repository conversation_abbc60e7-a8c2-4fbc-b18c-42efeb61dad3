/* Admin Forms Styles */

/* General styling */
body {
    font-family: 'Sarabun', sans-serif;
}

.form-label {
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5c636a;
    border-color: #565e64;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    border: none;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    color: #333;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem 1.5rem;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}







/* Progress Bar - Modern Design */
.dropzone .dz-preview .dz-progress {
    position: absolute;
    top: 50%;
    left: 10px;
    right: 10px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(10px);
}

.dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
}

.dropzone .dz-preview .dz-progress .dz-upload {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(90deg, #4e73df, #1cc88a);
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(78, 115, 223, 0.5);
}

.dropzone .dz-preview.dz-success .dz-progress {
    opacity: 0;
    transition: opacity 0.4s ease-in;
}

/* Error Message */
.dropzone .dz-preview .dz-error-message {
    position: absolute;
    top: 130px;
    left: -50px;
    width: 220px;
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.8rem;
    text-align: center;
    z-index: 25;
    box-shadow: 0 4px 15px rgba(231, 74, 59, 0.3);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropzone .dz-preview.dz-error .dz-error-message {
    opacity: 1;
    transform: translateY(0);
}

/* Success/Error States */
.dropzone .dz-preview.dz-success .dz-success-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 15;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1cc88a, #17a673);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(28, 200, 138, 0.4);
    animation: successPulse 0.6s ease-out;
    opacity: 1;
}

.dropzone .dz-preview.dz-error .dz-error-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 15;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(231, 74, 59, 0.4);
    animation: errorShake 0.6s ease-out;
    opacity: 1;
}

@keyframes successPulse {
    0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes errorShake {
    0%, 100% { transform: translate(-50%, -50%) translateX(0); }
    25% { transform: translate(-50%, -50%) translateX(-5px); }
    75% { transform: translate(-50%, -50%) translateX(5px); }
}

/* Remove Button - Outside Preview */
.dropzone .dz-preview .dz-remove {
    position: relative;
    display: block;
    margin-top: 8px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 2px 8px rgba(231, 74, 59, 0.3);
    transition: all 0.3s ease;
    z-index: 10;
}

.dropzone .dz-preview .dz-remove:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 74, 59, 0.5);
    text-decoration: none;
    color: white;
}

/* File Type Icons - Bottom Right Corner - Always Visible */
.dropzone .dz-preview .dz-image::after {
    content: "\f15b"; /* Default file icon */
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    backdrop-filter: blur(10px);
    z-index: 30;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
    font-weight: 900;
    font-size: 12px;
    color: #858796;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.8);
    opacity: 1 !important;
}

/* Image files */
.dropzone .dz-preview[data-type^="image"] .dz-image::after {
    content: "\f03e"; /* Image icon */
    color: #4e73df;
    background: linear-gradient(135deg, rgba(78, 115, 223, 0.1), rgba(78, 115, 223, 0.05));
}

/* PDF files */
.dropzone .dz-preview[data-type="application/pdf"] .dz-image::after {
    content: "\f1c1"; /* PDF icon */
    color: #e74a3b;
    background: linear-gradient(135deg, rgba(231, 74, 59, 0.1), rgba(231, 74, 59, 0.05));
}

/* Audio files */
.dropzone .dz-preview[data-type^="audio"] .dz-image::after {
    content: "\f1c7"; /* Audio icon */
    color: #1cc88a;
    background: linear-gradient(135deg, rgba(28, 200, 138, 0.1), rgba(28, 200, 138, 0.05));
}

/* Video files */
.dropzone .dz-preview[data-type^="video"] .dz-image::after {
    content: "\f1c8"; /* Video icon */
    color: #f6c23e;
    background: linear-gradient(135deg, rgba(246, 194, 62, 0.1), rgba(246, 194, 62, 0.05));
}

/* Document files (Word, etc.) */
.dropzone .dz-preview[data-type^="application/vnd.openxmlformats-officedocument"] .dz-image::after,
.dropzone .dz-preview[data-type^="application/msword"] .dz-image::after {
    content: "\f15c"; /* Document icon */
    color: #36b9cc;
    background: linear-gradient(135deg, rgba(54, 185, 204, 0.1), rgba(54, 185, 204, 0.05));
}

/* Text files */
.dropzone .dz-preview[data-type^="text"] .dz-image::after {
    content: "\f15c"; /* Text file icon */
    color: #858796;
    background: linear-gradient(135deg, rgba(133, 135, 150, 0.1), rgba(133, 135, 150, 0.05));
}

/* Archive files */
.dropzone .dz-preview[data-type^="application/zip"] .dz-image::after,
.dropzone .dz-preview[data-type^="application/x-rar"] .dz-image::after {
    content: "\f1c6"; /* Archive icon */
    color: #6f42c1;
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.1), rgba(111, 66, 193, 0.05));
}

/* Uppy Dashboard customizations */
.uppy-Dashboard-inner {
    width: 100% !important;
    height: 400px !important;
    border: 2px dashed #4e73df !important;
    border-radius: 0.5rem !important;
}

.uppy-Dashboard-AddFiles-title {
    font-family: 'Sarabun', sans-serif !important;
}

/* Form improvements */
.form-floating > label {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .dropzone {
        min-height: 120px;
        padding: 15px;
    }

    .dropzone .dz-preview {
        width: 100px;
    }

    .dropzone .dz-preview .dz-image {
        width: 100px;
        height: 100px;
    }
}
