/* Admin Forms Styles */

/* General styling */
body {
    font-family: 'Sarabun', sans-serif;
}

.form-label {
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5c636a;
    border-color: #565e64;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    border: none;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #fff;
    color: #333;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem 1.5rem;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Modern Dropzone Styles */
.dropzone {
    margin-bottom: 1.5rem;
    font-family: inherit;
    background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
    border: 2px dashed #4e73df;
    border-radius: 15px;
    min-height: 200px;
    padding: 40px 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(78, 115, 223, 0.1);
    cursor: pointer;
}

.dropzone:hover {
    border-color: #2e59d9;
    background: linear-gradient(135deg, #eef2ff 0%, #f8f9fc 100%);
    box-shadow: 0 4px 20px rgba(78, 115, 223, 0.2);
    transform: translateY(-2px);
}

.dropzone.dz-drag-hover {
    border-color: #1cc88a;
    background: linear-gradient(135deg, #d1eddf 0%, #f8f9fc 100%);
    box-shadow: 0 6px 25px rgba(28, 200, 138, 0.3);
}

.dropzone .dz-message {
    color: #5a5c69;
    font-size: 1.2rem;
    text-align: center;
    margin: 0;
    font-weight: 600;
    z-index: 2;
    position: relative;
}

.dropzone .dz-message .upload-icon {
    font-size: 3rem;
    color: #4e73df;
    margin-bottom: 15px;
    display: block;
    animation: float 3s ease-in-out infinite;
}

.dropzone .dz-message .upload-text {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.dropzone .dz-message .note {
    font-size: 0.9rem;
    color: #858796;
    display: block;
    margin-top: 0.5rem;
    font-weight: 400;
}

/* Floating animation for upload icon */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* File Preview Styles */
.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    margin: 15px;
    vertical-align: top;
    border-radius: 12px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.dropzone .dz-preview:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dropzone .dz-preview .dz-image {
    border-radius: 12px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10;
    background: #f8f9fc;
    border: 2px solid #e3e6f0;
}

.dropzone .dz-preview .dz-image img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropzone .dz-preview .dz-details {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 8px;
    font-size: 0.8rem;
    text-align: center;
    border-radius: 0 0 12px 12px;
}

.dropzone .dz-preview .dz-details .dz-filename {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
}

.dropzone .dz-preview .dz-details .dz-size {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* Progress Bar - Modern Design */
.dropzone .dz-preview .dz-progress {
    position: absolute;
    top: 50%;
    left: 10px;
    right: 10px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(10px);
}

.dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
}

.dropzone .dz-preview .dz-progress .dz-upload {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(90deg, #4e73df, #1cc88a);
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(78, 115, 223, 0.5);
}

.dropzone .dz-preview.dz-success .dz-progress {
    opacity: 0;
    transition: opacity 0.4s ease-in;
}

/* Error Message */
.dropzone .dz-preview .dz-error-message {
    position: absolute;
    top: 130px;
    left: -50px;
    width: 220px;
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 0.8rem;
    text-align: center;
    z-index: 25;
    box-shadow: 0 4px 15px rgba(231, 74, 59, 0.3);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropzone .dz-preview.dz-error .dz-error-message {
    opacity: 1;
    transform: translateY(0);
}

/* Success/Error States */
.dropzone .dz-preview.dz-success .dz-success-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 15;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1cc88a, #17a673);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(28, 200, 138, 0.4);
    animation: successPulse 0.6s ease-out;
    opacity: 1;
}

.dropzone .dz-preview.dz-error .dz-error-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 15;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(231, 74, 59, 0.4);
    animation: errorShake 0.6s ease-out;
    opacity: 1;
}

@keyframes successPulse {
    0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes errorShake {
    0%, 100% { transform: translate(-50%, -50%) translateX(0); }
    25% { transform: translate(-50%, -50%) translateX(-5px); }
    75% { transform: translate(-50%, -50%) translateX(5px); }
}

/* Remove Button - Modern Design */
.dropzone .dz-preview .dz-remove {
    position: absolute;
    top: -8px;
    right: -8px;
    z-index: 20;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e74a3b, #c0392b);
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(231, 74, 59, 0.4);
    transition: all 0.3s ease;
    opacity: 0;
}

.dropzone .dz-preview:hover .dz-remove {
    opacity: 1;
}

.dropzone .dz-preview .dz-remove:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(231, 74, 59, 0.6);
}

/* File Type Icons */
.dropzone .dz-preview .dz-image::before {
    content: "\f15b";
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    z-index: 15;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 12px;
    color: #4e73df;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropzone .dz-preview[data-type^="image"] .dz-image::before {
    content: "\f03e";
    color: #4e73df;
}

.dropzone .dz-preview[data-type="application/pdf"] .dz-image::before {
    content: "\f1c1";
    color: #e74a3b;
}

.dropzone .dz-preview[data-type^="audio"] .dz-image::before {
    content: "\f1c7";
    color: #1cc88a;
}

.dropzone .dz-preview[data-type^="video"] .dz-image::before {
    content: "\f1c8";
    color: #f6c23e;
}

.dropzone .dz-preview[data-type^="application/vnd.openxmlformats-officedocument"] .dz-image::before,
.dropzone .dz-preview[data-type^="application/msword"] .dz-image::before {
    content: "\f15c";
    color: #36b9cc;
}

/* Uppy Dashboard customizations */
.uppy-Dashboard-inner {
    width: 100% !important;
    height: 400px !important;
    border: 2px dashed #4e73df !important;
    border-radius: 0.5rem !important;
}

.uppy-Dashboard-AddFiles-title {
    font-family: 'Sarabun', sans-serif !important;
}

/* Form improvements */
.form-floating > label {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .dropzone {
        min-height: 120px;
        padding: 15px;
    }

    .dropzone .dz-preview {
        width: 100px;
    }

    .dropzone .dz-preview .dz-image {
        width: 100px;
        height: 100px;
    }
}
