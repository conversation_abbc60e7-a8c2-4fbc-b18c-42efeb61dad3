/* Home Page Styles */

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.section-header h2 {
    font-weight: 700;
    color: #333;
    position: relative;
    display: inline-block;
}

.section-header .view-all {
    font-weight: 500;
    transition: all 0.3s ease;
}

.section-header .view-all:hover {
    transform: translateX(5px);
}

/* Custom styles for the hero section */
.hero .input-group {
    border-radius: 50px;
    overflow: hidden;
    position: relative;
    z-index: 10;
}

.hero .form-control {
    border-radius: 50px 0 0 50px;
    height: 54px;
    font-size: 1.1rem;
    border: none;
    position: relative;
}

.hero .btn-primary {
    border-radius: 0 50px 50px 0;
    padding-left: 25px;
    padding-right: 25px;
    font-weight: 500;
    font-size: 1.1rem;
    position: relative;
}

/* Fix search box position */
.search-box {
    position: relative;
    max-width: 600px;
    margin: 30px auto 10px;
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform;
}

/* Stats Section Styles */
.stats-section {
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
    padding: 2rem 0;
    margin-top: 0;
    z-index: 1;
}

.stats-section .section-title {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.stats-section .section-title h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
    position: relative;
    display: inline-block;
}

.stats-section .section-title h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: #0d6efd;
    border-radius: 2px;
}

.stats-section .section-title p {
    font-size: 1rem;
    color: #6c757d;
    max-width: 700px;
    margin: 0 auto;
}

/* Main Stats Card Container */
.stats-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Individual Stats Card */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem 1rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.stats-number.animate {
    animation: pulse 0.5s ease-in-out;
}

/* Stats Icon */
.stats-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: white;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.stats-card:nth-child(1) .stats-icon {
    background: #0d6efd;
}

.stats-card:nth-child(2) .stats-icon {
    background: #6610f2;
}

.stats-card:nth-child(3) .stats-icon {
    background: #198754;
}

.stats-card:nth-child(4) .stats-icon {
    background: #dc3545;
}

/* Stats Number */
.stats-number {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
    transition: all 0.3s ease;
    position: relative;
}

.stats-number::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: currentColor;
    transition: width 0.3s ease;
}

.stats-card:hover .stats-number::after {
    width: 50px;
}

.stats-card:nth-child(1) .stats-number {
    color: #0d6efd;
}

.stats-card:nth-child(2) .stats-number {
    color: #6610f2;
}

.stats-card:nth-child(3) .stats-number {
    color: #198754;
}

.stats-card:nth-child(4) .stats-number {
    color: #dc3545;
}

/* Stats Title */
.stats-title {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 0;
    font-weight: 500;
    position: relative;
    display: inline-block;
}

/* Featured Cards */
.featured-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
}

.featured-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.featured-header {
    background: linear-gradient(90deg, #4a6bff, #2541b8);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
}

.featured-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    margin-right: 1rem;
}

.featured-header h4 {
    margin: 0;
    font-weight: 600;
}

.featured-body {
    padding: 1.5rem;
}

.featured-image {
    width: 120px;
    height: 120px;
    min-width: 120px;
    border-radius: 10px;
    overflow: hidden;
    margin-right: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-content h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.featured-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .featured-image {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }

    .featured-content h5 {
        font-size: 1rem;
    }

    .featured-meta {
        flex-direction: column;
        gap: 0.3rem;
    }

    .stats-number {
        font-size: 1.8rem;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        line-height: 60px;
        font-size: 1.8rem;
    }
}
