/* Item Show Page Styles */

/* Office document viewer styles */
#office-loading {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 8px;
    z-index: 100;
}

.item-viewer {
    position: relative;
}

/* Video player styles */
.video-player video {
    width: 100%;
    height: auto;
    max-height: 500px;
    background-color: #000;
    overflow: hidden;
}

.ratio-16x9 {
    max-height: 500px;
    background-color: #000;
    overflow: hidden;
}

/* Hide scrollbars for video players */
.video-player::-webkit-scrollbar,
.ratio-16x9::-webkit-scrollbar {
    display: none;
}

.video-player,
.ratio-16x9 {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* Modern tab styling */
.nav-tabs {
    position: relative;
    border-bottom: 1px solid #dee2e6 !important;
}

/* Important override for Bootstrap */
.card-header-tabs.nav-tabs {
    margin-bottom: 0 !important;
}

/* Direct style for the card header */
.card-header {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    padding-top: 0.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    border-bottom: none !important;
}

/* Add space between tabs and content */
.tab-content {
    padding-top: 1.25rem !important;
}

/* Remove unnecessary pseudo-elements */
.card-header::after,
.card-header::before {
    display: none !important;
}

/* File section styling */
.file-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-info {
    flex: 1;
    min-width: 0; /* Ensures text truncation works */
}

.file-actions {
    white-space: nowrap;
}

.image-preview {
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
}

/* Improve tab styling */
.nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 0;
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #dee2e6;
    background-color: rgba(0, 0, 0, 0.02);
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom-color: #0d6efd;
    background-color: transparent;
}

/* Card styling improvements */
.card {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-body {
    padding: 1.5rem;
}

/* List group styling */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 1rem;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.nav-tabs .nav-item {
    margin-bottom: 0;
    position: relative;
    z-index: 2;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tabs .nav-link:hover {
    color: #495057;
    background-color: transparent;
    border-color: transparent;
}

.nav-tabs .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: transparent;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    background-color: transparent;
    border-color: transparent;
}

.nav-tabs .nav-link.active::after {
    background-color: #0d6efd;
}

.tab-pane {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Item header and back button */
.h3.fw-semibold {
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    .h3.fw-semibold {
        max-width: 60%;
        font-size: 1.5rem;
    }

    .btn-outline-primary {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .h3.fw-semibold {
        max-width: 50%;
        font-size: 1.25rem;
    }

    .btn-outline-primary {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Image viewer improvements */
.main-image-container {
    min-height: 400px;
    background-color: #f8f9fa;
}

.main-image img {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
}

/* Thumbnail improvements */
.thumbnails {
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #007bff #f0f0f0;
}

.thumbnails::-webkit-scrollbar {
    height: 8px;
}

.thumbnails::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
}

.thumbnails::-webkit-scrollbar-thumb {
    background-color: #007bff;
    border-radius: 4px;
}

.thumbnail {
    transition: all 0.2s ease;
    cursor: pointer;
}

.thumbnail:hover:not(.active) {
    border-color: #ddd !important;
}

.thumbnail.active {
    border-color: #0d6efd !important;
}

/* Map container */
.map-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

/* File download buttons */
.btn-group-sm .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Audio/Video player improvements */
.audio-player,
.video-player {
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
}

/* PDF viewer improvements */
.pdf-viewer iframe {
    border-radius: 4px;
}
