/**
 * Back to Top Button Styles
 */

/* Button base styles */
#back-to-top-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.8);
    color: white;
    border: none;
    cursor: pointer;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Button hover state */
#back-to-top-btn:hover {
    background-color: rgba(13, 110, 253, 1);
    transform: translateY(-5px);
    box-shadow: 0 7px 15px rgba(0, 0, 0, 0.25);
}

/* Button focus state */
#back-to-top-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.5), 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Button active state */
#back-to-top-btn:active {
    transform: translateY(-2px);
}

/* Button visibility states */
#back-to-top-btn.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.9);
    pointer-events: none;
}

#back-to-top-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* Animation for the button icon */
#back-to-top-btn i {
    transition: transform 0.2s ease;
}

#back-to-top-btn:hover i {
    transform: translateY(-3px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #back-to-top-btn {
        width: 45px;
        height: 45px;
        font-size: 18px;
        bottom: 20px;
        right: 20px;
    }
}

@media (max-width: 480px) {
    #back-to-top-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
        bottom: 15px;
        right: 15px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    #back-to-top-btn {
        background-color: rgba(13, 110, 253, 0.7);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    #back-to-top-btn:hover {
        background-color: rgba(13, 110, 253, 0.9);
    }
}
