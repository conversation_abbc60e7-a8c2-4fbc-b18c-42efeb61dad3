<?php

$target = '/home/<USER>/domains/dc.pingdev.net/digital-collections-laravel/storage/app/public';
$link = '/home/<USER>/domains/dc.pingdev.net/digital-collections-laravel/public/storage';

// Check if symlink() is allowed
$disabled = explode(',', ini_get('disable_functions'));
if (in_array('symlink', $disabled)) {
    exit("❌ symlink() is disabled in PHP config.");
}

if (!file_exists($target)) {
    exit("❌ Target path does not exist.");
}

if (!is_writable(dirname($link))) {
    exit("❌ Cannot write to " . dirname($link));
}

if (file_exists($link)) {
    echo "ℹ️ Symlink or file already exists: $link";
} else {
    if (symlink($target, $link)) {
        echo "✅ Symlink created: $link → $target";
    } else {
        echo "❌ Failed to create symlink. Check file permissions or PHP restrictions.";
    }
}
?>
