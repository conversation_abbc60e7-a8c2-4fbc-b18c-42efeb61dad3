/**
 * Back to Top Button Script
 * 
 * This script creates and manages a back-to-top button that appears
 * when the user scrolls down the page and allows them to smoothly
 * scroll back to the top when clicked.
 */

(function() {
    'use strict';

    // Create the button element when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        createBackToTopButton();
    });

    /**
     * Creates the back-to-top button and adds it to the DOM
     */
    function createBackToTopButton() {
        // Create button element
        const button = document.createElement('button');
        button.id = 'back-to-top-btn';
        button.setAttribute('aria-label', 'กลับไปด้านบน');
        button.innerHTML = '<i class="fas fa-chevron-up"></i>';
        
        // Add button to the DOM
        document.body.appendChild(button);
        
        // Initially hide the button
        button.classList.add('hidden');
        
        // Add scroll event listener
        window.addEventListener('scroll', handleScroll);
        
        // Add click event listener
        button.addEventListener('click', scrollToTop);
    }

    /**
     * Handles the scroll event to show/hide the button
     */
    function handleScroll() {
        const button = document.getElementById('back-to-top-btn');
        if (!button) return;
        
        // Show button when user scrolls down 300px from the top
        if (window.scrollY > 300) {
            button.classList.remove('hidden');
            button.classList.add('visible');
        } else {
            button.classList.remove('visible');
            button.classList.add('hidden');
        }
    }

    /**
     * Scrolls the page to the top smoothly
     */
    function scrollToTop(e) {
        e.preventDefault();
        
        // Smooth scroll to top
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }
})();
