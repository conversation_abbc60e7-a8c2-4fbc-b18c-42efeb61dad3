/**
 * Item Viewer with Toggleable Sidebar
 * Handles the item viewing experience with toggleable sidebar
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const itemMain = document.getElementById('item-main');
    const itemSidebar = document.getElementById('item-sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');

    // Check if elements exist
    if (!itemMain || !itemSidebar || !sidebarToggle) {
        console.error('Required elements not found');
        return;
    }

    // Fix for item container position
    const itemContainer = document.querySelector('.item-container');
    if (itemContainer) {
        itemContainer.style.position = 'relative';
    }

    // Toggle sidebar function
    function toggleSidebar() {
        const isCollapsed = itemSidebar.classList.contains('collapsed');

        // Toggle classes
        itemSidebar.classList.toggle('collapsed', !isCollapsed);
        itemMain.classList.toggle('expanded', !isCollapsed);

        // Store preference in localStorage
        localStorage.setItem('item_sidebar_collapsed', !isCollapsed);

        // Force resize event for map if it exists
        setTimeout(() => {
            if (typeof map !== 'undefined' && map) {
                map.invalidateSize();
            }

            // Adjust image container size
            const mainImageContainer = document.querySelector('.main-image-container');
            if (mainImageContainer) {
                mainImageContainer.style.height = mainImageContainer.offsetHeight + 'px';
                setTimeout(() => {
                    mainImageContainer.style.height = '';
                }, 300);
            }
        }, 300);
    }

    // Add click event listener to sidebar toggle button
    sidebarToggle.addEventListener('click', function(e) {
        e.preventDefault();
        toggleSidebar();
    });

    // Check if there's a saved preference
    const savedPreference = localStorage.getItem('item_sidebar_collapsed');
    if (savedPreference === 'true') {
        // Collapse sidebar if that was the last state
        itemSidebar.classList.add('collapsed');
        itemMain.classList.add('expanded');
    }

    // Add keyboard shortcut for toggling sidebar (Esc key)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            toggleSidebar();
        }
    });

    // Fix for item viewer height
    const itemViewer = document.querySelector('.item-viewer');
    if (itemViewer) {
        const adjustHeight = () => {
            const containerHeight = itemViewer.offsetHeight;
            const toolbarHeight = document.querySelector('.viewer-toolbar')?.offsetHeight || 0;
            const paginationHeight = document.querySelector('.viewer-pagination')?.offsetHeight || 0;
            const thumbnailsHeight = document.querySelector('.thumbnails')?.offsetHeight || 0;

            const mainImageContainer = document.querySelector('.main-image-container');
            if (mainImageContainer) {
                const availableHeight = containerHeight - toolbarHeight - paginationHeight - thumbnailsHeight;
                mainImageContainer.style.height = availableHeight + 'px';
            }
        };

        // Adjust height on load and resize
        adjustHeight();
        window.addEventListener('resize', adjustHeight);
    }
});
