/* Admin Settings Page JavaScript */

document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Gradient Preview Functions
    function updateGradientPreview() {
        const enabled = document.getElementById('hero_gradient_enabled').checked;
        const startColor = document.getElementById('hero_gradient_start').value;
        const middleColor = document.getElementById('hero_gradient_middle').value;
        const endColor = document.getElementById('hero_gradient_end').value;
        const direction = document.getElementById('hero_gradient_direction').value;
        const opacity = document.getElementById('hero_gradient_opacity').value;
        const preview = document.getElementById('gradient-preview');

        if (!enabled) {
            preview.style.background = '#f8f9fa';
            preview.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100 text-muted">การไล่สีถูกปิดใช้งาน</div>';
            return;
        }

        // Convert hex to rgba
        function hexToRgba(hex, alpha) {
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }

        const alpha = opacity / 100;
        const startRgba = hexToRgba(startColor, alpha);
        const middleRgba = hexToRgba(middleColor, alpha);
        const endRgba = hexToRgba(endColor, alpha);

        // Create gradient
        const gradient = `linear-gradient(${direction}, ${startRgba}, ${middleRgba}, ${endRgba})`;
        preview.style.background = gradient;
        preview.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100 text-white" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">ตัวอย่างการไล่สี</div>';
    }

    // Update color input text values
    function updateColorText(colorInput) {
        const textInput = colorInput.parentElement.querySelector('input[type="text"]');
        if (textInput) {
            textInput.value = colorInput.value;
        }
    }

    // Update opacity value display
    function updateOpacityValue() {
        const opacitySlider = document.getElementById('hero_gradient_opacity');
        const opacityValue = document.getElementById('opacity-value');
        opacityValue.textContent = opacitySlider.value + '%';
    }

    // Event listeners
    document.getElementById('hero_gradient_enabled').addEventListener('change', updateGradientPreview);
    document.getElementById('hero_gradient_start').addEventListener('input', function() {
        updateColorText(this);
        updateGradientPreview();
    });
    document.getElementById('hero_gradient_middle').addEventListener('input', function() {
        updateColorText(this);
        updateGradientPreview();
    });
    document.getElementById('hero_gradient_end').addEventListener('input', function() {
        updateColorText(this);
        updateGradientPreview();
    });
    document.getElementById('hero_gradient_direction').addEventListener('change', updateGradientPreview);
    document.getElementById('hero_gradient_opacity').addEventListener('input', function() {
        updateOpacityValue();
        updateGradientPreview();
    });

    // Initialize
    updateGradientPreview();
    updateOpacityValue();
});
