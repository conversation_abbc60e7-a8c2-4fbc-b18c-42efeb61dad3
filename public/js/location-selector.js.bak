/**
 * Location Selector Script
 *
 * This script handles the dynamic loading of provinces based on country selection.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get the country and province select elements
    const countrySelect = document.getElementById('country');
    const provinceSelect = document.getElementById('province_id');

    if (countrySelect && provinceSelect) {
        // Add event listener to country select
        countrySelect.addEventListener('change', function() {
            const countryCode = this.value;

            // Clear province select
            provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';

            // If no country is selected, disable province select
            if (!countryCode) {
                provinceSelect.disabled = true;
                return;
            }

            // Enable province select
            provinceSelect.disabled = false;

            // Show loading indicator
            provinceSelect.innerHTML = '<option value="">กำลังโหลดข้อมูล...</option>';

            // Fetch provinces for selected country (convert to uppercase for API)
            fetch(`/locations/provinces/${countryCode.toUpperCase()}`)
                .then(response => response.json())
                .then(provinces => {
                    // Reset province select
                    provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';

                    // Add province options
                    provinces.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.id;
                        option.textContent = province.name_th;
                        provinceSelect.appendChild(option);
                    });

                    // If there are no provinces, show message
                    if (provinces.length === 0) {
                        provinceSelect.innerHTML = '<option value="">ไม่พบข้อมูลจังหวัด</option>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching provinces:', error);
                    provinceSelect.innerHTML = '<option value="">เกิดข้อผิดพลาดในการโหลดข้อมูล</option>';
                });
        });

        // Trigger change event if country is already selected (for edit page)
        if (countrySelect.value) {
            // We use setTimeout to ensure this runs after the page is fully loaded
            setTimeout(() => {
                // Store the current province value to reselect it after loading
                const currentProvinceValue = provinceSelect.value;
                console.log('Current province value:', currentProvinceValue);

                // Trigger the change event to load provinces
                const event = new Event('change');
                countrySelect.dispatchEvent(event);

                // Function to select the correct province after loading
                const selectProvinceAfterLoading = function() {
                    if (provinceSelect.options.length > 1) {
                        let found = false;

                        // Find and select the option with the stored value (case insensitive)
                        for (let i = 0; i < provinceSelect.options.length; i++) {
                            if (provinceSelect.options[i].value.toLowerCase() === currentProvinceValue.toLowerCase()) {
                                provinceSelect.selectedIndex = i;
                                found = true;
                                console.log('Found matching province:', provinceSelect.options[i].value);
                                break;
                            }
                        }

                        if (!found) {
                            console.log('Province not found in options, trying again...');
                            return; // Keep observer active
                        }

                        // Remove the observer once we've selected the province
                        observer.disconnect();
                    }
                };

                // Use a MutationObserver to watch for changes to the province select
                const observer = new MutationObserver(selectProvinceAfterLoading);
                observer.observe(provinceSelect, { childList: true });
            }, 300); // Increased timeout for better reliability
        } else {
            // If no country is selected, disable province select
            provinceSelect.disabled = true;
        }
    }
});
