/**
 * Document Viewer with Toggleable Sidebar
 * Handles the document viewing experience with toggleable sidebar
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const documentMain = document.getElementById('document-main');
    const documentSidebar = document.getElementById('document-sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');

    // Check if elements exist
    if (!documentMain || !documentSidebar || !sidebarToggle) {
        console.error('Required elements not found');
        return;
    }

    // Fix for document container position
    const documentContainer = document.querySelector('.document-container');
    if (documentContainer) {
        documentContainer.style.position = 'relative';
    }

    // Toggle sidebar function
    function toggleSidebar() {
        const isCollapsed = documentSidebar.classList.contains('collapsed');

        // Toggle classes
        documentSidebar.classList.toggle('collapsed', !isCollapsed);
        documentMain.classList.toggle('expanded', !isCollapsed);

        // Store preference in localStorage
        localStorage.setItem('document_sidebar_collapsed', !isCollapsed);

        // Force resize event for map if it exists
        setTimeout(() => {
            if (typeof map !== 'undefined' && map) {
                map.invalidateSize();
            }

            // Adjust image container size
            const mainImageContainer = document.querySelector('.main-image-container');
            if (mainImageContainer) {
                mainImageContainer.style.height = mainImageContainer.offsetHeight + 'px';
                setTimeout(() => {
                    mainImageContainer.style.height = '';
                }, 300);
            }
        }, 300);
    }

    // Add click event listener to sidebar toggle button
    sidebarToggle.addEventListener('click', function(e) {
        e.preventDefault();
        toggleSidebar();
    });

    // Check if there's a saved preference
    const savedPreference = localStorage.getItem('document_sidebar_collapsed');
    if (savedPreference === 'true') {
        // Collapse sidebar if that was the last state
        documentSidebar.classList.add('collapsed');
        documentMain.classList.add('expanded');
    }

    // Add keyboard shortcut for toggling sidebar (Esc key)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            toggleSidebar();
        }
    });

    // Fix for document viewer height
    const documentViewer = document.querySelector('.document-viewer');
    if (documentViewer) {
        const adjustHeight = () => {
            const containerHeight = documentViewer.offsetHeight;
            const toolbarHeight = document.querySelector('.viewer-toolbar')?.offsetHeight || 0;
            const paginationHeight = document.querySelector('.viewer-pagination')?.offsetHeight || 0;
            const thumbnailsHeight = document.querySelector('.thumbnails')?.offsetHeight || 0;

            const mainImageContainer = document.querySelector('.main-image-container');
            if (mainImageContainer) {
                const availableHeight = containerHeight - toolbarHeight - paginationHeight - thumbnailsHeight;
                mainImageContainer.style.height = availableHeight + 'px';
            }
        };

        // Adjust height on load and resize
        adjustHeight();
        window.addEventListener('resize', adjustHeight);
    }
});
