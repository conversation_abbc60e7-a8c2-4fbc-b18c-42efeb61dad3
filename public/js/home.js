/* Home Page JavaScript */

document.addEventListener('DOMContentLoaded', function() {
    // Animation for stats numbers
    const statsNumbers = document.querySelectorAll('.stats-number');

    // Intersection Observer to trigger animation when stats are visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Start animation for all stats numbers when they become visible
                statsNumbers.forEach(animateNumber);
                // Unobserve after animation starts
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    // Observe the stats section
    const statsSection = document.querySelector('.stats-section');
    if (statsSection) {
        observer.observe(statsSection);
    }

    // Function to animate a number from 0 to its final value
    function animateNumber(element) {
        const finalValue = parseInt(element.textContent.replace(/,/g, ''));
        const duration = 2000; // Animation duration in milliseconds
        const frameDuration = 1000 / 60; // 60fps
        const totalFrames = Math.round(duration / frameDuration);
        let frame = 0;

        // Start with 0
        let currentNumber = 0;
        element.textContent = '0';

        // Use requestAnimationFrame for smooth animation
        const animate = () => {
            frame++;

            // Calculate progress (0 to 1)
            const progress = frame / totalFrames;

            // Use easeOutQuad for smoother animation
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            // Calculate current number
            currentNumber = Math.floor(easeProgress * finalValue);

            // Format number with commas
            element.textContent = new Intl.NumberFormat().format(currentNumber);

            // Continue animation until complete
            if (frame < totalFrames) {
                requestAnimationFrame(animate);
            } else {
                // Ensure final value is set exactly
                element.textContent = new Intl.NumberFormat().format(finalValue);
                // Add pulse animation when counting completes
                element.classList.add('animate');
                // Remove animation class after animation completes
                setTimeout(() => {
                    element.classList.remove('animate');
                }, 500);
            }
        };

        // Start animation
        requestAnimationFrame(animate);
    }

    // Simple hover effects for stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('hovered');
        });

        card.addEventListener('mouseleave', function() {
            this.classList.remove('hovered');
        });
    });

    // Search form enhancement
    const searchForm = document.querySelector('.search-box form');
    const searchInput = document.querySelector('.search-box input[name="q"]');

    if (searchForm && searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchForm.submit();
            }
        });

        // Add search suggestions (if needed in future)
        searchInput.addEventListener('input', function() {
            // Placeholder for search suggestions functionality
        });
    }

    // Featured cards hover effects
    const featuredCards = document.querySelectorAll('.featured-card');
    featuredCards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Lazy loading for images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(function(img) {
        imageObserver.observe(img);
    });

    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.closest('form')) {
                this.classList.add('loading');
                setTimeout(() => {
                    this.classList.remove('loading');
                }, 3000);
            }
        });
    });
});
