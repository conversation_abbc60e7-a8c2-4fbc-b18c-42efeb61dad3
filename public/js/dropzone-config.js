/**
 * Dropzone Configuration
 *
 * This file contains configuration for Dropzone file uploader
 */

// Log that this file is loaded
console.log('dropzone-config.js loaded');

// File type configurations
const fileTypes = {
    image: {
        acceptedFiles: 'image/jpeg,image/png,image/gif,image/webp',
        maxFilesize: 5, // MB
        maxFiles: 5, // ลดจำนวนไฟล์สูงสุดเป็น 5 ไฟล์
        dictDefaultMessage: '<i class="fas fa-image mb-2" style="font-size: 2rem; color: #4e73df;"></i><br><span><i class="fas fa-file-image me-1"></i> ลากไฟล์รูปภาพมาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์ JPG, PNG, GIF (สูงสุด 5MB ต่อไฟล์, สูงสุด 5 ไฟล์)</span>'
    },
    document: {
        acceptedFiles: 'application/pdf',
        maxFilesize: 50, // MB
        maxFiles: 5,
        dictDefaultMessage: '<i class="fas fa-file-pdf mb-2" style="font-size: 2rem; color: #e74a3b;"></i><br><span><i class="fas fa-file-pdf me-1"></i> ลากไฟล์เอกสารมาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์ PDF (สูงสุด 50MB ต่อไฟล์, สูงสุด 5 ไฟล์)</span>'
    },
    audio: {
        acceptedFiles: 'audio/mpeg,audio/wav,audio/ogg',
        maxFilesize: 20, // MB
        maxFiles: 5,
        dictDefaultMessage: '<i class="fas fa-music mb-2" style="font-size: 2rem; color: #1cc88a;"></i><br><span><i class="fas fa-file-audio me-1"></i> ลากไฟล์เสียงมาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์ MP3, WAV, OGG (สูงสุด 20MB ต่อไฟล์, สูงสุด 5 ไฟล์)</span>'
    },
    video: {
        acceptedFiles: 'video/mp4,video/webm,video/quicktime,video/x-msvideo',
        maxFilesize: 50, // MB
        maxFiles: 3,
        dictDefaultMessage: '<i class="fas fa-video mb-2" style="font-size: 2rem; color: #f6c23e;"></i><br><span><i class="fas fa-file-video me-1"></i> ลากไฟล์วิดีโอมาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์ MP4, WEBM, MOV, AVI (สูงสุด 50MB ต่อไฟล์, สูงสุด 3 ไฟล์)</span>'
    },
    mixed: {
        acceptedFiles: 'image/*,application/pdf,audio/*,video/*,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        maxFilesize: 50, // MB
        maxFiles: 5, // ลดจำนวนไฟล์สูงสุดเป็น 5 ไฟล์
        dictDefaultMessage: '<i class="fas fa-cloud-upload-alt mb-2" style="font-size: 2rem; color: #4e73df;"></i><br><span><i class="fas fa-file-upload me-1"></i> ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์, สูงสุด 5 ไฟล์)</span>'
    }
};

/**
 * Initialize Dropzone for file uploads
 *
 * @param {string} selector - CSS selector for the file input element
 * @param {string} uploadUrl - URL for file uploads
 * @param {string} csrfToken - CSRF token for secure uploads
 * @param {string} fileType - Type of file to upload (image, document, audio, video, mixed)
 * @param {Function} onSuccess - Callback function when file is successfully uploaded
 * @returns {Dropzone} - Dropzone instance
 */
function initDropzone(selector, uploadUrl, csrfToken, fileType = 'mixed', onSuccess = null) {
    console.log('initDropzone called with:', { selector, uploadUrl, fileType });

    try {
        // Default to mixed if fileType is not valid
        if (!fileTypes[fileType]) {
            console.warn('Invalid fileType:', fileType, 'defaulting to mixed');
            fileType = 'mixed';
        }

        // Check if Dropzone is available
        if (typeof Dropzone === 'undefined') {
            console.error('Dropzone is not defined');
            return null;
        }

        // Check if the element exists
        const element = document.querySelector(selector);
        if (!element) {
            console.error('Element not found:', selector);
            return null;
        }

        // Get configuration for the selected file type
        const typeConfig = fileTypes[fileType] || fileTypes.mixed;
        console.log('Using file type configuration:', typeConfig);

        // Set Dropzone default options
        Dropzone.autoDiscover = false;

        // Create Dropzone instance with configuration
        const dropzoneOptions = {
            url: uploadUrl,
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'X-File-Type': fileType
            },
            paramName: 'file', // The name that will be used to transfer the file
            maxFilesize: typeConfig.maxFilesize, // MB
            maxFiles: typeConfig.maxFiles,
            acceptedFiles: typeConfig.acceptedFiles,
            dictDefaultMessage: typeConfig.dictDefaultMessage,
            addRemoveLinks: true,
            dictRemoveFile: '×',
            dictCancelUpload: '⊗',
            dictCancelUploadConfirmation: 'ยกเลิกการอัพโหลดนี้?',
            dictFileTooBig: 'ไฟล์ใหญ่เกินไป ขนาดไฟล์สูงสุดคือ: ' + typeConfig.maxFilesize + 'MB',
            dictInvalidFileType: 'ไม่รองรับไฟล์ประเภทนี้',
            dictMaxFilesExceeded: 'ไม่สามารถอัพโหลดเพิ่มได้ สูงสุด: ' + typeConfig.maxFiles + ' ไฟล์',
            autoProcessQueue: true,
            createImageThumbnails: true,
            thumbnailWidth: 120,
            thumbnailHeight: 120
        };

        console.log('Creating Dropzone with options:', dropzoneOptions);

        // Create Dropzone instance
        const dropzone = new Dropzone(selector, dropzoneOptions);
        console.log('Dropzone instance created:', dropzone);

        // Add event listeners
        dropzone.on('addedfile', function(file) {
            // Add data-type attribute to the preview element for styling
            if (file.previewElement) {
                file.previewElement.setAttribute('data-type', file.type || '');
                console.log('Added data-type attribute:', file.type);
            }
        });

        dropzone.on('success', function(file, response) {
            console.log('File successfully uploaded:', file.name);
            console.log('Server response:', response);

            if (response && response.success) {
                // Create fileData object from response
                const fileData = response;

                // Add file information if not present
                fileData.name = fileData.name || file.name;
                fileData.file_name = fileData.file_name || file.name;
                fileData.size = fileData.size || file.size;
                fileData.file_size = fileData.file_size || file.size;

                // Get file extension
                const extension = file.name.split('.').pop().toLowerCase();
                fileData.extension = fileData.extension || extension;
                fileData.file_extension = fileData.file_extension || extension;

                // Determine if file is an image based on MIME type or extension
                const isImageByType = file.type && file.type.startsWith('image/');
                const isImageByExt = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension);
                fileData.is_image = fileData.is_image !== undefined ? fileData.is_image : (isImageByType || isImageByExt);

                console.log('Processed file data:', fileData);

                // Call the success callback if provided
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess(fileData, file);
                }

                // Dispatch custom event
                window.dispatchEvent(new CustomEvent('dropzone-file-uploaded', {
                    detail: {
                        fileData: fileData,
                        file: file
                    }
                }));

                // Add the server response to the file
                file.serverResponse = response;
            }
        });

        dropzone.on('error', function(file, errorMessage, xhr) {
            console.error('Error uploading file:', file.name);
            console.error('Error message:', errorMessage);

            // Display error message on the file preview
            if (typeof errorMessage === 'string') {
                const errorElement = document.createElement('div');
                errorElement.className = 'dz-error-message';
                errorElement.innerHTML = `<span data-dz-errormessage>${errorMessage}</span>`;
                file.previewElement.appendChild(errorElement);
            } else if (errorMessage && errorMessage.error) {
                const errorElement = document.createElement('div');
                errorElement.className = 'dz-error-message';
                errorElement.innerHTML = `<span data-dz-errormessage>${errorMessage.error}</span>`;
                file.previewElement.appendChild(errorElement);
            }
        });

        return dropzone;
    } catch (error) {
        console.error('Error in initDropzone:', error);
        return null;
    }
}

// Make functions available globally
window.fileTypes = fileTypes;
window.initDropzone = initDropzone;
