/* Cleanup Tool JavaScript */

$(document).ready(function() {
    // ค้นหาไฟล์ขยะ
    $('#scan-btn').on('click', function() {
        const fileType = $('#file-type').val();

        // แสดง loading
        $('#loading').show();
        $('#results-container').hide();

        // ส่งคำขอค้นหาไฟล์ขยะ
        $.ajax({
            url: window.cleanupRoutes.scan,
            type: 'POST',
            data: {
                _token: window.csrfToken,
                type: fileType
            },
            success: function(response) {
                // ซ่อน loading
                $('#loading').hide();

                if (response.success) {
                    // แสดงผลลัพธ์
                    displayResults(response);
                } else {
                    showErrorAlert('เกิดข้อผิดพลาด', response.message || 'ไม่สามารถค้นหาไฟล์ขยะได้');
                }
            },
            error: function(xhr) {
                // ซ่อน loading
                $('#loading').hide();
                showErrorAlert('เกิดข้อผิดพลาด', 'ไม่สามารถค้นหาไฟล์ขยะได้');
            }
        });
    });

    // แสดงผลลัพธ์การค้นหา
    function displayResults(response) {
        // รวมไฟล์ทั้งหมดเข้าด้วยกัน
        const allFiles = [];
        const files = response.files || {};

        // รวมไฟล์จากทุกประเภท
        if (files.images && files.images.length > 0) {
            files.images.forEach(file => {
                file.fileType = 'image';
                allFiles.push(file);
            });
        }

        if (files.documents && files.documents.length > 0) {
            files.documents.forEach(file => {
                file.fileType = 'file';
                allFiles.push(file);
            });
        }

        if (files.others && files.others.length > 0) {
            files.others.forEach(file => {
                file.fileType = 'other';
                allFiles.push(file);
            });
        }

        // เพิ่มการรองรับไฟล์ประเภทใหม่
        if (files.settings && files.settings.length > 0) {
            files.settings.forEach(file => {
                file.fileType = 'settings';
                allFiles.push(file);
            });
        }

        if (files.categories && files.categories.length > 0) {
            files.categories.forEach(file => {
                file.fileType = 'categories';
                allFiles.push(file);
            });
        }

        if (files.profiles && files.profiles.length > 0) {
            files.profiles.forEach(file => {
                file.fileType = 'profiles';
                allFiles.push(file);
            });
        }

        const totalFiles = response.total || 0;
        const totalSize = response.totalSizeFormatted || '0 B';

        // แสดงจำนวนไฟล์ที่พบ
        $('#results-count').text(totalFiles);
        $('#results-count-display').text(totalFiles);
        $('#results-summary').html(`<i class="fas fa-search me-2"></i><span id="results-count-display">${totalFiles}</span> ไฟล์ขยะ (${totalSize})`);

        // แสดงผลลัพธ์
        $('#results-container').show();

        if (totalFiles === 0) {
            // ไม่พบไฟล์ขยะ
            $('#no-results').show();
            $('#files-container').hide();
            $('#delete-selected-btn').prop('disabled', true);
        } else {
            // พบไฟล์ขยะ
            $('#no-results').hide();
            $('#files-container').show();

            // แสดงรายการไฟล์
            displayFileList(allFiles);

            // ตรวจสอบการเลือกไฟล์
            updateDeleteButtonState();
        }
    }

    // แสดงรายการไฟล์
    function displayFileList(files) {
        const tableBody = $('#files-table-body');
        tableBody.empty();

        if (files.length === 0) {
            const emptyRow = `
                <tr>
                    <td colspan="6" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <p>ไม่พบไฟล์ขยะในระบบ</p>
                        </div>
                    </td>
                </tr>
            `;
            tableBody.append(emptyRow);
            return;
        }

        files.forEach(function(file) {
            const fileSize = formatBytes(file.size);
            const lastModified = new Date(file.last_modified * 1000).toLocaleString('th-TH');
            const fileExtension = getFileExtension(file.name);
            const fileTypeIcon = getFileTypeIcon(fileExtension);

            // ตรวจสอบประเภทไฟล์
            const fileType = file.fileType || getFileTypeFromExtension(fileExtension);
            const fileTypeLabel = getFileTypeLabel(fileType);

            let row = `
                <tr>
                    <td>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input file-checkbox" data-path="${file.path}" data-size="${file.size}">
                        </div>
                    </td>
            `;

            // แสดงตัวอย่างตามประเภทไฟล์
            if (fileType === 'image') {
                row += `
                    <td>
                        <img src="${window.storageUrl}/${file.path}" alt="${file.name}" class="img-thumbnail" style="height: 50px; width: auto; max-width: 100px;">
                    </td>
                `;
            } else {
                row += `
                    <td>
                        <div class="file-icon">
                            <i class="${fileTypeIcon} fa-2x text-secondary"></i>
                        </div>
                    </td>
                `;
            }

            row += `
                    <td>
                        <div class="d-flex flex-column">
                            <span class="fw-medium">${file.name}</span>
                            <small class="text-muted">${file.path}</small>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark">${fileTypeLabel}</span>
                    </td>
                    <td>${fileSize}</td>
                    <td>${lastModified}</td>
                </tr>
            `;

            tableBody.append(row);
        });
    }

    // เลือกไฟล์ทั้งหมด
    $('#select-all-btn, #select-all-files').on('click', function() {
        $('.file-checkbox').prop('checked', true);
        $('#select-all-files').prop('checked', true);
        updateDeleteButtonState();
    });

    // ยกเลิกการเลือกไฟล์ทั้งหมด
    $('#deselect-all-btn').on('click', function() {
        $('.file-checkbox').prop('checked', false);
        $('#select-all-files').prop('checked', false);
        updateDeleteButtonState();
    });

    // ตรวจสอบการเลือกไฟล์
    $(document).on('change', '.file-checkbox', function() {
        updateDeleteButtonState();
        updateSelectAllCheckbox();
    });

    // อัปเดตสถานะปุ่มลบ
    function updateDeleteButtonState() {
        const selectedCount = $('.file-checkbox:checked').length;
        $('#delete-selected-btn').prop('disabled', selectedCount === 0);
    }

    // อัปเดตสถานะ checkbox เลือกทั้งหมด
    function updateSelectAllCheckbox() {
        const totalFiles = $('.file-checkbox').length;
        const selectedFiles = $('.file-checkbox:checked').length;

        $('#select-all-files').prop('checked', totalFiles > 0 && totalFiles === selectedFiles);
    }

    // ลบไฟล์ที่เลือก
    $('#delete-selected-btn').on('click', function() {
        const selectedFiles = [];

        $('.file-checkbox:checked').each(function() {
            selectedFiles.push({
                path: $(this).data('path'),
                size: $(this).data('size')
            });
        });

        if (selectedFiles.length === 0) {
            return;
        }

        // ยืนยันการลบไฟล์
        Swal.fire({
            title: 'ยืนยันการลบไฟล์',
            html: `
                <div class="text-start">
                    <p>คุณต้องการลบไฟล์ที่เลือกทั้งหมด <strong>${selectedFiles.length}</strong> ไฟล์ใช่หรือไม่?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>การลบไฟล์ไม่สามารถเรียกคืนได้</p>
                </div>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash-alt me-2"></i>ใช่, ลบไฟล์',
            cancelButtonText: '<i class="fas fa-times me-2"></i>ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบไฟล์',
                    html: '<div class="text-center"><i class="fas fa-spinner fa-spin fa-3x mb-3"></i><p>กรุณารอสักครู่...</p></div>',
                    allowOutsideClick: false,
                    showConfirmButton: false
                });

                // ส่งคำขอลบไฟล์
                $.ajax({
                    url: window.cleanupRoutes.delete,
                    type: 'POST',
                    data: {
                        _token: window.csrfToken,
                        files: selectedFiles
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'ลบไฟล์สำเร็จ',
                                html: `
                                    <div class="text-start">
                                        <p>ลบไฟล์สำเร็จ <strong>${response.deleted}</strong> ไฟล์ (${response.totalSizeDeletedFormatted})</p>
                                        ${response.failed > 0 ? `<p class="text-warning">ล้มเหลว ${response.failed} ไฟล์</p>` : ''}
                                    </div>
                                `
                            }).then(() => {
                                // ค้นหาไฟล์ขยะอีกครั้ง
                                $('#scan-btn').click();
                            });
                        } else {
                            showErrorAlert('เกิดข้อผิดพลาด', response.message || 'ไม่สามารถลบไฟล์ได้');
                        }
                    },
                    error: function(xhr) {
                        showErrorAlert('เกิดข้อผิดพลาด', 'ไม่สามารถลบไฟล์ได้');
                    }
                });
            }
        });
    });

    // แสดงข้อความแจ้งเตือนข้อผิดพลาด
    function showErrorAlert(title, message) {
        Swal.fire({
            icon: 'error',
            title: title,
            text: message
        });
    }

    // Utility functions
    function formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getFileExtension(filename) {
        return filename.split('.').pop().toLowerCase();
    }

    function getFileTypeFromExtension(extension) {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
        const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];

        if (imageExtensions.includes(extension)) {
            return 'image';
        } else if (documentExtensions.includes(extension)) {
            return 'file';
        } else {
            return 'other';
        }
    }

    function getFileTypeIcon(extension) {
        const iconMap = {
            // Images
            'jpg': 'fas fa-image',
            'jpeg': 'fas fa-image',
            'png': 'fas fa-image',
            'gif': 'fas fa-image',
            'svg': 'fas fa-image',
            'webp': 'fas fa-image',
            // Documents
            'pdf': 'fas fa-file-pdf',
            'doc': 'fas fa-file-word',
            'docx': 'fas fa-file-word',
            'xls': 'fas fa-file-excel',
            'xlsx': 'fas fa-file-excel',
            'ppt': 'fas fa-file-powerpoint',
            'pptx': 'fas fa-file-powerpoint',
            // Audio
            'mp3': 'fas fa-file-audio',
            'wav': 'fas fa-file-audio',
            'ogg': 'fas fa-file-audio',
            // Video
            'mp4': 'fas fa-file-video',
            'avi': 'fas fa-file-video',
            'mov': 'fas fa-file-video',
            // Text
            'txt': 'fas fa-file-alt',
            'csv': 'fas fa-file-csv',
            // Archive
            'zip': 'fas fa-file-archive',
            'rar': 'fas fa-file-archive',
            '7z': 'fas fa-file-archive'
        };

        return iconMap[extension] || 'fas fa-file';
    }

    function getFileTypeLabel(fileType) {
        switch (fileType) {
            case 'image':
                return 'รูปภาพ';
            case 'file':
                return 'ไฟล์อื่นๆ';
            case 'document':
                return 'ไฟล์อื่นๆ';
            case 'other':
                return 'ไฟล์อื่นๆ';
            case 'settings':
                return 'ไฟล์ Settings';
            case 'categories':
                return 'ไฟล์ Categories';
            case 'profiles':
                return 'ไฟล์ Profiles';
            default:
                return 'ไม่ทราบประเภท';
        }
    }

    window.cleanupUtils = {
        formatBytes: formatBytes,
        getFileExtension: getFileExtension,
        getFileTypeFromExtension: getFileTypeFromExtension,
        getFileTypeIcon: getFileTypeIcon,
        getFileTypeLabel: getFileTypeLabel
    };
});
