/**
 * Location Selector Script
 *
 * This script handles the dynamic loading of provinces based on country selection.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get the country and province select elements
    const countrySelect = document.getElementById('country');
    const provinceSelect = document.getElementById('province');

    if (countrySelect && provinceSelect) {
        // Add event listener to country select
        countrySelect.addEventListener('change', function() {
            const countryCode = this.value;

            // Clear province select
            provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';

            // If no country is selected or not Thailand, disable province select
            if (!countryCode || countryCode.toLowerCase() !== 'th') {
                provinceSelect.disabled = true;
                provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';
                return;
            }

            // Enable province select only for Thailand
            provinceSelect.disabled = false;

            // Show loading indicator
            provinceSelect.innerHTML = '<option value="">กำลังโหลดข้อมูล...</option>';

            // Fetch provinces for selected country (convert to uppercase for API)
            // ดึงค่า subfolder จาก meta tag
            const appSubfolder = document.querySelector('meta[name="app-subfolder"]')?.content || '';
            console.log('APP_SUBFOLDER from meta tag:', appSubfolder);

            // สร้าง URL สำหรับเรียก API
            const apiUrl = appSubfolder ? `/${appSubfolder}/locations/provinces/${countryCode.toUpperCase()}` : `/locations/provinces/${countryCode.toUpperCase()}`;
            console.log('Using API URL:', apiUrl);

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(provinces => {
                    // Reset province select
                    provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';

                    // Add province options
                    provinces.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.code;
                        option.textContent = province.name_th;
                        provinceSelect.appendChild(option);
                    });

                    // If there are no provinces, show message
                    if (provinces.length === 0) {
                        provinceSelect.innerHTML = '<option value="">ไม่พบข้อมูลจังหวัด</option>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching provinces:', error);
                    // แสดงข้อความข้อผิดพลาดที่ละเอียดขึ้น
                    provinceSelect.innerHTML = `<option value="">เกิดข้อผิดพลาดในการโหลดข้อมูล: ${error.message}</option>`;

                    // แสดงข้อมูลเพิ่มเติมเพื่อการแก้ไขปัญหา
                    console.log('API URL used:', apiUrl);
                    console.log('Error details:', error);

                    // ลองใช้ URL สำรอง
                    const fallbackUrl = appSubfolder === 'dc' ? `/locations/provinces/${countryCode.toUpperCase()}` : `/dc/locations/provinces/${countryCode.toUpperCase()}`;
                    console.log('Trying fallback URL:', fallbackUrl);

                    // ลองเรียก API ด้วย URL สำรอง
                    fetch(fallbackUrl)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Fallback URL also failed with status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(provinces => {
                            console.log('Fallback URL succeeded!');
                            // Reset province select
                            provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';

                            // Add province options
                            provinces.forEach(province => {
                                const option = document.createElement('option');
                                option.value = province.code;
                                option.textContent = province.name_th;
                                provinceSelect.appendChild(option);
                            });

                            // If there are no provinces, show message
                            if (provinces.length === 0) {
                                provinceSelect.innerHTML = '<option value="">ไม่พบข้อมูลจังหวัด</option>';
                            }
                        })
                        .catch(fallbackError => {
                            console.error('Fallback URL also failed:', fallbackError);
                            provinceSelect.innerHTML = '<option value="">ไม่สามารถโหลดข้อมูลจังหวัดได้</option>';
                        });
                });
        });

        // Trigger change event if country is already selected (for edit page)
        if (countrySelect.value) {
            // We use setTimeout to ensure this runs after the page is fully loaded
            setTimeout(() => {
                // Store the current province value to reselect it after loading
                const currentProvinceValue = provinceSelect.value;
                console.log('Current province value:', currentProvinceValue);

                // Trigger the change event to load provinces
                const event = new Event('change');
                countrySelect.dispatchEvent(event);

                // Function to select the correct province after loading
                const selectProvinceAfterLoading = function() {
                    if (provinceSelect.options.length > 1) {
                        let found = false;

                        // Find and select the option with the stored value (case insensitive)
                        for (let i = 0; i < provinceSelect.options.length; i++) {
                            if (provinceSelect.options[i].value.toLowerCase() === currentProvinceValue.toLowerCase()) {
                                provinceSelect.selectedIndex = i;
                                found = true;
                                console.log('Found matching province:', provinceSelect.options[i].value);
                                break;
                            }
                        }

                        if (!found) {
                            console.log('Province not found in options, trying again...');
                            return; // Keep observer active
                        }

                        // Remove the observer once we've selected the province
                        observer.disconnect();
                    }
                };

                // Use a MutationObserver to watch for changes to the province select
                const observer = new MutationObserver(selectProvinceAfterLoading);
                observer.observe(provinceSelect, { childList: true });
            }, 300); // Increased timeout for better reliability
        } else {
            // If no country is selected or not Thailand, disable province select
            provinceSelect.disabled = true;
            provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';
        }
    }
});
