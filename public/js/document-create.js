// Document Create Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Uppy for images
    const uppyDashboard = document.getElementById('uppy-dashboard');
    if (uppyDashboard) {
        const uploadUrl = uppyDashboard.getAttribute('data-upload-url');
        const isCreatePage = uppyDashboard.getAttribute('data-is-create-page') === 'true';
        const allowedFileTypes = uppyDashboard.getAttribute('data-allowed-file-types');
        const maxFileSize = parseInt(uppyDashboard.getAttribute('data-max-file-size')) || 5;
        const maxNumberOfFiles = parseInt(uppyDashboard.getAttribute('data-max-number-of-files')) || 10;
        
        // Create Uppy instance
        const uppy = new Uppy.Uppy({
            debug: false,
            autoProceed: false,
            restrictions: {
                maxFileSize: maxFileSize * 1024 * 1024,
                maxNumberOfFiles: maxNumberOfFiles,
                allowedFileTypes: allowedFileTypes ? allowedFileTypes.split(',') : null
            },
            locale: Uppy.locales.th_TH
        });
        
        uppy.use(Uppy.Dashboard, {
            inline: true,
            target: '#uppy-dashboard',
            showProgressDetails: true,
            proudlyDisplayPoweredByUppy: false,
            height: 350,
            theme: 'light',
            doneButtonHandler: null,
        });
        
        // Add XHR Upload plugin
        uppy.use(Uppy.XHRUpload, {
            endpoint: uploadUrl,
            formData: true,
            fieldName: 'files',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        // Handle upload button click
        document.getElementById('upload-files-btn').addEventListener('click', function() {
            uppy.upload();
        });
        
        // Handle successful uploads
        uppy.on('upload-success', (file, response) => {
            // Show a subtle notification
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
            });
            
            Toast.fire({
                icon: 'success',
                title: `อัพโหลดไฟล์ ${file.name} สำเร็จ`
            });
        });
        
        uppy.on('complete', (result) => {
            if (result.successful.length > 0) {
                const uploadedFiles = result.successful.map(file => {
                    return {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        data: file.response.body
                    };
                });
                
                // Update hidden input with uploaded files data
                const uploadedFilesInput = document.getElementById('uploaded-files');
                const currentFiles = uploadedFilesInput.value ? JSON.parse(uploadedFilesInput.value) : [];
                const newFiles = [...currentFiles, ...uploadedFiles];
                uploadedFilesInput.value = JSON.stringify(newFiles);
                
                // Display uploaded files
                displayUploadedFiles(newFiles);
                
                // Reset Uppy dashboard
                uppy.reset();
            }
        });
        
        uppy.on('upload-error', (file, error, response) => {
            console.error('Upload error:', error);
            Swal.fire({
                icon: 'error',
                title: 'เกิดข้อผิดพลาดในการอัพโหลด',
                text: `ไม่สามารถอัพโหลดไฟล์ ${file.name} ได้`,
                confirmButtonText: 'ตกลง',
                confirmButtonColor: '#4a6bff'
            });
        });
    }
    
    // Function to display uploaded files
    function displayUploadedFiles(files) {
        const container = document.getElementById('file-preview-container');
        const noFilesMessage = document.getElementById('no-files-message');
        
        if (files.length > 0) {
            // Hide "no files" message
            if (noFilesMessage) {
                noFilesMessage.style.display = 'none';
            }
            
            // Clear container
            container.innerHTML = '';
            
            // Add file previews
            files.forEach((file, index) => {
                const fileData = file.data;
                const fileUrl = fileData.url || fileData.path;
                const fileName = file.name;
                const fileType = file.type;
                
                const filePreview = document.createElement('div');
                filePreview.className = 'file-preview-card';
                filePreview.dataset.fileIndex = index;
                
                let fileIcon = 'fa-file';
                if (fileType.startsWith('image/')) {
                    fileIcon = 'fa-image';
                } else if (fileType === 'application/pdf') {
                    fileIcon = 'fa-file-pdf';
                } else if (fileType.startsWith('audio/')) {
                    fileIcon = 'fa-file-audio';
                } else if (fileType.startsWith('video/')) {
                    fileIcon = 'fa-file-video';
                }
                
                let previewContent = '';
                if (fileType.startsWith('image/')) {
                    previewContent = `<img src="${fileUrl}" alt="${fileName}" class="card-img-top" style="height: 100px; object-fit: cover;">`;
                } else {
                    previewContent = `
                        <div class="d-flex align-items-center justify-content-center bg-light" style="height: 100px;">
                            <i class="fas ${fileIcon} fa-3x text-primary"></i>
                        </div>
                    `;
                }
                
                filePreview.innerHTML = `
                    ${previewContent}
                    <div class="card-body p-2">
                        <p class="card-title mb-1 text-truncate" title="${fileName}">${fileName}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-file-btn" data-file-index="${index}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                container.appendChild(filePreview);
            });
            
            // Add event listeners to delete buttons
            document.querySelectorAll('.delete-file-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const fileIndex = parseInt(this.dataset.fileIndex);
                    
                    Swal.fire({
                        title: 'ยืนยันการลบไฟล์',
                        text: 'คุณแน่ใจหรือไม่ว่าต้องการลบไฟล์นี้?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'ลบไฟล์',
                        cancelButtonText: 'ยกเลิก'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            removeFile(fileIndex);
                            
                            const Toast = Swal.mixin({
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 3000,
                                timerProgressBar: true
                            });
                            
                            Toast.fire({
                                icon: 'success',
                                title: 'ลบไฟล์เรียบร้อยแล้ว'
                            });
                        }
                    });
                });
            });
            
            // Add hover effects
            document.querySelectorAll('.file-preview-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 10px 15px rgba(0, 0, 0, 0.1)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });
        } else {
            // Show "no files" message
            if (noFilesMessage) {
                noFilesMessage.style.display = 'block';
            } else {
                container.innerHTML = '<div class="text-muted small" id="no-files-message">ยังไม่มีไฟล์ที่อัพโหลด</div>';
            }
        }
    }
    
    // Function to remove a file
    function removeFile(index) {
        const uploadedFilesInput = document.getElementById('uploaded-files');
        const files = JSON.parse(uploadedFilesInput.value);
        
        // Remove file from array
        files.splice(index, 1);
        
        // Update hidden input
        uploadedFilesInput.value = JSON.stringify(files);
        
        // Update display
        displayUploadedFiles(files);
    }
    
    // Helper function to format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Initialize display if there are already uploaded files
    const uploadedFilesInput = document.getElementById('uploaded-files');
    if (uploadedFilesInput && uploadedFilesInput.value) {
        const files = JSON.parse(uploadedFilesInput.value);
        displayUploadedFiles(files);
    }
    
    // Handle file type selection
    const fileTypeButtons = document.querySelectorAll('.file-type-btn');
    const uploadSections = document.querySelectorAll('.upload-section');
    const fileUploadTypeInput = document.getElementById('file-upload-type');
    
    fileTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const fileType = this.dataset.type;
            
            // Update hidden input
            fileUploadTypeInput.value = fileType;
            
            // Update active button
            fileTypeButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Show/hide upload sections
            uploadSections.forEach(section => {
                section.style.display = 'none';
                section.style.opacity = '0';
            });
            
            const targetSection = document.getElementById(`${fileType}-upload-section`);
            if (targetSection) {
                targetSection.style.display = 'block';
                
                // Add a nice fade-in effect
                targetSection.style.transition = 'opacity 0.3s ease';
                
                setTimeout(() => {
                    targetSection.style.opacity = '1';
                }, 10);
            }
        });
        
        // Add hover effects
        button.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
            }
        });
        
        button.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = '';
            }
        });
    });
    
    // Form validation enhancement
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Check required fields
            const requiredFields = form.querySelectorAll('[required]');
            let hasErrors = false;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    hasErrors = true;
                    field.classList.add('is-invalid');
                    
                    // Add error message if not exists
                    const errorDiv = field.nextElementSibling;
                    if (!errorDiv || !errorDiv.classList.contains('invalid-feedback')) {
                        const newErrorDiv = document.createElement('div');
                        newErrorDiv.className = 'invalid-feedback';
                        newErrorDiv.textContent = 'กรุณากรอกข้อมูลในช่องนี้';
                        field.parentNode.insertBefore(newErrorDiv, field.nextSibling);
                    }
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (hasErrors) {
                e.preventDefault();
                
                // Scroll to first error
                const firstError = form.querySelector('.is-invalid');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstError.focus();
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'กรุณากรอกข้อมูลให้ครบถ้วน',
                    text: 'กรุณาตรวจสอบข้อมูลที่กรอกและลองใหม่อีกครั้ง',
                    confirmButtonText: 'ตกลง',
                    confirmButtonColor: '#4a6bff'
                });
            } else {
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึกข้อมูล...';
                    
                    // Re-enable button after 10 seconds (in case of error)
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }, 10000);
                }
            }
        });
        
        // Add input event listeners to remove validation errors when user types
        form.querySelectorAll('input, select, textarea').forEach(field => {
            field.addEventListener('input', function() {
                this.classList.remove('is-invalid');
                const errorDiv = this.nextElementSibling;
                if (errorDiv && errorDiv.classList.contains('invalid-feedback')) {
                    errorDiv.textContent = '';
                }
            });
        });
    }
    
    // Add animation to cards
    const cards = document.querySelectorAll('.card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 15px rgba(0, 0, 0, 0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
        });
    });
    
    // Check for duplicate identifier
    const identifierInput = document.getElementById('identifier_no');
    if (identifierInput) {
        const identifierFeedback = document.createElement('div');
        identifierFeedback.className = 'mt-1';
        identifierInput.parentNode.insertBefore(identifierFeedback, identifierInput.nextSibling.nextSibling);
        
        let checkTimeout;
        
        identifierInput.addEventListener('input', function() {
            const identifier = this.value.trim();
            clearTimeout(checkTimeout);
            
            // Clear feedback if empty
            if (!identifier) {
                identifierFeedback.innerHTML = '';
                return;
            }
            
            // Set a timeout to avoid too many requests
            checkTimeout = setTimeout(function() {
                // Make AJAX request to check identifier
                fetch('/admin/documents/check-identifier', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ identifier: identifier })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.exists) {
                        identifierFeedback.innerHTML = `<div class="alert alert-danger py-1 px-2 mt-1 mb-0"><i class="fas fa-exclamation-triangle me-1"></i> ${data.message}</div>`;
                        identifierInput.classList.add('is-invalid');
                    } else {
                        identifierFeedback.innerHTML = `<div class="alert alert-success py-1 px-2 mt-1 mb-0"><i class="fas fa-check-circle me-1"></i> ${data.message}</div>`;
                        identifierInput.classList.remove('is-invalid');
                    }
                })
                .catch(error => {
                    console.error('Error checking identifier:', error);
                });
            }, 500); // Wait 500ms after user stops typing
        });
    }
    
    // Add event listener for page unload to clean up temporary files
    window.addEventListener('beforeunload', function() {
        // Send a cleanup request to the server
        // Using navigator.sendBeacon to ensure the request is sent even if the page is unloading
        if (navigator.sendBeacon) {
            const formData = new FormData();
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            navigator.sendBeacon('/admin/documents/temp-cleanup', formData);
        } else {
            // Fallback for browsers that don't support sendBeacon
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/documents/temp-cleanup', false); // Synchronous request
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.send('_token=' + document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        }
    });
});
