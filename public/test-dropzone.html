<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropzone</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Dropzone CSS -->
    <link rel="stylesheet" href="https://unpkg.com/dropzone@6.0.0-beta.2/dist/dropzone.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/admin-forms.css">
    
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            padding: 20px;
            background: #f8f9fc;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">🧪 Dropzone Test Page</h1>
        
        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Font Awesome: <span id="fa-status">Loading...</span><br>
            Dropzone: <span id="dz-status">Loading...</span><br>
            Config Function: <span id="config-status">Loading...</span>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">📁 File Upload Test</h5>
            </div>
            <div class="card-body">
                <div id="dropzone-container">
                    <form id="dropzone-upload" class="dropzone" action="/upload" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="_token" value="test-token">
                    </form>
                </div>
                
                <div class="mt-3">
                    <h6>📋 Test Results:</h6>
                    <ul id="test-results" class="list-unstyled">
                        <li>⏳ Waiting for tests...</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Manual Test Elements -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">🎨 CSS Test Elements</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>File Type Icons Test:</h6>
                        <div class="d-flex flex-wrap gap-3">
                            <div class="dropzone">
                                <div class="dz-preview" data-type="image/jpeg">
                                    <div class="dz-image">
                                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5JTUc8L3RleHQ+PC9zdmc+" alt="test">
                                    </div>
                                    <div class="dz-filename-display">image.jpg</div>
                                    <a href="#" class="dz-remove">ลบไฟล์</a>
                                </div>
                            </div>
                            
                            <div class="dropzone">
                                <div class="dz-preview" data-type="application/pdf">
                                    <div class="dz-image non-image-file"></div>
                                    <div class="dz-filename-display">document.pdf</div>
                                    <a href="#" class="dz-remove">ลบไฟล์</a>
                                </div>
                            </div>
                            
                            <div class="dropzone">
                                <div class="dz-preview" data-type="audio/mp3">
                                    <div class="dz-image non-image-file"></div>
                                    <div class="dz-filename-display">music.mp3</div>
                                    <a href="#" class="dz-remove">ลบไฟล์</a>
                                </div>
                            </div>
                            
                            <div class="dropzone">
                                <div class="dz-preview" data-type="video/mp4">
                                    <div class="dz-image non-image-file"></div>
                                    <div class="dz-filename-display">video.mp4</div>
                                    <a href="#" class="dz-remove">ลบไฟล์</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Font Awesome Icons Test:</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <i class="fas fa-file" style="font-size: 2rem; color: #858796;"></i>
                            <i class="fas fa-image" style="font-size: 2rem; color: #4e73df;"></i>
                            <i class="fas fa-file-pdf" style="font-size: 2rem; color: #e74a3b;"></i>
                            <i class="fas fa-music" style="font-size: 2rem; color: #1cc88a;"></i>
                            <i class="fas fa-video" style="font-size: 2rem; color: #f6c23e;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Dropzone JS -->
    <script src="https://unpkg.com/dropzone@6.0.0-beta.2/dist/dropzone-min.js"></script>
    
    <!-- Dropzone Config -->
    <script src="js/dropzone-config.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const results = document.getElementById('test-results');
            const faStatus = document.getElementById('fa-status');
            const dzStatus = document.getElementById('dz-status');
            const configStatus = document.getElementById('config-status');
            
            // Test Font Awesome
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-file';
            document.body.appendChild(testIcon);
            const iconStyle = window.getComputedStyle(testIcon);
            if (iconStyle.fontFamily.includes('Font Awesome')) {
                faStatus.textContent = '✅ Loaded';
                faStatus.style.color = 'green';
            } else {
                faStatus.textContent = '❌ Failed';
                faStatus.style.color = 'red';
            }
            document.body.removeChild(testIcon);
            
            // Test Dropzone
            if (typeof Dropzone !== 'undefined') {
                dzStatus.textContent = '✅ Loaded';
                dzStatus.style.color = 'green';
            } else {
                dzStatus.textContent = '❌ Failed';
                dzStatus.style.color = 'red';
            }
            
            // Test Config Function
            if (typeof initDropzone === 'function') {
                configStatus.textContent = '✅ Available';
                configStatus.style.color = 'green';
            } else {
                configStatus.textContent = '❌ Not Available';
                configStatus.style.color = 'red';
            }
            
            // Clear results
            results.innerHTML = '';
            
            // Test CSS Selectors
            const testElements = document.querySelectorAll('.dz-preview');
            testElements.forEach((element, index) => {
                const type = element.getAttribute('data-type');
                const afterStyle = window.getComputedStyle(element.querySelector('.dz-image'), '::after');
                const filenameDisplay = element.querySelector('.dz-filename-display');
                
                const li = document.createElement('li');
                li.innerHTML = `
                    <strong>Element ${index + 1} (${type}):</strong><br>
                    - ::after content: ${afterStyle.content || 'none'}<br>
                    - ::after display: ${afterStyle.display || 'none'}<br>
                    - Filename display: ${filenameDisplay ? '✅ Found' : '❌ Missing'}<br>
                    - Remove button: ${element.querySelector('.dz-remove') ? '✅ Found' : '❌ Missing'}
                `;
                results.appendChild(li);
            });
            
            // Try to initialize Dropzone
            if (typeof initDropzone === 'function' && typeof Dropzone !== 'undefined') {
                try {
                    const dropzone = initDropzone('#dropzone-upload', '/upload', 'test-token', 'mixed', function(fileData, file) {
                        console.log('File processed:', fileData, file);
                    });
                    
                    const li = document.createElement('li');
                    li.innerHTML = `<strong>Dropzone Init:</strong> ${dropzone ? '✅ Success' : '❌ Failed'}`;
                    results.appendChild(li);
                } catch (error) {
                    const li = document.createElement('li');
                    li.innerHTML = `<strong>Dropzone Init:</strong> ❌ Error: ${error.message}`;
                    results.appendChild(li);
                }
            }
        });
    </script>
</body>
</html>
