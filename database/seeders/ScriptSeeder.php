<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Script;

class ScriptSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $scripts = [
            [
                'id' => 1,
                'name' => 'ไทย',

            ],
            [
                'id' => 2,
                'name' => 'ล้านนา (ตั๋วเมือง)',

            ],
            [
                'id' => 3,
                'name' => 'ขอม',

            ],
            [
                'id' => 4,
                'name' => 'มอญ',

            ],
            [
                'id' => 5,
                'name' => 'ไทลื้อ',

            ],
            [
                'id' => 6,
                'name' => 'ไทเขิน',

            ],
        ];

        foreach ($scripts as $script) {
            Script::create($script);
        }
    }
}
