<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Language;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            [
                'id' => 1,
                'name' => 'ไทย',

            ],
            [
                'id' => 2,
                'name' => 'ล้านนา',

            ],
            [
                'id' => 3,
                'name' => 'บาลี',

            ],
            [
                'id' => 4,
                'name' => 'สันสกฤต',

            ],
            [
                'id' => 5,
                'name' => 'มอญ',

            ],
            [
                'id' => 6,
                'name' => 'ไทลื้อ',

            ],
            [
                'id' => 7,
                'name' => 'ไทเขิน',

            ],
        ];

        foreach ($languages as $language) {
            Language::create($language);
        }
    }
}
