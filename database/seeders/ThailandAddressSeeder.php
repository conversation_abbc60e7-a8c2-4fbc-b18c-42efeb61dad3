<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ThailandAddressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed geographies
        $geographies = [
            ['id' => 1, 'name' => 'ภาคเหนือ'],
            ['id' => 2, 'name' => 'ภาคกลาง'],
            ['id' => 3, 'name' => 'ภาคตะวันออกเฉียงเหนือ'],
            ['id' => 4, 'name' => 'ภาคตะวันตก'],
            ['id' => 5, 'name' => 'ภาคตะวันออก'],
            ['id' => 6, 'name' => 'ภาคใต้'],
        ];

        foreach ($geographies as $geography) {
            DB::table('geographies')->updateOrInsert(
                ['id' => $geography['id']],
                $geography
            );
        }

        // Seed provinces (sample of northern provinces)
        $provinces = [
            ['id' => 1, 'name_th' => 'กรุงเทพมหานคร', 'name_en' => 'Bangkok', 'geography_id' => 2, 'code' => '10'],
            ['id' => 2, 'name_th' => 'เชียงใหม่', 'name_en' => 'Chiang Mai', 'geography_id' => 1, 'code' => '50'],
            ['id' => 3, 'name_th' => 'เชียงราย', 'name_en' => 'Chiang Rai', 'geography_id' => 1, 'code' => '57'],
            ['id' => 4, 'name_th' => 'น่าน', 'name_en' => 'Nan', 'geography_id' => 1, 'code' => '55'],
            ['id' => 5, 'name_th' => 'พะเยา', 'name_en' => 'Phayao', 'geography_id' => 1, 'code' => '56'],
            ['id' => 6, 'name_th' => 'แพร่', 'name_en' => 'Phrae', 'geography_id' => 1, 'code' => '54'],
            ['id' => 7, 'name_th' => 'แม่ฮ่องสอน', 'name_en' => 'Mae Hong Son', 'geography_id' => 1, 'code' => '58'],
            ['id' => 8, 'name_th' => 'ลำปาง', 'name_en' => 'Lampang', 'geography_id' => 1, 'code' => '52'],
            ['id' => 9, 'name_th' => 'ลำพูน', 'name_en' => 'Lamphun', 'geography_id' => 1, 'code' => '51'],
            ['id' => 10, 'name_th' => 'อุตรดิตถ์', 'name_en' => 'Uttaradit', 'geography_id' => 1, 'code' => '53'],
        ];

        foreach ($provinces as $province) {
            DB::table('provinces')->updateOrInsert(
                ['id' => $province['id']],
                $province
            );
        }

        // Seed amphures (sample for Chiang Mai)
        $amphures = [
            ['id' => 1, 'name_th' => 'เมืองเชียงใหม่', 'name_en' => 'Mueang Chiang Mai', 'province_id' => 2, 'code' => '5001'],
            ['id' => 2, 'name_th' => 'จอมทอง', 'name_en' => 'Chom Thong', 'province_id' => 2, 'code' => '5002'],
            ['id' => 3, 'name_th' => 'แม่แจ่ม', 'name_en' => 'Mae Chaem', 'province_id' => 2, 'code' => '5003'],
            ['id' => 4, 'name_th' => 'เชียงดาว', 'name_en' => 'Chiang Dao', 'province_id' => 2, 'code' => '5004'],
            ['id' => 5, 'name_th' => 'ดอยสะเก็ด', 'name_en' => 'Doi Saket', 'province_id' => 2, 'code' => '5005'],
            ['id' => 6, 'name_th' => 'แม่แตง', 'name_en' => 'Mae Taeng', 'province_id' => 2, 'code' => '5006'],
            ['id' => 7, 'name_th' => 'แม่ริม', 'name_en' => 'Mae Rim', 'province_id' => 2, 'code' => '5007'],
            ['id' => 8, 'name_th' => 'สะเมิง', 'name_en' => 'Samoeng', 'province_id' => 2, 'code' => '5008'],
            ['id' => 9, 'name_th' => 'ฝาง', 'name_en' => 'Fang', 'province_id' => 2, 'code' => '5009'],
            ['id' => 10, 'name_th' => 'แม่อาย', 'name_en' => 'Mae Ai', 'province_id' => 2, 'code' => '5010'],
        ];

        foreach ($amphures as $amphure) {
            DB::table('amphures')->updateOrInsert(
                ['id' => $amphure['id']],
                $amphure
            );
        }

        // Seed districts (sample for Mueang Chiang Mai)
        $districts = [
            ['id' => 1, 'name_th' => 'ช้างเผือก', 'name_en' => 'Chang Phueak', 'amphure_id' => 1, 'code' => '500101', 'zip_code' => '50300'],
            ['id' => 2, 'name_th' => 'ศรีภูมิ', 'name_en' => 'Si Phum', 'amphure_id' => 1, 'code' => '500102', 'zip_code' => '50200'],
            ['id' => 3, 'name_th' => 'พระสิงห์', 'name_en' => 'Phra Sing', 'amphure_id' => 1, 'code' => '500103', 'zip_code' => '50200'],
            ['id' => 4, 'name_th' => 'เวียงเหนือ', 'name_en' => 'Wiang Nuea', 'amphure_id' => 1, 'code' => '500104', 'zip_code' => '50300'],
            ['id' => 5, 'name_th' => 'ช้างคลาน', 'name_en' => 'Chang Khlan', 'amphure_id' => 1, 'code' => '500105', 'zip_code' => '50100'],
            ['id' => 6, 'name_th' => 'วัดเกต', 'name_en' => 'Wat Ket', 'amphure_id' => 1, 'code' => '500106', 'zip_code' => '50000'],
            ['id' => 7, 'name_th' => 'ช้างม่อย', 'name_en' => 'Chang Moi', 'amphure_id' => 1, 'code' => '500107', 'zip_code' => '50300'],
            ['id' => 8, 'name_th' => 'สุเทพ', 'name_en' => 'Suthep', 'amphure_id' => 1, 'code' => '500108', 'zip_code' => '50200'],
            ['id' => 9, 'name_th' => 'แม่เหียะ', 'name_en' => 'Mae Hia', 'amphure_id' => 1, 'code' => '500109', 'zip_code' => '50100'],
            ['id' => 10, 'name_th' => 'ป่าตัน', 'name_en' => 'Pa Tan', 'amphure_id' => 1, 'code' => '500110', 'zip_code' => '50300'],
        ];

        foreach ($districts as $district) {
            DB::table('districts')->updateOrInsert(
                ['id' => $district['id']],
                $district
            );
        }
    }
}
