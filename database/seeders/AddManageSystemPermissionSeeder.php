<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AddManageSystemPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create manage system permission if it doesn't exist
        if (!Permission::where('name', 'manage system')->exists()) {
            $permission = Permission::create(['name' => 'manage system']);
            $this->command->info('Created permission: manage system');
            
            // Assign to admin role
            $adminRole = Role::where('name', 'admin')->first();
            if ($adminRole) {
                $adminRole->givePermissionTo($permission);
                $this->command->info('Assigned manage system permission to admin role');
            }
        } else {
            $this->command->info('Permission manage system already exists');
        }
    }
}
