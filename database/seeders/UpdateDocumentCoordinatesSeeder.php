<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Document;

class UpdateDocumentCoordinatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // อัปเดตพิกัดสำหรับเอกสารที่ 1 (คัมภีร์ใบลานวัดพระธาตุศรีจอมทอง)
        $document1 = Document::find(1);
        if ($document1) {
            $document1->update([
                'latitude' => 18.4196,
                'longitude' => 98.9962,
                'location' => 'วัดพระธาตุศรีจอมทอง',
                'district' => 'จอมทอง',
                'province' => 'เชียงใหม่',
                'country' => 'ไทย'
            ]);
            $this->command->info('Updated coordinates for document 1');
        }

        // อัปเดตพิกัดสำหรับเอกสารที่ 2 (ถ้ามี)
        $document2 = Document::find(2);
        if ($document2) {
            $document2->update([
                'latitude' => 13.7563,
                'longitude' => 100.5018,
                'location' => 'วัดพระแก้ว',
                'district' => 'พระนคร',
                'province' => 'กรุงเทพมหานคร',
                'country' => 'ไทย'
            ]);
            $this->command->info('Updated coordinates for document 2');
        }

        // อัปเดตพิกัดสำหรับเอกสารที่ 3 (ถ้ามี)
        $document3 = Document::find(3);
        if ($document3) {
            $document3->update([
                'latitude' => 18.7872,
                'longitude' => 98.9845,
                'location' => 'วัดพระธาตุดอยสุเทพ',
                'district' => 'เมือง',
                'province' => 'เชียงใหม่',
                'country' => 'ไทย'
            ]);
            $this->command->info('Updated coordinates for document 3');
        }
    }
}
