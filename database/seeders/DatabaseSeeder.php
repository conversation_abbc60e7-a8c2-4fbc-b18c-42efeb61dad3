<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // สร้างผู้ใช้แอดมินด้วย UserSeeder
        $this->call(UserSeeder::class);

        $this->call([
            RolesAndPermissionsSeeder::class,
            AdminUserSeeder::class, // Add admin user for testing
            CategorySeeder::class,
            ItemTypeSeeder::class,
            MaterialSeeder::class,
            LanguageSeeder::class,
            ScriptSeeder::class,
            ThailandAddressSeeder::class, // Add Thailand address data
            CountrySeeder::class,
            LocationSeeder::class,
            ItemSeeder::class,
            // UpdateManuscriptCoordinatesSeeder::class, // Uncomment to update coordinates from manuscript.sql
        ]);
    }
}
