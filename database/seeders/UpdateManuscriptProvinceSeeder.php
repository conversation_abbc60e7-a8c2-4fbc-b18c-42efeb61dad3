<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Document;
use App\Models\Province;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateManuscriptProvinceSeeder extends Seeder
{
    /**
     * Parse columns from a SQL row
     */
    private function parseColumns($row)
    {
        $columns = [];
        $inQuote = false;
        $currentColumn = '';

        for ($i = 0; $i < strlen($row); $i++) {
            $char = $row[$i];

            if ($char === "'" && ($i === 0 || $row[$i-1] !== '\\')) {
                $inQuote = !$inQuote;
                $currentColumn .= $char;
            } elseif ($char === ',' && !$inQuote) {
                $columns[] = trim($currentColumn);
                $currentColumn = '';
            } else {
                $currentColumn .= $char;
            }
        }

        // Add the last column
        if (!empty($currentColumn)) {
            $columns[] = trim($currentColumn);
        }

        return $columns;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting to update document provinces from manuscript.sql...');

        // อ่านไฟล์ manuscript.sql
        $sqlFilePath = $_SERVER['HOME'] . '/Downloads/manuscript.sql';

        if (!file_exists($sqlFilePath)) {
            $this->command->error('manuscript.sql file not found at: ' . $sqlFilePath);
            return;
        }

        $sqlContent = file_get_contents($sqlFilePath);

        // แยกข้อมูลจากไฟล์ SQL โดยหาบรรทัดที่มีการ INSERT
        preg_match_all("/INSERT INTO `manuscript`.*?VALUES\s*\((.*?)\);/s", $sqlContent, $insertMatches);

        $updatedCount = 0;
        $notFoundCount = 0;
        $noProvinceCount = 0;

        // โหลดข้อมูลจังหวัดจากฐานข้อมูล
        $provinces = Province::all()->pluck('id', 'code')->toArray();
        $provinceNames = Province::all()->pluck('name_th', 'id')->toArray();

        foreach ($insertMatches[1] as $insertData) {
            // แยกข้อมูลแต่ละแถว
            $rows = explode("),\n(", $insertData);

            foreach ($rows as $row) {
                // แยกข้อมูลในแต่ละคอลัมน์
                $columns = $this->parseColumns($row);

                if (count($columns) < 30) {
                    continue; // ข้ามถ้าข้อมูลไม่ครบ
                }

                $manuscriptId = $columns[0];
                $identifierNo = $columns[1];
                $provinceCode = $columns[24]; // provinces column

                // ลบเครื่องหมาย quote ออกจากค่า
                $identifierNo = trim($identifierNo, "'");
                $provinceCode = trim($provinceCode, "'");

                // ตรวจสอบว่ามีค่าจังหวัดหรือไม่
                if (empty($provinceCode) || $provinceCode === 'NULL') {
                    $noProvinceCount++;
                    continue;
                }

                // ค้นหาเอกสารด้วย identifier_no
                $document = Document::where('identifier_no', $identifierNo)->first();

                if ($document) {
                    // หาชื่อจังหวัดจากรหัสจังหวัด
                    $provinceName = null;

                    // ตรวจสอบว่ามีข้อมูลจังหวัดในฐานข้อมูลหรือไม่
                    if (isset($provinces[$provinceCode])) {
                        $provinceId = $provinces[$provinceCode];
                        $provinceName = $provinceNames[$provinceId];
                    } else {
                        // ถ้าไม่พบในฐานข้อมูล ให้ดึงข้อมูลจากตำแหน่งที่อยู่ในฟิลด์ location
                        $location = $document->location;
                        if (!empty($location)) {
                            // ค้นหาชื่อจังหวัดจากข้อความในฟิลด์ location
                            if (preg_match('/จ\.([\p{Thai}\s]+)/u', $location, $matches)) {
                                $provinceName = trim($matches[1]);
                            } elseif (preg_match('/จังหวัด([\p{Thai}\s]+)/u', $location, $matches)) {
                                $provinceName = trim($matches[1]);
                            }
                        }
                    }

                    if ($provinceName) {
                        // อัปเดตชื่อจังหวัด
                        $document->update([
                            'province' => $provinceName
                        ]);

                        $updatedCount++;
                        $this->command->info("Updated province for document with identifier_no: $identifierNo (Province: $provinceName)");
                    } else {
                        $this->command->warn("Province code not found: $provinceCode for document: $identifierNo");
                        $noProvinceCount++;
                    }
                } else {
                    $notFoundCount++;
                    $this->command->warn("Document with identifier_no: $identifierNo not found");
                }
            }
        }

        $this->command->info("Province update completed:");
        $this->command->info("- Updated: $updatedCount documents");
        $this->command->info("- Not found: $notFoundCount documents");
        $this->command->info("- No province: $noProvinceCount documents");
    }
}
