<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ItemType;

class ItemTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $itemTypes = [
            [
                'id' => 1,
                'name' => 'คัมภีร์ใบลาน',

            ],
            [
                'id' => 2,
                'name' => 'พับสา',

            ],
            [
                'id' => 3,
                'name' => 'จารึก',

            ],
            [
                'id' => 4,
                'name' => 'สมุดข่อย',

            ],
            [
                'id' => 5,
                'name' => 'เสียง',

            ],
            [
                'id' => 6,
                'name' => 'วิดีโอ',

            ],
        ];

        foreach ($itemTypes as $itemType) {
            ItemType::create($itemType);
        }
    }
}
