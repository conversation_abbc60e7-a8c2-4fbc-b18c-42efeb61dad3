<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'ใบลาน',
                'description' => 'เอกสารโบราณที่บันทึกบนใบลาน',
                'image_path' => 'placeholder.svg',
            ],
            [
                'name' => 'พับสา',
                'description' => 'เอกสารโบราณที่บันทึกบนกระดาษพับ',
                'image_path' => 'placeholder.svg',
            ],
            [
                'name' => 'จารึก',
                'description' => 'ข้อความที่สลักบนวัสดุแข็ง เช่น หิน โลหะ',
                'image_path' => 'placeholder.svg',
            ],
            [
                'name' => 'คัมภีร์',
                'description' => 'หนังสือหรือตำราโบราณ',
                'image_path' => 'placeholder.svg',
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
