<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GeographySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing geographies
        DB::table('geographies')->truncate();

        // Geographies data (Thai regions)
        $geographies = [
            ['id' => 1, 'name' => 'ภาคเหนือ'],
            ['id' => 2, 'name' => 'ภาคกลาง'],
            ['id' => 3, 'name' => 'ภาคตะวันออกเฉียงเหนือ'],
            ['id' => 4, 'name' => 'ภาคตะวันตก'],
            ['id' => 5, 'name' => 'ภาคตะวันออก'],
            ['id' => 6, 'name' => 'ภาคใต้'],
        ];

        // Insert geographies
        DB::table('geographies')->insert($geographies);
    }
}
