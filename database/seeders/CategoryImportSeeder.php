<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Category;
use App\Models\Document;

class CategoryImportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Importing categories from SQL data...');

        // Categories data from SQL file
        $categories = [
            ['id' => 1, 'code' => 'A-1', 'name' => 'พระไตรปิฎก - พระวินัยปิฎก'],
            ['id' => 2, 'code' => 'A-2', 'name' => 'พระไตรปิฎก - พระสุดตันตปีฎก'],
            ['id' => 3, 'code' => 'A-3', 'name' => 'พระไตรปิฎก - พระอภิธรรมปีฎก'],
            ['id' => 4, 'code' => 'A-4', 'name' => 'พระไตรปิฎกแบบย่อ'],
            ['id' => 5, 'code' => 'A-5', 'name' => 'อรรภกถา ฎีกา และปกรณ์วิเสสต่างๆ'],
            ['id' => 6, 'code' => 'A-6', 'name' => 'ชาดก'],
            ['id' => 7, 'code' => 'A-7', 'name' => 'ธรรมทั่วไป'],
            ['id' => 8, 'code' => 'C-1', 'name' => 'ตำนาน-พุทธตำนาน'],
            ['id' => 9, 'code' => 'B', 'name' => 'ประวัติศาสตร์'],
            ['id' => 10, 'code' => 'C-2', 'name' => 'ตำนาน-พระสาวก'],
            ['id' => 11, 'code' => 'C-3', 'name' => 'ตำนานปูชนียสถาน - ปูชนียวัตถุ'],
            ['id' => 12, 'code' => 'C-4', 'name' => 'ตำนาน-บุคคลสำคัญ'],
            ['id' => 13, 'code' => 'F', 'name' => 'นิทานพื้นบ้าน'],
            ['id' => 14, 'code' => 'G', 'name' => 'การพยากรณ์เหตุการณ์อนาคต'],
            ['id' => 15, 'code' => 'H', 'name' => 'โหราศาสตร์'],
            ['id' => 16, 'code' => 'I', 'name' => 'จักรวาลวิทยา'],
            ['id' => 17, 'code' => 'J', 'name' => 'ไสยศาสตร์'],
            ['id' => 18, 'code' => 'E', 'name' => 'จริยศาสตร์'],
            ['id' => 19, 'code' => 'D', 'name' => 'อานิสงส์'],
            ['id' => 20, 'code' => 'K', 'name' => 'ภาษาศาสตร์'],
            ['id' => 21, 'code' => 'L', 'name' => 'กฎหมาย'],
            ['id' => 22, 'code' => 'M', 'name' => 'เวชศาสตร์'],
            ['id' => 23, 'code' => 'P', 'name' => 'กวีนิพนธ์'],
            ['id' => 24, 'code' => 'Q', 'name' => 'บทสวด, คำไหวต่างๆ'],
            ['id' => 25, 'code' => 'R', 'name' => 'ปกิณกะ'],
            ['id' => 26, 'code' => 'O', 'name' => 'พิธีกรรมสงฆ์'],
            ['id' => 27, 'code' => 'N', 'name' => 'พิธีกรรมท้องถิ่น'],
        ];

        // Insert categories
        $count = 0;
        foreach ($categories as $category) {
            // Check if category already exists
            $existingCategory = Category::where('code', $category['code'])->orWhere('id', $category['id'])->first();

            if (!$existingCategory) {
                Category::create([
                    'id' => $category['id'],
                    'code' => $category['code'],
                    'name' => $category['name'],
                    'description' => null,
                    'parent_id' => null,
                    'image_path' => 'placeholder.svg',
                ]);
                $count++;
                $this->command->info("Added category: {$category['name']} ({$category['code']})");
            } else {
                $this->command->info("Category already exists: {$category['name']} ({$category['code']})");

                // Update existing category if needed
                $existingCategory->code = $category['code'];
                $existingCategory->name = $category['name'];
                $existingCategory->save();
                $this->command->info("Updated category: {$category['name']} ({$category['code']})");
            }
        }

        $this->command->info("Imported/updated {$count} categories.");

        // Now let's update documents with category codes
        $this->updateDocumentCategories();
    }

    /**
     * Update document categories based on title or other metadata
     */
    private function updateDocumentCategories(): void
    {
        $this->command->info('Updating document categories...');

        // Get all categories
        $categories = Category::all();

        // Default category for documents without a match
        $defaultCategory = Category::where('code', 'R')->first(); // ปกิณกะ
        if (!$defaultCategory) {
            $defaultCategory = Category::first();
        }

        // Get all documents
        $documents = Document::all();
        $updatedCount = 0;

        foreach ($documents as $document) {
            $categoryAssigned = false;

            // Try to match document title with category keywords
            $title = strtolower($document->title);

            // Define category matching rules
            $categoryMatches = [
                'A-1' => ['วินัย', 'พระวินัย', 'วินัยปิฎก'],
                'A-2' => ['สุตตันต', 'สุดตันต', 'สูตร', 'พระสูตร'],
                'A-3' => ['อภิธรรม', 'อภิธัมม'],
                'A-4' => ['ย่อ', 'ย่อธรรม', 'ย่อพระไตรปิฎก'],
                'A-5' => ['อรรถกถา', 'ฎีกา', 'ปกรณ์วิเสส'],
                'A-6' => ['ชาดก', 'นิทาน'],
                'A-7' => ['ธรรม', 'ธรรมะ', 'คำสอน'],
                'C-1' => ['พุทธตำนาน', 'ตำนานพระพุทธ', 'ประวัติพระพุทธ'],
                'B' => ['ประวัติศาสตร์', 'ประวัติ', 'ศาสนา'],
                'C-2' => ['ตำนานพระสาวก', 'ประวัติพระสาวก', 'สาวก'],
                'C-3' => ['ตำนานปูชนียสถาน', 'ปูชนียวัตถุ', 'วัด', 'เจดีย์', 'พระธาตุ'],
                'C-4' => ['ตำนานบุคคล', 'ประวัติบุคคล'],
                'F' => ['นิทานพื้นบ้าน', 'นิทาน', 'เรื่องเล่า'],
                'G' => ['พยากรณ์', 'ทำนาย', 'อนาคต'],
                'H' => ['โหราศาสตร์', 'ดวง', 'ดูดวง', 'ทำนาย'],
                'I' => ['จักรวาล', 'โลกธาตุ', 'ไตรภูมิ'],
                'J' => ['ไสยศาสตร์', 'คาถา', 'อาคม', 'เวทมนตร์'],
                'E' => ['จริยศาสตร์', 'จริยธรรม', 'ศีลธรรม'],
                'D' => ['อานิสงส์', 'บุญ', 'กุศล'],
                'K' => ['ภาษา', 'ไวยากรณ์', 'คำศัพท์', 'พจนานุกรม'],
                'L' => ['กฎหมาย', 'มังรายศาสตร์', 'ธรรมศาสตร์'],
                'M' => ['เวชศาสตร์', 'ยา', 'สมุนไพร', 'รักษา', 'โรค'],
                'P' => ['กวี', 'กวีนิพนธ์', 'กลอน', 'โคลง', 'ฉันท์', 'กาพย์', 'ร่าย'],
                'Q' => ['บทสวด', 'คำไหว้', 'มนต์', 'สวดมนต์'],
                'O' => ['พิธีกรรมสงฆ์', 'สังฆกรรม', 'กรรมวาจา'],
                'N' => ['พิธีกรรมท้องถิ่น', 'ประเพณี', 'พิธี']
            ];

            // Check document title against category keywords
            foreach ($categoryMatches as $code => $keywords) {
                foreach ($keywords as $keyword) {
                    if (mb_stripos($document->title, $keyword) !== false) {
                        // Find category by code
                        $category = $categories->where('code', $code)->first();
                        if ($category) {
                            $document->category_id = $category->id;
                            $document->save();
                            $updatedCount++;
                            $categoryAssigned = true;
                            $this->command->info("Assigned document '{$document->title}' to category '{$category->name}' ({$category->code})");
                            break 2; // Break both loops
                        }
                    }
                }
            }

            // If no category was assigned, use the default category
            if (!$categoryAssigned && !$document->category_id && $defaultCategory) {
                $document->category_id = $defaultCategory->id;
                $document->save();
                $updatedCount++;
                $this->command->info("Assigned document '{$document->title}' to default category '{$defaultCategory->name}' ({$defaultCategory->code})");
            }
        }

        $this->command->info("Updated categories for {$updatedCount} documents.");
    }
}
