<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Items
            'view items',
            'create items',
            'edit items',
            'delete items',

            // Categories
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',

            // Types
            'view types',
            'create types',
            'edit types',
            'delete types',

            // Materials
            'view materials',
            'create materials',
            'edit materials',
            'delete materials',

            // Languages
            'view languages',
            'create languages',
            'edit languages',
            'delete languages',

            // Scripts
            'view scripts',
            'create scripts',
            'edit scripts',
            'delete scripts',

            // Users
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Settings
            'view settings',
            'edit settings',

            // Roles
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',

            // Permissions
            'view permissions',
            'create permissions',
            'edit permissions',
            'delete permissions',
        ];

        foreach ($permissions as $permission) {
            // Check if permission already exists
            if (!Permission::where('name', $permission)->exists()) {
                Permission::create(['name' => $permission]);
            }
        }

        // Create roles and assign permissions

        // Admin role
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Editor role
        $editorRole = Role::firstOrCreate(['name' => 'editor']);
        $editorRole->givePermissionTo([
            'view items', 'create items', 'edit items', 'delete items',
            'view categories', 'view types', 'view materials', 'view languages', 'view scripts',
            'view roles', 'view permissions',
        ]);

        // User role
        $userRole = Role::firstOrCreate(['name' => 'user']);
        $userRole->givePermissionTo([
            'view items',
        ]);

        // Assign roles to existing users based on their 'role' field
        $users = User::all();
        foreach ($users as $user) {
            switch ($user->role) {
                case 'admin':
                    $user->assignRole('admin');
                    break;
                case 'editor':
                    $user->assignRole('editor');
                    break;
                default:
                    $user->assignRole('user');
                    break;
            }
        }
    }
}
