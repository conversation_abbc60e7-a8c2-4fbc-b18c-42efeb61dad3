<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed countries
        $this->seedCountries();

        // Seed geographies
        $this->seedGeographies();

        // Seed provinces
        $this->seedProvinces();

        // Seed amphures
        $this->seedAmphures();
    }

    /**
     * Seed countries table
     */
    private function seedCountries(): void
    {
        // Don't truncate, just check if data exists
        if (DB::table('countries')->count() == 0) {

            // Insert Thailand
            DB::table('countries')->insert([
                'id' => 1,
                'name_th' => 'ประเทศไทย',
                'name_en' => 'Thailand',
                'code' => 'TH',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Insert other countries if needed
            DB::table('countries')->insert([
                'id' => 2,
                'name_th' => 'ลาว',
                'name_en' => 'Laos',
                'code' => 'LA',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('countries')->insert([
                'id' => 3,
                'name_th' => 'กัมพูชา',
                'name_en' => 'Cambodia',
                'code' => 'KH',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('countries')->insert([
                'id' => 4,
                'name_th' => 'พม่า',
                'name_en' => 'Myanmar',
                'code' => 'MM',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('countries')->insert([
                'id' => 5,
                'name_th' => 'มาเลเซีย',
                'name_en' => 'Malaysia',
                'code' => 'MY',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Seed geographies table
     */
    private function seedGeographies(): void
    {
        // Don't truncate, just check if data exists
        if (DB::table('geographies')->count() == 0) {

        // Insert Thai geographies
        $geographies = [
            ['id' => 1, 'name' => 'ภาคเหนือ'],
            ['id' => 2, 'name' => 'ภาคกลาง'],
            ['id' => 3, 'name' => 'ภาคตะวันออกเฉียงเหนือ'],
            ['id' => 4, 'name' => 'ภาคตะวันออก'],
            ['id' => 5, 'name' => 'ภาคตะวันตก'],
            ['id' => 6, 'name' => 'ภาคใต้'],
        ];

            foreach ($geographies as $geography) {
                $geography['created_at'] = now();
                $geography['updated_at'] = now();
                DB::table('geographies')->insert($geography);
            }
        }
    }

    /**
     * Seed provinces table
     */
    private function seedProvinces(): void
    {
        // Don't truncate, just check if data exists
        if (DB::table('provinces')->count() == 0) {

        // Northern provinces
        $northernProvinces = [
            ['id' => 1, 'code' => '50', 'name_th' => 'เชียงใหม่', 'name_en' => 'Chiang Mai', 'geography_id' => 1],
            ['id' => 2, 'code' => '57', 'name_th' => 'เชียงราย', 'name_en' => 'Chiang Rai', 'geography_id' => 1],
            ['id' => 3, 'code' => '51', 'name_th' => 'ลำพูน', 'name_en' => 'Lamphun', 'geography_id' => 1],
            ['id' => 4, 'code' => '52', 'name_th' => 'ลำปาง', 'name_en' => 'Lampang', 'geography_id' => 1],
            ['id' => 5, 'code' => '55', 'name_th' => 'น่าน', 'name_en' => 'Nan', 'geography_id' => 1],
            ['id' => 6, 'code' => '56', 'name_th' => 'พะเยา', 'name_en' => 'Phayao', 'geography_id' => 1],
            ['id' => 7, 'code' => '54', 'name_th' => 'แพร่', 'name_en' => 'Phrae', 'geography_id' => 1],
            ['id' => 8, 'code' => '58', 'name_th' => 'แม่ฮ่องสอน', 'name_en' => 'Mae Hong Son', 'geography_id' => 1],
            ['id' => 9, 'code' => '53', 'name_th' => 'อุตรดิตถ์', 'name_en' => 'Uttaradit', 'geography_id' => 1],
        ];

        // Central provinces
        $centralProvinces = [
            ['id' => 10, 'code' => '10', 'name_th' => 'กรุงเทพมหานคร', 'name_en' => 'Bangkok', 'geography_id' => 2],
            ['id' => 11, 'code' => '13', 'name_th' => 'ปทุมธานี', 'name_en' => 'Pathum Thani', 'geography_id' => 2],
            ['id' => 12, 'code' => '12', 'name_th' => 'นนทบุรี', 'name_en' => 'Nonthaburi', 'geography_id' => 2],
            ['id' => 13, 'code' => '11', 'name_th' => 'สมุทรปราการ', 'name_en' => 'Samut Prakan', 'geography_id' => 2],
            ['id' => 14, 'code' => '74', 'name_th' => 'สมุทรสาคร', 'name_en' => 'Samut Sakhon', 'geography_id' => 2],
            ['id' => 15, 'code' => '75', 'name_th' => 'สมุทรสงคราม', 'name_en' => 'Samut Songkhram', 'geography_id' => 2],
        ];

        // Northeastern provinces
        $northeasternProvinces = [
            ['id' => 16, 'code' => '46', 'name_th' => 'กาฬสินธุ์', 'name_en' => 'Kalasin', 'geography_id' => 3],
            ['id' => 17, 'code' => '40', 'name_th' => 'ขอนแก่น', 'name_en' => 'Khon Kaen', 'geography_id' => 3],
            ['id' => 18, 'code' => '41', 'name_th' => 'อุดรธานี', 'name_en' => 'Udon Thani', 'geography_id' => 3],
            ['id' => 19, 'code' => '42', 'name_th' => 'เลย', 'name_en' => 'Loei', 'geography_id' => 3],
            ['id' => 20, 'code' => '43', 'name_th' => 'หนองคาย', 'name_en' => 'Nong Khai', 'geography_id' => 3],
        ];

        // Eastern provinces
        $easternProvinces = [
            ['id' => 21, 'code' => '20', 'name_th' => 'ชลบุรี', 'name_en' => 'Chonburi', 'geography_id' => 4],
            ['id' => 22, 'code' => '21', 'name_th' => 'ระยอง', 'name_en' => 'Rayong', 'geography_id' => 4],
            ['id' => 23, 'code' => '22', 'name_th' => 'จันทบุรี', 'name_en' => 'Chanthaburi', 'geography_id' => 4],
            ['id' => 24, 'code' => '23', 'name_th' => 'ตราด', 'name_en' => 'Trat', 'geography_id' => 4],
        ];

        // Western provinces
        $westernProvinces = [
            ['id' => 25, 'code' => '71', 'name_th' => 'กาญจนบุรี', 'name_en' => 'Kanchanaburi', 'geography_id' => 5],
            ['id' => 26, 'code' => '70', 'name_th' => 'ราชบุรี', 'name_en' => 'Ratchaburi', 'geography_id' => 5],
            ['id' => 27, 'code' => '76', 'name_th' => 'เพชรบุรี', 'name_en' => 'Phetchaburi', 'geography_id' => 5],
            ['id' => 28, 'code' => '77', 'name_th' => 'ประจวบคีรีขันธ์', 'name_en' => 'Prachuap Khiri Khan', 'geography_id' => 5],
        ];

        // Southern provinces
        $southernProvinces = [
            ['id' => 29, 'code' => '80', 'name_th' => 'นครศรีธรรมราช', 'name_en' => 'Nakhon Si Thammarat', 'geography_id' => 6],
            ['id' => 30, 'code' => '81', 'name_th' => 'กระบี่', 'name_en' => 'Krabi', 'geography_id' => 6],
            ['id' => 31, 'code' => '82', 'name_th' => 'พังงา', 'name_en' => 'Phang Nga', 'geography_id' => 6],
            ['id' => 32, 'code' => '83', 'name_th' => 'ภูเก็ต', 'name_en' => 'Phuket', 'geography_id' => 6],
            ['id' => 33, 'code' => '84', 'name_th' => 'สุราษฎร์ธานี', 'name_en' => 'Surat Thani', 'geography_id' => 6],
        ];

        // Combine all provinces
        $provinces = array_merge(
            $northernProvinces,
            $centralProvinces,
            $northeasternProvinces,
            $easternProvinces,
            $westernProvinces,
            $southernProvinces
        );

            // Insert provinces
            foreach ($provinces as $province) {
                $province['created_at'] = now();
                $province['updated_at'] = now();
                DB::table('provinces')->insert($province);
            }
        }
    }

    /**
     * Seed amphures table
     */
    private function seedAmphures(): void
    {
        // Don't truncate, just check if data exists
        if (DB::table('amphures')->count() == 0) {

        // Chiang Mai amphures
        $chiangMaiAmphures = [
            ['id' => 1, 'code' => '5001', 'name_th' => 'เมืองเชียงใหม่', 'name_en' => 'Mueang Chiang Mai', 'province_id' => 1],
            ['id' => 2, 'code' => '5002', 'name_th' => 'จอมทอง', 'name_en' => 'Chom Thong', 'province_id' => 1],
            ['id' => 3, 'code' => '5003', 'name_th' => 'แม่แจ่ม', 'name_en' => 'Mae Chaem', 'province_id' => 1],
            ['id' => 4, 'code' => '5004', 'name_th' => 'เชียงดาว', 'name_en' => 'Chiang Dao', 'province_id' => 1],
            ['id' => 5, 'code' => '5005', 'name_th' => 'ดอยสะเก็ด', 'name_en' => 'Doi Saket', 'province_id' => 1],
        ];

        // Bangkok amphures
        $bangkokAmphures = [
            ['id' => 6, 'code' => '1001', 'name_th' => 'เขตพระนคร', 'name_en' => 'Phra Nakhon', 'province_id' => 10],
            ['id' => 7, 'code' => '1002', 'name_th' => 'เขตดุสิต', 'name_en' => 'Dusit', 'province_id' => 10],
            ['id' => 8, 'code' => '1003', 'name_th' => 'เขตหนองจอก', 'name_en' => 'Nong Chok', 'province_id' => 10],
            ['id' => 9, 'code' => '1004', 'name_th' => 'เขตบางรัก', 'name_en' => 'Bang Rak', 'province_id' => 10],
            ['id' => 10, 'code' => '1005', 'name_th' => 'เขตบางเขน', 'name_en' => 'Bang Khen', 'province_id' => 10],
        ];

        // Combine all amphures
        $amphures = array_merge(
            $chiangMaiAmphures,
            $bangkokAmphures
        );

        // Add more amphures for other provinces as needed

            // Insert amphures
            foreach ($amphures as $amphure) {
                $amphure['created_at'] = now();
                $amphure['updated_at'] = now();
                DB::table('amphures')->insert($amphure);
            }
        }
    }
}
