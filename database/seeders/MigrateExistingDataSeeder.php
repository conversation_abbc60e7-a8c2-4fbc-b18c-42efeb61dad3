<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Document;
use App\Models\DocumentType;
use App\Models\Material;
use App\Models\Language;
use App\Models\Script;

class MigrateExistingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting migration of existing data...');

        // 1. Migrate document types
        $this->migrateDocumentTypes();

        // 2. Migrate materials
        $this->migrateMaterials();

        // 3. Migrate languages
        $this->migrateLanguages();

        // 4. Migrate scripts
        $this->migrateScripts();

        // 5. Update document records with new foreign keys
        $this->updateDocumentRecords();

        $this->command->info('Data migration completed successfully!');
    }

    /**
     * Migrate document types from existing documents
     */
    private function migrateDocumentTypes(): void
    {
        $this->command->info('Migrating document types...');

        // Get unique document types from documents table
        $documentTypes = DB::table('documents')
            ->whereNotNull('document_type')
            ->where('document_type', '!=', '')
            ->select('document_type')
            ->distinct()
            ->pluck('document_type')
            ->toArray();

        $this->command->info('Found ' . count($documentTypes) . ' unique document types');

        // Make sure we have an "Other" type
        if (!DocumentType::where('name', 'Other')->exists()) {
            DocumentType::create([
                'name' => 'Other',
                'description' => 'Other document types not listed'
            ]);
            $this->command->info('Created "Other" document type');
        }

        // Create document types
        foreach ($documentTypes as $type) {
            if (!DocumentType::where('name', $type)->exists()) {
                DocumentType::create([
                    'name' => $type,
                    'description' => $type
                ]);
                $this->command->info('Created document type: ' . $type);
            } else {
                $this->command->info('Document type already exists: ' . $type);
            }
        }
    }

    /**
     * Migrate materials from existing documents
     */
    private function migrateMaterials(): void
    {
        $this->command->info('Migrating materials...');

        // Get unique materials from documents table
        $materials = DB::table('documents')
            ->whereNotNull('material')
            ->where('material', '!=', '')
            ->select('material')
            ->distinct()
            ->pluck('material')
            ->toArray();

        $this->command->info('Found ' . count($materials) . ' unique materials');

        // Make sure we have an "Other" material
        if (!Material::where('name', 'Other')->exists()) {
            Material::create([
                'name' => 'Other',
                'description' => 'Other materials not listed'
            ]);
            $this->command->info('Created "Other" material');
        }

        // Create materials
        foreach ($materials as $material) {
            if (!Material::where('name', $material)->exists()) {
                Material::create([
                    'name' => $material,
                    'description' => $material
                ]);
                $this->command->info('Created material: ' . $material);
            } else {
                $this->command->info('Material already exists: ' . $material);
            }
        }
    }

    /**
     * Migrate languages from existing documents
     */
    private function migrateLanguages(): void
    {
        $this->command->info('Migrating languages...');

        // Get unique languages from documents table
        $languages = DB::table('documents')
            ->whereNotNull('language')
            ->where('language', '!=', '')
            ->select('language')
            ->distinct()
            ->pluck('language')
            ->toArray();

        $this->command->info('Found ' . count($languages) . ' unique languages');

        // Make sure we have an "Other" language
        if (!Language::where('name', 'Other')->exists()) {
            Language::create([
                'name' => 'Other',
                'code' => '',
                'description' => 'Other languages not listed'
            ]);
            $this->command->info('Created "Other" language');
        }

        // Create languages
        foreach ($languages as $language) {
            if (!Language::where('name', $language)->exists()) {
                Language::create([
                    'name' => $language,
                    'code' => '',
                    'description' => $language
                ]);
                $this->command->info('Created language: ' . $language);
            } else {
                $this->command->info('Language already exists: ' . $language);
            }
        }
    }

    /**
     * Migrate scripts from existing documents
     */
    private function migrateScripts(): void
    {
        $this->command->info('Migrating scripts...');

        // Get unique scripts from documents table
        $scripts = DB::table('documents')
            ->whereNotNull('script')
            ->where('script', '!=', '')
            ->select('script')
            ->distinct()
            ->pluck('script')
            ->toArray();

        $this->command->info('Found ' . count($scripts) . ' unique scripts');

        // Make sure we have an "Other" script
        if (!Script::where('name', 'Other')->exists()) {
            Script::create([
                'name' => 'Other',
                'description' => 'Other scripts not listed'
            ]);
            $this->command->info('Created "Other" script');
        }

        // Create scripts
        foreach ($scripts as $script) {
            if (!Script::where('name', $script)->exists()) {
                Script::create([
                    'name' => $script,
                    'description' => $script
                ]);
                $this->command->info('Created script: ' . $script);
            } else {
                $this->command->info('Script already exists: ' . $script);
            }
        }
    }

    /**
     * Update document records with new foreign keys
     */
    private function updateDocumentRecords(): void
    {
        $this->command->info('Updating document records with new foreign keys...');

        // Get all documents
        $documents = Document::all();
        $count = 0;

        // Get the "Other" IDs for each category
        $otherDocumentTypeId = DocumentType::where('name', 'Other')->first()->id ?? null;
        $otherMaterialId = Material::where('name', 'Other')->first()->id ?? null;
        $otherLanguageId = Language::where('name', 'Other')->first()->id ?? null;
        $otherScriptId = Script::where('name', 'Other')->first()->id ?? null;

        foreach ($documents as $document) {
            $updated = false;

            // Update document_type_id
            if ($document->document_type && !$document->document_type_id) {
                $documentType = DocumentType::where('name', $document->document_type)->first();
                if ($documentType) {
                    $document->document_type_id = $documentType->id;
                    $updated = true;
                } elseif ($otherDocumentTypeId) {
                    $document->document_type_id = $otherDocumentTypeId;
                    $updated = true;
                }
            } elseif (!$document->document_type_id && $otherDocumentTypeId) {
                $document->document_type_id = $otherDocumentTypeId;
                $updated = true;
            }

            // Update material_id
            if ($document->material && !$document->material_id) {
                $material = Material::where('name', $document->material)->first();
                if ($material) {
                    $document->material_id = $material->id;
                    $updated = true;
                } elseif ($otherMaterialId) {
                    $document->material_id = $otherMaterialId;
                    $updated = true;
                }
            } elseif (!$document->material_id && $otherMaterialId) {
                $document->material_id = $otherMaterialId;
                $updated = true;
            }

            // Update language_id
            if ($document->language && !$document->language_id) {
                $language = Language::where('name', $document->language)->first();
                if ($language) {
                    $document->language_id = $language->id;
                    $updated = true;
                } elseif ($otherLanguageId) {
                    $document->language_id = $otherLanguageId;
                    $updated = true;
                }
            } elseif (!$document->language_id && $otherLanguageId) {
                $document->language_id = $otherLanguageId;
                $updated = true;
            }

            // Update script_id
            if ($document->script && !$document->script_id) {
                $script = Script::where('name', $document->script)->first();
                if ($script) {
                    $document->script_id = $script->id;
                    $updated = true;
                } elseif ($otherScriptId) {
                    $document->script_id = $otherScriptId;
                    $updated = true;
                }
            } elseif (!$document->script_id && $otherScriptId) {
                $document->script_id = $otherScriptId;
                $updated = true;
            }

            // Save the document if updated
            if ($updated) {
                $document->save();
                $count++;
            }
        }

        $this->command->info('Updated ' . $count . ' document records');
    }
}
