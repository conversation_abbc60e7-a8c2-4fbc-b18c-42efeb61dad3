<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Document;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateManuscriptCoordinatesSeeder extends Seeder
{
    /**
     * Parse columns from a SQL row
     */
    private function parseColumns($row)
    {
        $columns = [];
        $inQuote = false;
        $currentColumn = '';

        for ($i = 0; $i < strlen($row); $i++) {
            $char = $row[$i];

            if ($char === "'" && ($i === 0 || $row[$i-1] !== '\\')) {
                $inQuote = !$inQuote;
                $currentColumn .= $char;
            } elseif ($char === ',' && !$inQuote) {
                $columns[] = trim($currentColumn);
                $currentColumn = '';
            } else {
                $currentColumn .= $char;
            }
        }

        // Add the last column
        if (!empty($currentColumn)) {
            $columns[] = trim($currentColumn);
        }

        return $columns;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting to update document coordinates from manuscript.sql...');

        // อ่านไฟล์ manuscript.sql
        $sqlFilePath = $_SERVER['HOME'] . '/Downloads/manuscript.sql';

        if (!file_exists($sqlFilePath)) {
            $this->command->error('manuscript.sql file not found at: ' . $sqlFilePath);
            return;
        }

        $sqlContent = file_get_contents($sqlFilePath);

        // แยกข้อมูลจากไฟล์ SQL โดยหาบรรทัดที่มีการ INSERT
        preg_match_all("/INSERT INTO `manuscript`.*?VALUES\s*\((.*?)\);/s", $sqlContent, $insertMatches);

        $updatedCount = 0;
        $notFoundCount = 0;
        $noCoordinatesCount = 0;

        foreach ($insertMatches[1] as $insertData) {
            // แยกข้อมูลแต่ละแถว
            $rows = explode("),\n(", $insertData);

            foreach ($rows as $row) {
                // แยกข้อมูลในแต่ละคอลัมน์
                $columns = $this->parseColumns($row);

                if (count($columns) < 30) {
                    continue; // ข้ามถ้าข้อมูลไม่ครบ
                }

                $manuscriptId = $columns[0];
                $identifierNo = $columns[1];
                $latitude = $columns[27];
                $longitude = $columns[28];

                // ตรวจสอบว่ามีค่าพิกัดหรือไม่
                if (empty($latitude) || $latitude === 'NULL' || empty($longitude) || $longitude === 'NULL') {
                    $noCoordinatesCount++;
                    continue;
                }

                    // ลบเครื่องหมาย quote ออกจากค่า
                $identifierNo = trim($identifierNo, "'");
                $latitude = trim($latitude, "'");
                $longitude = trim($longitude, "'");

                // ค้นหาเอกสารด้วย identifier_no
                $document = Document::where('identifier_no', $identifierNo)->first();

                if ($document) {
                    // อัปเดตพิกัด
                    $document->update([
                        'latitude' => $latitude,
                        'longitude' => $longitude
                    ]);

                    $updatedCount++;
                    $this->command->info("Updated coordinates for document with identifier_no: $identifierNo (Lat: $latitude, Long: $longitude)");
                } else {
                    $notFoundCount++;
                    $this->command->warn("Document with identifier_no: $identifierNo not found");
                }
            }
        }

        $this->command->info("Coordinates update completed:");
        $this->command->info("- Updated: $updatedCount documents");
        $this->command->info("- Not found: $notFoundCount documents");
        $this->command->info("- No coordinates: $noCoordinatesCount documents");
    }
}
