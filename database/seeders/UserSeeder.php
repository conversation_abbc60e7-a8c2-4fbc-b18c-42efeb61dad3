<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // สร้างผู้ใช้แอดมิน
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'is_active' => true,
            ]);
        } else {
            // Update existing admin user to have admin role
            $admin->role = 'admin';
            $admin->is_active = true;
            $admin->save();
        }

        // สร้างผู้ใช้ editor
        $editor = User::where('email', '<EMAIL>')->first();

        if (!$editor) {
            User::create([
                'name' => 'Editor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'editor',
                'is_active' => true,
            ]);
        }

        // สร้างผู้ใช้ viewer
        $viewer = User::where('email', '<EMAIL>')->first();

        if (!$viewer) {
            User::create([
                'name' => 'Viewer',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'viewer',
                'is_active' => true,
            ]);
        }
    }
}
