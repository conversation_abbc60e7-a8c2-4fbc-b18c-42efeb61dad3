<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Countries data
        $countries = [
            ['id' => 1, 'name_th' => 'ไทย', 'name_en' => 'Thailand', 'code' => 'TH'],
            ['id' => 2, 'name_th' => 'ลาว', 'name_en' => 'Laos', 'code' => 'LA'],
            ['id' => 3, 'name_th' => 'พม่า', 'name_en' => 'Myanmar', 'code' => 'MM'],
            ['id' => 4, 'name_th' => 'กัมพูชา', 'name_en' => 'Cambodia', 'code' => 'KH'],
            ['id' => 5, 'name_th' => 'เวียดนาม', 'name_en' => 'Vietnam', 'code' => 'VN'],
            ['id' => 6, 'name_th' => 'มาเลเซีย', 'name_en' => 'Malaysia', 'code' => 'MY'],
            ['id' => 7, 'name_th' => 'สิงคโปร์', 'name_en' => 'Singapore', 'code' => 'SG'],
            ['id' => 8, 'name_th' => 'อินโดนีเซีย', 'name_en' => 'Indonesia', 'code' => 'ID'],
            ['id' => 9, 'name_th' => 'ฟิลิปปินส์', 'name_en' => 'Philippines', 'code' => 'PH'],
            ['id' => 10, 'name_th' => 'บรูไน', 'name_en' => 'Brunei', 'code' => 'BN'],
            ['id' => 11, 'name_th' => 'จีน', 'name_en' => 'China', 'code' => 'CN'],
            ['id' => 12, 'name_th' => 'ญี่ปุ่น', 'name_en' => 'Japan', 'code' => 'JP'],
            ['id' => 13, 'name_th' => 'เกาหลีใต้', 'name_en' => 'South Korea', 'code' => 'KR'],
            ['id' => 14, 'name_th' => 'อินเดีย', 'name_en' => 'India', 'code' => 'IN'],
            ['id' => 15, 'name_th' => 'สหรัฐอเมริกา', 'name_en' => 'United States', 'code' => 'US'],
            ['id' => 16, 'name_th' => 'สหราชอาณาจักร', 'name_en' => 'United Kingdom', 'code' => 'GB'],
        ];

        // Insert or update countries
        foreach ($countries as $country) {
            DB::table('countries')->updateOrInsert(
                ['id' => $country['id']],
                $country
            );
        }
    }
}
