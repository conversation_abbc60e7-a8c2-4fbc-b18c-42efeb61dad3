<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategoryCodesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'พระไตรปิฎก - พระวินัยปิฎก', 'code' => 'A-1'],
            ['name' => 'พระไตรปิฎก - พระสุดตันตปีฎก', 'code' => 'A-2'],
            ['name' => 'พระไตรปิฎก - พระอภิธรรมปีฎก', 'code' => 'A-3'],
            ['name' => 'พระไตรปิฎกแบบย่อ', 'code' => 'A-4'],
            ['name' => 'อรรภกถา ฎีกา และปกรณ์วิเสสต่างๆ', 'code' => 'A-5'],
            ['name' => 'ชาดก', 'code' => 'A-6'],
            ['name' => 'ธรรมทั่วไป', 'code' => 'A-7'],
            ['name' => 'ตำนาน-พุทธตำนาน', 'code' => 'C-1'],
            ['name' => 'ตำนาน-พระสาวก', 'code' => 'C-2'],
            ['name' => 'ตำนานปูชนียสถาน - ปูชนียวัตถุ', 'code' => 'C-3'],
            ['name' => 'ตำนาน-บุคคลสำคัญ', 'code' => 'C-4'],
            ['name' => 'นิทานพื้นบ้าน', 'code' => 'B'],
            ['name' => 'การพยากรณ์เหตุการณ์อนาคต', 'code' => 'D'],
            ['name' => 'โหราศาสตร์', 'code' => 'H'],
            ['name' => 'จักรวาลวิทยา', 'code' => 'E'],
            ['name' => 'ไสยศาสตร์', 'code' => 'F'],
            ['name' => 'จริยศาสตร์', 'code' => 'G'],
            ['name' => 'อานิสงส์', 'code' => 'O'],
            ['name' => 'ภาษาศาสตร์', 'code' => 'L'],
            ['name' => 'กฎหมาย', 'code' => 'K'],
            ['name' => 'เวชศาสตร์', 'code' => 'M'],
            ['name' => 'กวีนิพนธ์', 'code' => 'P'],
            ['name' => 'บทสวดมนต์', 'code' => 'Q'],
            ['name' => 'ประวัติศาสตร์', 'code' => 'R'],
        ];

        foreach ($categories as $category) {
            $existingCategory = Category::where('name', $category['name'])->first();
            
            if ($existingCategory) {
                // Update existing category with code
                $existingCategory->code = $category['code'];
                $existingCategory->save();
                $this->command->info("Updated category: {$category['name']} with code: {$category['code']}");
            } else {
                // Create new category
                Category::create($category);
                $this->command->info("Created category: {$category['name']} with code: {$category['code']}");
            }
        }
    }
}
