<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign admin role if using spatie/laravel-permission
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminUser->assignRole($adminRole);
        }

        $this->command->info('Admin user created: <EMAIL> / password');

        // Create editor user
        $editorUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Editor User',
                'password' => Hash::make('password'),
                'role' => 'editor',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign editor role if using spatie/laravel-permission
        $editorRole = Role::where('name', 'editor')->first();
        if ($editorRole) {
            $editorUser->assignRole($editorRole);
        }

        $this->command->info('Editor user created: <EMAIL> / password');

        // Create viewer user
        $viewerUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Viewer User',
                'password' => Hash::make('password'),
                'role' => 'viewer',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Assign viewer role if using spatie/laravel-permission
        $viewerRole = Role::where('name', 'viewer')->first();
        if ($viewerRole) {
            $viewerUser->assignRole($viewerRole);
        }

        $this->command->info('Viewer user created: <EMAIL> / password');
    }
}
