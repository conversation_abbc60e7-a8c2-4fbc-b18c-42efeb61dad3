<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Material;

class MaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $materials = [
            [
                'id' => 1,
                'name' => 'ใบลาน',

            ],
            [
                'id' => 2,
                'name' => 'กระดาษสา',

            ],
            [
                'id' => 3,
                'name' => 'หิน',

            ],
            [
                'id' => 4,
                'name' => 'ผ้า',

            ],
            [
                'id' => 5,
                'name' => 'โลหะ',

            ],
        ];

        foreach ($materials as $material) {
            Material::create($material);
        }
    }
}
