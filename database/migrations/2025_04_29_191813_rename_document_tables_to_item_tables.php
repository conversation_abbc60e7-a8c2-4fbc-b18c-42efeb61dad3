<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip renaming documents table to items since it already exists
        // Schema::rename('documents', 'items');

        // Skip renaming tables since they already exist
        // Schema::rename('document_files', 'item_files');
        // Schema::rename('document_images', 'item_images');
        // Schema::rename('document_types', 'item_types');

        // Skip renaming foreign key columns in item_files table
        // Already done in previous migrations

        // Skip renaming foreign key columns in item_images table
        // Already done in previous migrations

        // Skip renaming document_type_id to item_type_id in items table
        // Already done in previous migrations
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Skip renaming item_type_id back to document_type_id in items table
        // This is a no-op migration now

        // Skip renaming foreign key columns in item_images table back to document_id
        // This is a no-op migration now

        // <PERSON>p renaming foreign key columns in item_files table back to document_id
        // This is a no-op migration now

        // Skip renaming tables back to original names
        // This is a no-op migration now
    }

    /**
     * Get a list of foreign keys for a table
     */
    private function listTableForeignKeys($table)
    {
        $foreignKeys = [];

        // Get foreign keys from information_schema
        $results = \DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS
            WHERE CONSTRAINT_TYPE = 'FOREIGN KEY'
            AND TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = ?
        ", [$table]);

        foreach ($results as $result) {
            $foreignKeys[] = $result->CONSTRAINT_NAME;
        }

        return $foreignKeys;
    }
};
