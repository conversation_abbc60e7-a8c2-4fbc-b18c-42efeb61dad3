<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('provinces') && !Schema::hasColumn('provinces', 'geography_id')) {
            Schema::table('provinces', function (Blueprint $table) {
                // Check if country_code column exists
                if (Schema::hasColumn('provinces', 'country_code')) {
                    $table->unsignedBigInteger('geography_id')->nullable()->after('country_code');
                } else {
                    $table->unsignedBigInteger('geography_id')->nullable();
                }

                $table->foreign('geography_id')->references('id')->on('geographies')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('provinces', function (Blueprint $table) {
            //
        });
    }
};
