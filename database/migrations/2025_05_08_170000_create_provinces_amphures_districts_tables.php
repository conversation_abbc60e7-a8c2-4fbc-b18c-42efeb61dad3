<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create geographies table
        if (!Schema::hasTable('geographies')) {
            Schema::create('geographies', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->timestamps();
            });
        }

        // Create provinces table
        if (!Schema::hasTable('provinces')) {
            Schema::create('provinces', function (Blueprint $table) {
                $table->id();
                $table->string('name_th');
                $table->string('name_en');
                $table->foreignId('geography_id')->nullable()->constrained('geographies')->nullOnDelete();
                $table->string('code', 10)->nullable();
                $table->timestamps();
            });
        }

        // Create amphures table
        if (!Schema::hasTable('amphures')) {
            Schema::create('amphures', function (Blueprint $table) {
                $table->id();
                $table->string('name_th');
                $table->string('name_en');
                $table->foreignId('province_id')->constrained('provinces')->cascadeOnDelete();
                $table->string('code', 10)->nullable();
                $table->timestamps();
            });
        }

        // Create districts table
        if (!Schema::hasTable('districts')) {
            Schema::create('districts', function (Blueprint $table) {
                $table->id();
                $table->string('name_th');
                $table->string('name_en');
                $table->foreignId('amphure_id')->constrained('amphures')->cascadeOnDelete();
                $table->string('code', 10)->nullable();
                $table->string('zip_code', 10)->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('districts');
        Schema::dropIfExists('amphures');
        Schema::dropIfExists('provinces');
        Schema::dropIfExists('geographies');
    }
};
