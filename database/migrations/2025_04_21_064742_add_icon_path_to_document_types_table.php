<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_types', function (Blueprint $table) {
            if (!Schema::hasColumn('item_types', 'icon_class')) {
                $table->string('icon_class')->nullable()->after('description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_types', function (Blueprint $table) {
            if (Schema::hasColumn('item_types', 'icon_class')) {
                $table->dropColumn('icon_class');
            }
        });
    }
};
