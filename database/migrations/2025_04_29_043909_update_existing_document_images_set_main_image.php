<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get all items
        if (Schema::hasTable('items')) {
            $items = DB::table('items')->get();

            foreach ($items as $item) {
                // Skip items without image_path
                if (empty($item->image_path)) {
                    continue;
                }

                // Find the image record that matches the item's image_path
                if (Schema::hasTable('item_images')) {
                    $image = DB::table('item_images')
                        ->where('item_id', $item->id)
                        ->where('image_path', $item->image_path)
                        ->first();

                    if ($image) {
                        // Set this image as main
                        DB::table('item_images')
                            ->where('id', $image->id)
                            ->update(['is_main' => true]);
                    } else {
                        // If no exact match, try to find the first image for this item
                        $firstImage = DB::table('item_images')
                            ->where('item_id', $item->id)
                            ->orderBy('sort_order')
                            ->first();

                        if ($firstImage) {
                            // Set the first image as main
                            DB::table('item_images')
                                ->where('id', $firstImage->id)
                                ->update(['is_main' => true]);

                            // Update item's image_path to match
                            DB::table('items')
                                ->where('id', $item->id)
                                ->update(['image_path' => $firstImage->image_path]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to do anything in down migration
        // This is just a data update
    }
};
