<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_files', function (Blueprint $table) {
            if (!Schema::hasColumn('item_files', 'is_main')) {
                $table->boolean('is_main')->default(false)->after('file_extension');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_files', function (Blueprint $table) {
            if (Schema::hasColumn('item_files', 'is_main')) {
                $table->dropColumn('is_main');
            }
        });
    }
};
