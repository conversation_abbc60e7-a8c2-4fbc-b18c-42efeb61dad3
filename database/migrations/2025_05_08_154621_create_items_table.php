<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('items')) {
            Schema::create('items', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('description')->nullable();
                $table->string('identifier_no')->nullable();
                $table->string('year')->nullable();
                $table->foreignId('item_type_id')->nullable()->constrained('item_types')->nullOnDelete();
                $table->foreignId('category_id')->nullable()->constrained('categories')->nullOnDelete();
                $table->foreignId('material_id')->nullable()->constrained('materials')->nullOnDelete();
                $table->foreignId('language_id')->nullable()->constrained('languages')->nullOnDelete();
                $table->foreignId('script_id')->nullable()->constrained('scripts')->nullOnDelete();
                $table->string('creator')->nullable();
                $table->string('author')->nullable();
                $table->string('location')->nullable();
                $table->string('country', 10)->nullable();
                $table->string('province', 10)->nullable();
                $table->decimal('latitude', 10, 8)->nullable();
                $table->decimal('longitude', 11, 8)->nullable();
                $table->text('remark')->nullable();
                $table->string('manuscript_condition')->nullable();
                $table->string('image_path')->nullable();
                $table->string('file_path')->nullable();
                $table->string('file_type')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('items');
    }
};
