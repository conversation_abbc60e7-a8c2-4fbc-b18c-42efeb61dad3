<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('lanna_words');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('lanna_words', function (Blueprint $table) {
            $table->id();
            $table->string('lanna_word');
            $table->string('thai_word');
            $table->string('phonetic_lanna')->nullable();
            $table->string('ipa_pronunciation')->nullable();
            $table->string('part_of_speech')->nullable();
            $table->text('definition')->nullable();
            $table->timestamps();
        });
    }
};
