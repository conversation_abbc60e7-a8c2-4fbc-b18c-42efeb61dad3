<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_images', function (Blueprint $table) {
            if (!Schema::hasColumn('item_images', 'is_main')) {
                $table->boolean('is_main')->default(false)->after('sort_order');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_images', function (Blueprint $table) {
            if (Schema::hasColumn('item_images', 'is_main')) {
                $table->dropColumn('is_main');
            }
        });
    }
};
