# Installing in a Subfolder

This document provides instructions for installing the application in a subfolder instead of the root directory of a domain.

## Configuration Steps

1. **Set the correct APP_URL in .env file**:
   ```
   APP_URL=http://example.com/subfolder
   ```
   Replace `example.com/subfolder` with your actual domain and subfolder path.

2. **Ensure the .htaccess file exists in the root directory**:
   The application includes a root .htaccess file that redirects requests to the public directory.

3. **Run the storage link command**:
   ```
   php artisan storage:link
   ```
   This creates a symbolic link from `public/storage` to `storage/app/public`.

4. **Clear the application cache**:
   ```
   php artisan config:clear
   php artisan cache:clear
   php artisan route:clear
   php artisan view:clear
   ```

5. **Rebuild assets if using Vite**:
   ```
   npm run build
   ```

## Troubleshooting

### Images or Files Not Loading

If images or files are not loading correctly:

1. Check that the storage link is correctly set up
2. Verify that the APP_URL in .env includes the subfolder path
3. Make sure file permissions are correct
4. Check the browser console for any 404 errors and verify the paths

### URL Generation Issues

If links are not working correctly:

1. Make sure you're using <PERSON><PERSON>'s URL generation functions (`url()`, `route()`, `asset()`) rather than hardcoded paths
2. Clear the application cache after making changes to the .env file

### API Endpoints

If you're using API endpoints, make sure they include the subfolder path in the base URL.

## Additional Notes

- The application has been updated to automatically detect when it's running in a subfolder and adjust URLs accordingly.
- All file upload and storage functions have been modified to work correctly in subfolder installations.
- The Vite configuration has been updated to support subfolder installations for asset compilation.
