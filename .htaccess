<IfModule mod_rewrite.c>
    RewriteEngine On

    # If the request is for an actual file or directory, serve it directly
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]

    # Handle subfolder installation
    # This will be used when the application is installed in a subfolder
    # The subfolder name is configured in the .env file with APP_SUBFOLDER

    # Otherwise, redirect to the public directory
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>
