<?php

/**
 * Laravel - A PHP Framework For Web Artisans
 *
 * This file includes the Laravel public/index.php file
 * to allow direct access to the application without
 * going through the public directory.
 */

// Define the path to the Laravel public directory
$publicPath = __DIR__ . '/public';

// Store the original request URI
$originalRequestUri = $_SERVER['REQUEST_URI'];

// Load .env file to get APP_SUBFOLDER
if (file_exists(__DIR__ . '/.env')) {
    $envFile = file_get_contents(__DIR__ . '/.env');
    preg_match('/APP_SUBFOLDER=(.*)/', $envFile, $matches);
    $configSubfolder = isset($matches[1]) ? trim($matches[1]) : 'dc';
} else {
    $configSubfolder = 'dc';
}

// If the request is for the Laravel root directory, adjust the URI
$scriptName = '/' . $configSubfolder;
if (strpos($originalRequestUri, $scriptName) === 0) {
    // Remove the script name from the request URI
    $requestUri = substr($originalRequestUri, strlen($scriptName));
    // If empty, set to /
    if (empty($requestUri)) {
        $requestUri = '/';
    }
    // Set the new request URI
    $_SERVER['REQUEST_URI'] = $requestUri;
}

// Set the script name and filename
$_SERVER['SCRIPT_NAME'] = '/index.php';
$_SERVER['PHP_SELF'] = '/index.php';
$_SERVER['SCRIPT_FILENAME'] = $publicPath . '/index.php';

// Change directory to the Laravel public directory
chdir($publicPath);

// Include the Laravel public index.php
require $publicPath . '/index.php';
