import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import fs from 'fs';
import path from 'path';

// Detect if we're in a subfolder installation
const detectBasePath = () => {
    try {
        // Try to read .env file to get APP_SUBFOLDER first
        const envPath = path.resolve('.env');
        if (fs.existsSync(envPath)) {
            const envContent = fs.readFileSync(envPath, 'utf8');

            // First check for APP_SUBFOLDER
            const subfolderMatch = envContent.match(/APP_SUBFOLDER=(.+)/);
            if (subfolderMatch && subfolderMatch[1]) {
                const subfolder = subfolderMatch[1].trim();
                if (subfolder) {
                    return '/' + subfolder + '/';
                }
            }

            // Fallback to APP_URL
            const appUrlMatch = envContent.match(/APP_URL=(.+)/);
            if (appUrlMatch && appUrlMatch[1]) {
                const url = new URL(appUrlMatch[1]);
                const pathname = url.pathname.replace(/^\/|\/$/g, '');
                if (pathname) {
                    return '/' + pathname + '/';
                }
            }
        }
    } catch (error) {
        console.error('Error detecting base path:', error);
    }
    return '/';
};

const base = process.env.ASSET_URL || detectBasePath();

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'node_modules/dropzone/dist/dropzone.css',
                'node_modules/dropzone/dist/dropzone.js'
            ],
            refresh: true,
        }),
    ],
    base,
    build: {
        rollupOptions: {
            external: [
                '@uppy/locales/dist/th_TH',
                '@uppy/locales/lib/th_TH'
            ]
        }
    }
});
