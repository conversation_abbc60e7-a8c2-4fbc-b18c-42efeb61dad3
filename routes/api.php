<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ApiItemController;
use App\Http\Controllers\Api\LocationApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API routes for items
Route::get('/items', [ApiItemController::class, 'index']);
Route::get('/items/{id}', [ApiItemController::class, 'show']);

// Manuscript API endpoint (for compatibility with the reference URL)
Route::get('/manuscript-api.php', [ApiItemController::class, 'index']);

// Location API routes
Route::get('/locations/countries', [LocationApiController::class, 'getCountries']);
Route::get('/locations/provinces/{countryCode}', [LocationApiController::class, 'getProvincesByCountry']);
