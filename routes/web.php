<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\SearchController;
use App\Models\Setting;
use App\Helpers\MaintenanceHelper;

use App\Http\Controllers\MapController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\Auth\LoginController;
// Admin controllers removed

// เส้นทางทั่วไปที่ต้องตรวจสอบโหมดบำรุงรักษา
Route::get('/', function () {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new HomeController();
    return $controller->index();
})->name('home');

// Test route
Route::get('/test', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    return 'Test route is working!';
});

// Test Livewire route
Route::get('/test-livewire', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    return view('test-livewire');
});

// Location routes
Route::get('/locations/countries', [LocationController::class, 'getCountries']);
Route::get('/locations/provinces/{countryCode}', [LocationController::class, 'getProvincesByCountry']);
Route::get('/dc/locations/countries', [LocationController::class, 'getCountries']); // สำหรับการติดตั้งในโฟลเดอร์ย่อย
Route::get('/dc/locations/provinces/{countryCode}', [LocationController::class, 'getProvincesByCountry']); // สำหรับการติดตั้งในโฟลเดอร์ย่อย

// Item routes - order is important!

// Item index routes
Route::get('/items', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new ItemController();
    return $controller->index(request());
})->name('items.index');

// Item files route (specific route first)
Route::get('/items/files', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $files = File::files(public_path('documents-all'));
    $fileList = [];

    foreach ($files as $file) {
        $fileList[] = [
            'name' => $file->getFilename(),
            'path' => '/documents-all/' . $file->getFilename(),
            'size' => $file->getSize(),
            'type' => pathinfo($file->getFilename(), PATHINFO_EXTENSION),
            'last_modified' => date('Y-m-d H:i:s', $file->getMTime())
        ];
    }

    return view('items.files', ['files' => $fileList]);
})->name('items.files');

// Item detail route (with parameter - must be last)
Route::get('/items/{id}', function($id) {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new ItemController();
    return $controller->show($id);
})->name('items.show');



// Category routes
Route::get('/categories', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new CategoryController();
    return $controller->index();
})->name('categories.index');

Route::get('/categories/{id}', function($id) {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new CategoryController();
    return $controller->show($id);
})->name('categories.show');

// Search routes
Route::get('/search', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new SearchController();
    return $controller->index(request());
})->name('search.index');

// Statistics routes
Route::get('/statistics', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new \App\Http\Controllers\StatisticsController();
    return $controller->index();
})->name('statistics.index');

// Map routes
Route::get('/maps', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    $controller = new MapController();
    return $controller->index();
})->name('maps.index');

// API Documentation route
Route::get('/api-docs', function() {
    // ตรวจสอบโหมดบำรุงรักษา
    $maintenanceResponse = MaintenanceHelper::checkMaintenance(request()->path());
    if ($maintenanceResponse) {
        return $maintenanceResponse;
    }

    return view('api.documentation');
})->name('api.documentation');

// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Debug log route
Route::post('/admin/debug-log', function (Illuminate\Http\Request $request) {
    $data = $request->all();
    \Illuminate\Support\Facades\Log::info('Debug log from frontend', $data);
    file_put_contents(
        storage_path('logs/frontend_debug.log'),
        date('Y-m-d H:i:s') . " - Debug log from frontend\n" .
        json_encode($data, JSON_PRETTY_PRINT) . "\n\n",
        FILE_APPEND
    );
    return response()->json(['success' => true]);
})->middleware(['auth', 'admin']);


// Admin routes
Route::middleware(['auth'])->prefix('admin')->group(function () {
    // Dashboard - accessible to anyone who can access admin area
    Route::get('/', [App\Http\Controllers\AdminController::class, 'dashboard'])->name('admin.dashboard');

    // Items
    Route::middleware('permission:view items')->group(function () {
        Route::get('/items', [App\Http\Controllers\AdminController::class, 'items'])->name('admin.items');
        Route::get('/items/{id}/edit', [App\Http\Controllers\AdminController::class, 'editItem'])->name('admin.items.edit');


    });

    Route::middleware('permission:create items')->group(function () {
        Route::get('/items/create', [App\Http\Controllers\AdminController::class, 'createItem'])->name('admin.items.create');
        Route::post('/items', [App\Http\Controllers\AdminController::class, 'storeItem'])->name('admin.items.store');


    });

    Route::middleware('permission:edit items')->group(function () {
        Route::put('/items/{id}', [App\Http\Controllers\AdminController::class, 'updateItem'])->name('admin.items.update');
        Route::post('/items/{id}/upload-files', [App\Http\Controllers\AdminController::class, 'uploadFiles'])->name('admin.items.upload-files');
        Route::post('/items/upload-file/new', [App\Http\Controllers\AdminController::class, 'uploadFile'])->name('admin.items.upload-file-new');
        Route::post('/items/{id}/upload-file', [App\Http\Controllers\AdminController::class, 'uploadFile'])->name('admin.items.upload-file');
        Route::post('/items/delete-file', [App\Http\Controllers\AdminController::class, 'deleteFile'])->name('admin.items.delete-file');
        Route::post('/items/delete-image', [App\Http\Controllers\AdminController::class, 'deleteImage'])->name('admin.items.delete-image');
        Route::get('/items/uppy-iframe', [App\Http\Controllers\AdminController::class, 'uppyIframe'])->name('admin.items.uppy-iframe');
        Route::get('/items/uppy-unified-iframe', [App\Http\Controllers\AdminController::class, 'uppyUnifiedIframe'])->name('admin.items.uppy-unified-iframe');
        Route::post('/items/set-main-image', [App\Http\Controllers\AdminController::class, 'setMainImage'])->name('admin.items.set-main-image');
        Route::post('/items/set-main-file', [App\Http\Controllers\AdminController::class, 'setMainFile'])->name('admin.items.set-main-file');
        Route::get('/items/check-identifier', [App\Http\Controllers\AdminController::class, 'checkIdentifier'])->name('admin.items.check-identifier');
        Route::post('/items/temp-cleanup', [App\Http\Controllers\AdminController::class, 'tempCleanup'])->name('admin.items.temp-cleanup');


    });

    Route::middleware('permission:delete items')->group(function () {
        Route::delete('/items/{id}', [App\Http\Controllers\AdminController::class, 'destroyItem'])->name('admin.items.destroy');


    });

    // Categories
    Route::middleware('permission:view categories')->group(function () {
        Route::get('/categories', [App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('admin.categories.index');
    });

    Route::middleware('permission:create categories')->group(function () {
        Route::get('/categories/create', [App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('admin.categories.create');
        Route::post('/categories', [App\Http\Controllers\Admin\CategoryController::class, 'store'])->name('admin.categories.store');
    });

    Route::middleware('permission:edit categories')->group(function () {
        Route::get('/categories/{category}/edit', [App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('admin.categories.edit');
        Route::put('/categories/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('admin.categories.update');
        Route::delete('/categories/{category}/delete-image', [App\Http\Controllers\Admin\CategoryController::class, 'deleteImage']);
    });

    Route::middleware('permission:delete categories')->group(function () {
        Route::delete('/categories/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('admin.categories.destroy');
    });



    // Item Types
    Route::middleware('permission:view types')->group(function () {
        // Use a different path for the index route to avoid conflicts
        Route::get('/item-types/all', [App\Http\Controllers\Admin\ItemTypeController::class, 'index'])
            ->name('admin.item-types.index');

        // Original path for backward compatibility
        Route::get('/item-types', [App\Http\Controllers\AdminController::class, 'itemTypes'])
            ->name('admin.item-types');


    });

    Route::middleware('permission:create types')->group(function () {
        Route::get('/item-types/create', [App\Http\Controllers\Admin\ItemTypeController::class, 'create'])->name('admin.item-types.create');
        Route::post('/item-types', [App\Http\Controllers\Admin\ItemTypeController::class, 'store'])->name('admin.item-types.store');
    });

    Route::middleware('permission:edit types')->group(function () {
        Route::get('/item-types/{itemType}/edit', [App\Http\Controllers\Admin\ItemTypeController::class, 'edit'])->name('admin.item-types.edit');
        Route::put('/item-types/{itemType}', [App\Http\Controllers\Admin\ItemTypeController::class, 'update'])->name('admin.item-types.update');
    });

    Route::middleware('permission:delete types')->group(function () {
        Route::delete('/item-types/{itemType}', [App\Http\Controllers\Admin\ItemTypeController::class, 'destroy'])->name('admin.item-types.destroy');
    });

    // Languages
    Route::middleware('permission:view languages')->group(function () {
        Route::get('/languages', [App\Http\Controllers\AdminController::class, 'languages'])->name('admin.languages');
    });

    Route::middleware('permission:create languages')->group(function () {
        Route::get('/languages/create', [App\Http\Controllers\Admin\LanguageController::class, 'create'])->name('admin.languages.create');
        Route::post('/languages', [App\Http\Controllers\Admin\LanguageController::class, 'store'])->name('admin.languages.store');
    });

    Route::middleware('permission:edit languages')->group(function () {
        Route::get('/languages/{language}/edit', [App\Http\Controllers\Admin\LanguageController::class, 'edit'])->name('admin.languages.edit');
        Route::put('/languages/{language}', [App\Http\Controllers\Admin\LanguageController::class, 'update'])->name('admin.languages.update');
    });

    Route::middleware('permission:delete languages')->group(function () {
        Route::delete('/languages/{language}', [App\Http\Controllers\Admin\LanguageController::class, 'destroy'])->name('admin.languages.destroy');
    });

    // Scripts
    Route::middleware('permission:view scripts')->group(function () {
        Route::get('/scripts', [App\Http\Controllers\AdminController::class, 'scripts'])->name('admin.scripts');
    });

    Route::middleware('permission:create scripts')->group(function () {
        Route::get('/scripts/create', [App\Http\Controllers\Admin\ScriptController::class, 'create'])->name('admin.scripts.create');
        Route::post('/scripts', [App\Http\Controllers\Admin\ScriptController::class, 'store'])->name('admin.scripts.store');
    });

    Route::middleware('permission:edit scripts')->group(function () {
        Route::get('/scripts/{script}/edit', [App\Http\Controllers\Admin\ScriptController::class, 'edit'])->name('admin.scripts.edit');
        Route::put('/scripts/{script}', [App\Http\Controllers\Admin\ScriptController::class, 'update'])->name('admin.scripts.update');
    });

    Route::middleware('permission:delete scripts')->group(function () {
        Route::delete('/scripts/{script}', [App\Http\Controllers\Admin\ScriptController::class, 'destroy'])->name('admin.scripts.destroy');
    });

    // Materials
    Route::middleware('permission:view materials')->group(function () {
        Route::get('/materials', [App\Http\Controllers\AdminController::class, 'materials'])->name('admin.materials');
    });

    Route::middleware('permission:create materials')->group(function () {
        Route::get('/materials/create', [App\Http\Controllers\Admin\MaterialController::class, 'create'])->name('admin.materials.create');
        Route::post('/materials', [App\Http\Controllers\Admin\MaterialController::class, 'store'])->name('admin.materials.store');
    });

    Route::middleware('permission:edit materials')->group(function () {
        Route::get('/materials/{material}/edit', [App\Http\Controllers\Admin\MaterialController::class, 'edit'])->name('admin.materials.edit');
        Route::put('/materials/{material}', [App\Http\Controllers\Admin\MaterialController::class, 'update'])->name('admin.materials.update');
    });

    Route::middleware('permission:delete materials')->group(function () {
        Route::delete('/materials/{material}', [App\Http\Controllers\Admin\MaterialController::class, 'destroy'])->name('admin.materials.destroy');
    });

    // Users
    Route::middleware('permission:view users')->group(function () {
        Route::get('/users', function() {
            return view('admin.users.index');
        })->name('admin.users');
    });

    // Profile - accessible to any authenticated user
    Route::get('/profile', [App\Http\Controllers\AdminController::class, 'profile'])->name('admin.profile');
    Route::put('/profile', [App\Http\Controllers\AdminController::class, 'updateProfile'])->name('admin.profile.update');
    Route::put('/profile/password', [App\Http\Controllers\AdminController::class, 'updatePassword'])->name('admin.profile.password');
    Route::post('/profile/image', [App\Http\Controllers\AdminController::class, 'updateProfileImage'])->name('admin.profile.update-image');

    // Cleanup Tool
    Route::get('/cleanup', [App\Http\Controllers\Admin\CleanupController::class, 'index'])->name('admin.cleanup.index');
    Route::post('/cleanup/scan', [App\Http\Controllers\Admin\CleanupController::class, 'scan'])->name('admin.cleanup.scan');
    Route::post('/cleanup/delete', [App\Http\Controllers\Admin\CleanupController::class, 'cleanup'])->name('admin.cleanup.delete');

    // Settings
    Route::middleware('permission:view settings')->group(function () {
        Route::get('/settings', [App\Http\Controllers\AdminController::class, 'settings'])
            ->name('admin.settings');
    });

    Route::middleware('permission:edit settings')->group(function () {
        Route::post('/settings', [App\Http\Controllers\AdminController::class, 'updateSettings'])
            ->name('admin.settings.update');
    });

    // Roles & Permissions
    Route::middleware('permission:view roles')->group(function () {
        Route::get('/roles', [App\Http\Controllers\Admin\RolePermissionController::class, 'roles'])
            ->name('admin.roles');
    });

    Route::middleware('permission:view permissions')->group(function () {
        Route::get('/permissions', [App\Http\Controllers\Admin\PermissionController::class, 'index'])
            ->name('admin.permissions');
    });

    Route::middleware('permission:create permissions')->group(function () {
        Route::get('/permissions/create', [App\Http\Controllers\Admin\PermissionController::class, 'create'])
            ->name('admin.permissions.create');
        Route::post('/permissions', [App\Http\Controllers\Admin\PermissionController::class, 'store'])
            ->name('admin.permissions.store');
    });

    Route::middleware('permission:edit permissions')->group(function () {
        Route::get('/permissions/{permission}/edit', [App\Http\Controllers\Admin\PermissionController::class, 'edit'])
            ->name('admin.permissions.edit');
        Route::put('/permissions/{permission}', [App\Http\Controllers\Admin\PermissionController::class, 'update'])
            ->name('admin.permissions.update');
    });

    Route::middleware('permission:delete permissions')->group(function () {
        Route::delete('/permissions/{permission}', [App\Http\Controllers\Admin\PermissionController::class, 'destroy'])
            ->name('admin.permissions.destroy');
    });

    // File Upload - accessible to users who can edit items
    Route::middleware('permission:edit items')->group(function () {
        Route::post('/upload', [App\Http\Controllers\AdminController::class, 'uploadFile'])->name('admin.upload');
    });
});
