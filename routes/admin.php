<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;

use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\DocumentTypeController;
use App\Http\Controllers\Admin\MaterialController;
use App\Http\Controllers\Admin\LanguageController;
use App\Http\Controllers\Admin\ScriptController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\ProfileController;

// All routes in this file are prefixed with 'admin' and protected by the 'admin' middleware
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');



    // Categories
    Route::resource('categories', CategoryController::class);
    Route::delete('categories/{category}/delete-image', [CategoryController::class, 'deleteImage'])->name('categories.delete-image');

    // Item Types
    Route::resource('item-types', DocumentTypeController::class);

    // Materials
    Route::resource('materials', MaterialController::class);

    // Languages
    Route::resource('languages', LanguageController::class);

    // Scripts
    Route::resource('scripts', ScriptController::class);

    // Users
    Route::resource('users', UserController::class);

    // Settings
    Route::get('settings', [SettingController::class, 'index'])->name('settings');
    Route::post('settings', [SettingController::class, 'update'])->name('settings.update');

    // Profile
    Route::get('profile', [ProfileController::class, 'edit'])->name('profile');
    Route::put('profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password');
});
