@extends('layouts.app')

@section('title', 'แผนที่ข้อมูล')

@section('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
<link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

<style>
    body {
        overflow: hidden;
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
    }

    footer {
        display: none !important;
    }

    .map-container-wrapper {
        position: fixed;
        top: 80px; /* ความสูงของ navbar + padding */
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    #map-container {
        height: 100%;
        width: 100%;
        z-index: 1;
    }

    .map-sidebar {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 350px;
        max-height: calc(100% - 20px);
        z-index: 1000;
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .map-sidebar-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .map-sidebar-body {
        padding: 15px;
        overflow-y: auto;
        max-height: calc(100vh - 150px);
    }

    .map-info {
        max-height: 100%;
        overflow-y: auto;
    }

    .item-list {
        max-height: 100%;
        overflow-y: auto;
    }

    .item-element {
        padding: 8px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .item-element:hover {
        background-color: #f8f9fa;
    }

    .item-element.active {
        background-color: #e9f5ff;
        border-left: 3px solid #007bff;
    }

    .item-element h5 {
        margin-bottom: 3px;
        font-size: 0.95rem;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .item-element p {
        margin-bottom: 0;
        font-size: 0.85rem;
        color: #6c757d;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Responsive adjustments for item elements */
    @media (max-width: 576px) {
        .item-element {
            padding: 6px;
        }

        .item-element h5 {
            font-size: 0.9rem;
            margin-bottom: 2px;
        }

        .item-element p {
            font-size: 0.8rem;
        }
    }

    /* Pagination styles */
    .pagination-container {
        border-top: 1px solid #eee;
        padding-top: 10px;
        margin-top: 10px;
        font-size: 0.9rem;
    }

    .pagination-buttons button {
        min-width: 32px;
        padding: 0.25rem 0.5rem;
    }

    .pagination-buttons button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .pagination-container {
            font-size: 0.8rem;
        }

        .pagination-buttons button {
            min-width: 28px;
            padding: 0.2rem 0.4rem;
        }

        #itemsPerPage {
            width: 50px !important;
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
        }
    }

    .leaflet-popup-content {
        min-width: 200px;
    }

    .leaflet-popup-content h5 {
        margin-bottom: 5px;
        font-size: 1rem;
    }

    .leaflet-popup-content p {
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .leaflet-popup-content .btn {
        margin-top: 10px;
        color: #fff !important;
        font-weight: 500;
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background-color: #0d6efd;
        border-color: #0d6efd;
        text-decoration: none;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .leaflet-popup-content .btn:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
        color: #fff !important;
    }



    .toggle-sidebar {
        position: absolute;
        top: 10px;
        right: 370px;
        z-index: 1000;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 1px 5px rgba(0,0,0,0.2);
        padding: 8px 12px;
        font-weight: 500;
        text-decoration: none;
        color: #333;
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
    }

    .toggle-sidebar:hover {
        background-color: #f8f9fa;
        color: #0d6efd;
    }

    .sidebar-collapsed {
        transform: translateX(360px);
    }

    /* Styles for loading effect */
    #map-container.content-loading {
        filter: blur(2px);
        transition: all 0.3s ease;
        pointer-events: none;
        opacity: 0.6;
    }

    .item-list.content-loading {
        filter: blur(2px);
        transition: all 0.3s ease;
        pointer-events: none;
        opacity: 0.6;
    }
</style>
@endsection

@section('content')
<div class="container-fluid py-2">
    <div class="row mb-2">
        <div class="col-12">
            <h4 class="mb-0">แผนที่ข้อมูลรายการ</h4>
            <p class="text-muted small">แสดงตำแหน่งของรายการทั้งหมดที่มีข้อมูลพิกัดทางภูมิศาสตร์</p>
        </div>
    </div>
</div>

<div class="map-container-wrapper">
    <!-- Loading Indicator -->
    <div id="map-loading-indicator" class="wire-loading-indicator" style="display: none;">
        <div class="loading-spinner" role="status">
            <span class="visually-hidden">กำลังโหลด...</span>
        </div>
        <div class="loading-text">กำลังโหลดข้อมูล</div>
        <div class="loading-dots">
            <span class="dot dot-1">.</span>
            <span class="dot dot-2">.</span>
            <span class="dot dot-3">.</span>
        </div>
    </div>

    <button type="button" class="toggle-sidebar" id="toggle-sidebar">
        <i class="fas fa-bars me-2"></i> <span id="toggle-text">ซ่อนรายการ</span>
    </button>

    <div id="map-container"></div>

    <div class="map-sidebar" id="map-sidebar">
        <div class="map-sidebar-header">
            <h5 class="mb-0">รายการทั้งหมด</h5>
            <span class="badge bg-primary">{{ count($items) }} รายการ</span>
        </div>
        <div class="map-sidebar-body">
            @include('livewire.map-search', [
                'categories' => $categories,
                'itemTypes' => $itemTypes,
                'provinces' => $provinces,
                'languages' => $languages,
                'scripts' => $scripts
            ])
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

<script>
    // Global variables for map data
    let allItemsData = @json($items);
    let map, markers, allMarkers = {};
    let currentFilters = {
        searchTerm: '',
        categoryIds: [],
        itemTypeIds: [],
        countryIds: [],
        provinceIds: [],
        languageIds: [],
        scriptIds: []
    };

    // Pagination variables
    let currentPage = 1;
    let itemsPerPage = 10;
    let totalPages = 1;
    let paginatedItems = [];

    document.addEventListener('DOMContentLoaded', function() {
        // สร้างแผนที่
        const map = L.map('map-container');

        // เพิ่ม control สำหรับย้อนกลับไปมุมมองเริ่มต้น
        const resetViewControl = L.control({ position: 'topleft' });
        resetViewControl.onAdd = function(map) {
            const div = L.DomUtil.create('div', 'leaflet-bar leaflet-control');
            div.innerHTML = `<a href="#" title="กลับไปมุมมองเริ่มต้น" role="button" aria-label="กลับไปมุมมองเริ่มต้น" style="display: flex; align-items: center; justify-content: center; width: 30px; height: 30px;"><i class="fas fa-home"></i></a>`;
            div.onclick = function() {
                if (Object.keys(allMarkers).length > 0) {
                    map.fitBounds(markers.getBounds(), { padding: [50, 50] });
                } else {
                    map.setView([13.7563, 100.5018], 6);
                }
                return false;
            };
            return div;
        };
        resetViewControl.addTo(map);

        // เพิ่ม tile layer จาก OpenStreetMap
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // สร้าง marker cluster group
        const markers = L.markerClusterGroup();

        // ข้อมูลรายการ
        const items = @json($items);

        // ตัวแปรเก็บ marker ทั้งหมด
        const allMarkers = {};

        // ถ้าไม่มีข้อมูล
        if (items.length === 0) {
            // ตั้งค่าแผนที่ให้แสดงประเทศไทย
            map.setView([13.7563, 100.5018], 6);
            return;
        }

        // ฟังก์ชันสำหรับสร้าง marker
        function createMarkers(items) {
            // ล้าง marker เดิมทั้งหมด
            markers.clearLayers();

            // ล้างตัวแปรเก็บ marker
            Object.keys(allMarkers).forEach(key => {
                delete allMarkers[key];
            });

            // วนลูปเพิ่ม marker สำหรับแต่ละรายการ
            items.forEach(function(item) {
                if (item.latitude && item.longitude) {
                    // สร้าง marker
                    const marker = L.marker([item.latitude, item.longitude]);

                    // สร้าง popup content
                    let popupContent = `
                        <h5>${item.title}</h5>
                    `;

                    if (item.location) {
                        popupContent += `<p><i class="fas fa-map-marker-alt me-1"></i> ${item.location}</p>`;
                    }

                    // แสดงข้อมูลประเทศ
                    if (item.country) {
                        if (item.country === 'th' || item.country === 'TH') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศไทย</p>`;
                        } else if (item.country === 'mm' || item.country === 'MM') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศเมียนมาร์</p>`;
                        } else if (item.country === 'la' || item.country === 'LA') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศลาว</p>`;
                        } else if (item.country === 'kh' || item.country === 'KH') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศกัมพูชา</p>`;
                        } else if (item.country === 'vn' || item.country === 'VN') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศเวียดนาม</p>`;
                        } else if (item.country === 'cn' || item.country === 'CN') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศจีน</p>`;
                        } else {
                            // ดึงชื่อประเทศจาก countryRelation ถ้ามี
                            if (item.country_relation && item.country_relation.name) {
                                popupContent += `<p><i class="fas fa-globe me-1"></i> ${item.country_relation.name}</p>`;
                            } else {
                                popupContent += `<p><i class="fas fa-globe me-1"></i> ${item.country}</p>`;
                            }
                        }
                    } else if (item.province) {
                        // ถ้าไม่มีข้อมูลประเทศแต่มีข้อมูลจังหวัด
                        if (item.province === 'MM' || item.province === 'mm') {
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศเมียนมาร์</p>`;
                        } else {
                            // ถ้าเป็นจังหวัดอื่นๆ ให้ถือว่าเป็นประเทศไทย
                            popupContent += `<p><i class="fas fa-globe me-1"></i> ประเทศไทย</p>`;
                            popupContent += `<p><i class="fas fa-map me-1"></i> จังหวัด${item.province_name || item.province}</p>`;
                        }
                    }

                    if (item.category) {
                        popupContent += `<p><i class="fas fa-folder me-1"></i> ${item.category.name}</p>`;
                    }

                    if (item.item_type) {
                        popupContent += `<p><i class="fas fa-file-alt me-1"></i> ${item.item_type.name}</p>`;
                    }

                    // สร้าง URL ที่ถูกต้องโดยใช้ subfolder จาก .env
                    const baseUrl = '{{ env("APP_SUBFOLDER") ? "/" . env("APP_SUBFOLDER") : "" }}';
                    popupContent += `<a href="${baseUrl}/items/${item.id}" class="btn btn-sm btn-primary"><i class="fas fa-eye me-1"></i> ดูรายละเอียด</a>`;

                    // เพิ่ม popup ให้กับ marker
                    marker.bindPopup(popupContent);

                    // เพิ่ม marker เข้าไปใน cluster
                    markers.addLayer(marker);

                    // เก็บ marker ไว้ใน object เพื่อใช้อ้างอิงภายหลัง
                    allMarkers[item.id] = marker;
                }
            });

            // เพิ่ม marker cluster เข้าไปในแผนที่
            map.addLayer(markers);

            // ปรับ view ให้แสดงทุก marker
            if (Object.keys(allMarkers).length > 0) {
                map.fitBounds(markers.getBounds(), { padding: [50, 50] });
            }

            // เพิ่ม event listener สำหรับรายการ
            setupItemListeners();
        }

        // ฟังก์ชันสำหรับเพิ่ม event listener ให้กับรายการ
        function setupItemListeners() {
            const itemElements = document.querySelectorAll('.item-element');
            itemElements.forEach(function(item) {
                item.addEventListener('click', function() {
                    // ลบ class active จากทุกรายการ
                    document.querySelectorAll('.item-element').forEach(function(el) {
                        el.classList.remove('active');
                    });

                    // เพิ่ม class active ให้กับรายการที่คลิก
                    this.classList.add('active');

                    // ดึงข้อมูล
                    const id = this.dataset.id;
                    const lat = parseFloat(this.dataset.lat);
                    const lng = parseFloat(this.dataset.lng);

                    // หา marker ที่ตรงกับ id
                    const marker = allMarkers[id];

                    if (marker) {
                        // เลื่อนแผนที่ไปที่ marker
                        map.setView([lat, lng], 15);

                        // เปิด popup
                        marker.openPopup();
                    }
                });
            });
        }

        // Initialize map with all items
        createMarkers(allItemsData);

        // Update item list with all items
        updateItemList(allItemsData);

        // Initialize filter counts
        document.getElementById('filteredCount').textContent = allItemsData.length;
        document.getElementById('totalCount').textContent = allItemsData.length;

        // ฟังก์ชันสำหรับซ่อน/แสดง sidebar
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const mapSidebar = document.getElementById('map-sidebar');
        const toggleText = document.getElementById('toggle-text');

        toggleSidebar.addEventListener('click', function() {
            mapSidebar.classList.toggle('sidebar-collapsed');

            if (mapSidebar.classList.contains('sidebar-collapsed')) {
                toggleText.textContent = 'แสดงรายการ';
                toggleSidebar.style.right = '10px';
            } else {
                toggleText.textContent = 'ซ่อนรายการ';
                toggleSidebar.style.right = '370px';
            }
        });

        // Function to show loading indicator
        function showLoading() {
            const loadingIndicator = document.getElementById('map-loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'flex';

                const mapContainer = document.getElementById('map-container');
                if (mapContainer) {
                    mapContainer.classList.add('content-loading');
                }

                const itemList = document.querySelector('.item-list');
                if (itemList) {
                    itemList.classList.add('content-loading');
                }
            }
        }

        // Function to hide loading indicator
        function hideLoading() {
            const loadingIndicator = document.getElementById('map-loading-indicator');
            if (loadingIndicator) {
                setTimeout(() => {
                    loadingIndicator.style.display = 'none';

                    const mapContainer = document.getElementById('map-container');
                    if (mapContainer) {
                        mapContainer.classList.remove('content-loading');
                    }

                    const itemList = document.querySelector('.item-list');
                    if (itemList) {
                        itemList.classList.remove('content-loading');
                    }
                }, 500); // เพิ่มดีเลย์เล็กน้อยเพื่อให้ผู้ใช้เห็น spinner
            }
        }

        // Function to filter items directly with JavaScript
        window.filterItems = function(filters) {
            console.log('Filtering items with:', filters);

            try {
                // Show loading indicator
                showLoading();

                // Update current filters
                if (filters) {
                    currentFilters = {...currentFilters, ...filters};
                }

                // Filter the items
                const filteredItems = allItemsData.filter(item => {
                    // Filter by search term
                    const matchesSearch = !currentFilters.searchTerm ||
                        item.title.toLowerCase().includes(currentFilters.searchTerm.toLowerCase()) ||
                        (item.description && item.description.toLowerCase().includes(currentFilters.searchTerm.toLowerCase())) ||
                        (item.location && item.location.toLowerCase().includes(currentFilters.searchTerm.toLowerCase()));

                    // Check if any filter has our special "no match" value
                    const hasNoMatchFilter =
                        currentFilters.categoryIds.includes('NO_MATCH_FILTER_VALUE') ||
                        currentFilters.itemTypeIds.includes('NO_MATCH_FILTER_VALUE') ||
                        currentFilters.countryIds.includes('NO_MATCH_FILTER_VALUE') ||
                        currentFilters.provinceIds.includes('NO_MATCH_FILTER_VALUE') ||
                        currentFilters.languageIds.includes('NO_MATCH_FILTER_VALUE') ||
                        currentFilters.scriptIds.includes('NO_MATCH_FILTER_VALUE');

                    // If we have a "no match" filter, don't show any items
                    if (hasNoMatchFilter) {
                        return false;
                    }

                    // Filter by category
                    const matchesCategory = currentFilters.categoryIds.length === 0 ||
                        (item.category_id && currentFilters.categoryIds.includes(parseInt(item.category_id)));

                    // Filter by item type
                    const matchesItemType = currentFilters.itemTypeIds.length === 0 ||
                        (item.item_type_id && currentFilters.itemTypeIds.includes(parseInt(item.item_type_id)));

                    // Filter by country
                    const matchesCountry = currentFilters.countryIds.length === 0 ||
                        (item.country && currentFilters.countryIds.includes(item.country)) ||
                        // ถ้าไม่มีข้อมูลประเทศในรายการ แต่มีข้อมูลจังหวัด ให้ถือว่าเป็นประเทศไทย
                        (!item.country && item.province && item.province !== 'MM' && currentFilters.countryIds.includes('TH')) ||
                        // ถ้าจังหวัดเป็น MM ให้ถือว่าเป็นประเทศเมียนมาร์
                        (item.province === 'MM' && currentFilters.countryIds.includes('MM'));

                    // Filter by province (could be string or integer)
                    const matchesProvince = currentFilters.provinceIds.length === 0 ||
                        (item.province && (
                            currentFilters.provinceIds.includes(item.province) ||
                            currentFilters.provinceIds.includes(parseInt(item.province))
                        ));

                    // Filter by language
                    const matchesLanguage = currentFilters.languageIds.length === 0 ||
                        (item.language_id && currentFilters.languageIds.includes(parseInt(item.language_id)));

                    // Filter by script
                    const matchesScript = currentFilters.scriptIds.length === 0 ||
                        (item.script_id && currentFilters.scriptIds.includes(parseInt(item.script_id)));

                    return matchesSearch && matchesCategory && matchesItemType &&
                           matchesCountry && matchesProvince && matchesLanguage && matchesScript;
                });

                console.log(`Found ${filteredItems.length} items after filtering`);

                // Clear existing markers
                markers.clearLayers();
                Object.keys(allMarkers).forEach(key => {
                    delete allMarkers[key];
                });

                // If we have filtered items
                if (filteredItems.length > 0) {
                    // Create markers for filtered items
                    createMarkers(filteredItems);

                    // Update item list
                    updateItemList(filteredItems);

                    // Adjust map view
                    if (Object.keys(allMarkers).length > 0) {
                        map.fitBounds(markers.getBounds(), { padding: [50, 50] });
                    }

                    // Hide loading indicator
                    hideLoading();
                } else {
                    // No items found
                    console.log('No items found, showing default view');
                    map.setView([13.7563, 100.5018], 6);

                    // Show message
                    const itemList = document.querySelector('.item-list');
                    if (itemList) {
                        itemList.innerHTML = '<div class="alert alert-info">ไม่พบรายการที่ตรงกับเงื่อนไขการค้นหา</div>';
                    }

                    // Hide loading indicator
                    hideLoading();
                }

                // Update filter counts in the UI
                document.getElementById('filteredCount').textContent = filteredItems.length;
                document.getElementById('totalCount').textContent = allItemsData.length;

            } catch (error) {
                console.error('Error filtering items:', error);
                // Hide loading indicator in case of error
                hideLoading();
            }
        };

        // Reset all filters
        window.resetFilters = function() {
            // Show loading indicator
            showLoading();

            // Reset filter values
            currentFilters = {
                searchTerm: '',
                categoryIds: [],
                itemTypeIds: [],
                countryIds: [],
                provinceIds: [],
                languageIds: [],
                scriptIds: []
            };

            // Reset search input
            document.getElementById('searchTerm').value = '';

            // Reset all checkboxes - check "All" options, uncheck individual items
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.id.includes('_all')) {
                    checkbox.checked = true;
                } else {
                    checkbox.checked = false;
                }
            });

            // Show all items
            window.filterItems();
        };

        // Set up event listeners for filter inputs
        document.getElementById('searchTerm').addEventListener('input', function(e) {
            // Show loading indicator before filtering
            showLoading();
            // Add a small delay to prevent too many requests when typing quickly
            setTimeout(() => {
                window.filterItems({searchTerm: e.target.value});
            }, 300);
        });

        // Set up event listeners for checkboxes
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            if (!checkbox.id.includes('_all')) {
                checkbox.addEventListener('change', function() {
                    // Show loading indicator before filtering
                    showLoading();

                    // Special case for item type which uses "type_" prefix but needs "itemTypeIds" filter
                    let filterType;
                    if (this.id.startsWith('type_')) {
                        filterType = 'itemTypeIds';
                    } else if (this.id.startsWith('country_')) {
                        filterType = 'countryIds';
                        console.log('Country filter selected:', this.value);
                    } else {
                        filterType = this.id.split('_')[0] + 'Ids';
                    }

                    // Parse all values as integers if possible
                    const value = parseInt(this.value) || this.value;

                    if (this.checked) {
                        // Add to filter
                        if (!currentFilters[filterType].includes(value)) {
                            currentFilters[filterType].push(value);
                        }
                    } else {
                        // Remove from filter
                        currentFilters[filterType] = currentFilters[filterType].filter(id => id !== value);
                    }

                    window.filterItems();
                });
            }
        });

        // Set up event listeners for "All" checkboxes
        document.querySelectorAll('input[id$="_all"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Show loading indicator before filtering
                showLoading();

                const checkboxId = this.id.split('_')[0];

                // Special case for item type which uses "doctype" prefix but needs to select "type_" elements
                let filterType, checkboxPrefix;
                if (checkboxId === 'doctype') {
                    filterType = 'itemType';
                    checkboxPrefix = 'type';
                } else if (checkboxId === 'country') {
                    filterType = 'country';
                    checkboxPrefix = 'country';
                    console.log('Country "All" checkbox changed:', this.checked);
                } else {
                    filterType = checkboxId;
                    checkboxPrefix = checkboxId;
                }

                const checkboxes = document.querySelectorAll(`input[id^="${checkboxPrefix}_"]:not([id$="_all"])`);

                if (this.checked) {
                    // When "All" is checked, uncheck all individual checkboxes
                    checkboxes.forEach(cb => {
                        cb.checked = false;
                    });

                    // Clear the filter to show all items
                    currentFilters[filterType + 'Ids'] = [];
                } else {
                    // When "All" is unchecked, leave all individual checkboxes unchecked
                    // This is the key change - we're not checking any boxes when "All" is unchecked
                    checkboxes.forEach(cb => {
                        cb.checked = false;
                    });

                    // Since no individual items are selected, we should filter to show nothing
                    // We'll use a special value that won't match any real ID
                    currentFilters[filterType + 'Ids'] = ['NO_MATCH_FILTER_VALUE'];
                }

                window.filterItems();
            });
        });

        // Additional event listener to handle "All" checkbox state
        document.querySelectorAll('input[type="checkbox"]:not([id$="_all"])').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Show loading indicator before filtering
                showLoading();

                // Special case for item type which uses "type_" prefix but needs "doctype_all" checkbox
                let checkboxPrefix, filterType, allCheckboxId;

                if (this.id.startsWith('type_')) {
                    checkboxPrefix = 'type';
                    filterType = 'itemType';
                    allCheckboxId = 'doctype_all';
                } else if (this.id.startsWith('country_')) {
                    checkboxPrefix = 'country';
                    filterType = 'country';
                    allCheckboxId = 'country_all';
                    console.log('Country checkbox changed:', this.value, this.checked);
                } else {
                    checkboxPrefix = this.id.split('_')[0];
                    filterType = checkboxPrefix;
                    allCheckboxId = checkboxPrefix + '_all';
                }

                const allCheckbox = document.getElementById(allCheckboxId);
                const checkboxes = document.querySelectorAll(`input[id^="${checkboxPrefix}_"]:not([id$="_all"])`);
                const checkedCount = document.querySelectorAll(`input[id^="${checkboxPrefix}_"]:not([id$="_all"]):checked`).length;

                // Update the filter array based on checked items
                currentFilters[filterType + 'Ids'] = [];

                if (checkedCount > 0) {
                    // If any individual checkbox is checked, uncheck the "All" checkbox
                    allCheckbox.checked = false;

                    // Add all checked values to the filter
                    document.querySelectorAll(`input[id^="${checkboxPrefix}_"]:not([id$="_all"]):checked`).forEach(cb => {
                        // Parse all values as integers if possible
                        const value = parseInt(cb.value) || cb.value;
                        currentFilters[filterType + 'Ids'].push(value);
                    });
                } else {
                    // If no individual checkboxes are checked, check the "All" checkbox
                    allCheckbox.checked = true;
                    // Empty array means "show all" for this filter
                    currentFilters[filterType + 'Ids'] = [];
                }

                window.filterItems();
            });
        });

        // Reset button event listener
        document.getElementById('resetFiltersBtn').addEventListener('click', function() {
            // Show loading indicator before resetting
            showLoading();
            window.resetFilters();
        });

        // Function to reset map view to show all items
        window.resetMapView = function() {
            // Reset filters and show all items
            window.resetFilters();
        };

        // Function to paginate items
        function paginateItems(items, page, perPage) {
            // Calculate total pages
            totalPages = Math.ceil(items.length / perPage);

            // Ensure current page is valid
            if (page < 1) page = 1;
            if (page > totalPages) page = totalPages;

            // Store current page
            currentPage = page;

            // Calculate start and end indices
            const startIndex = (page - 1) * perPage;
            const endIndex = Math.min(startIndex + perPage, items.length);

            // Get the items for the current page
            return items.slice(startIndex, endIndex);
        }

        // Function to generate pagination controls
        function generatePaginationControls(totalItems) {
            if (totalItems === 0) return '';

            totalPages = Math.ceil(totalItems / itemsPerPage);

            let html = '<div class="pagination-container mt-2 d-flex flex-wrap justify-content-between align-items-center gap-2">';

            // Compact display for small screens
            html += '<div class="d-flex flex-wrap align-items-center gap-2">';

            // Items per page selector (more compact)
            html += `
                <div class="items-per-page d-flex align-items-center">
                    <select id="itemsPerPage" class="form-select form-select-sm" style="width: 60px;">
                        <option value="5" ${itemsPerPage === 5 ? 'selected' : ''}>5</option>
                        <option value="10" ${itemsPerPage === 10 ? 'selected' : ''}>10</option>
                        <option value="20" ${itemsPerPage === 20 ? 'selected' : ''}>20</option>
                        <option value="50" ${itemsPerPage === 50 ? 'selected' : ''}>50</option>
                    </select>
                    <span class="ms-1 small">/ หน้า</span>
                </div>
            `;

            // Page info (more compact)
            html += `<span class="small text-muted">${currentPage}/${totalPages}</span>`;

            html += '</div>';

            // Pagination buttons (more compact)
            html += '<div class="pagination-buttons btn-group btn-group-sm">';

            // Previous button
            html += `<button class="btn btn-outline-secondary" ${currentPage === 1 ? 'disabled' : ''} data-page="${currentPage - 1}">
                <i class="fas fa-chevron-left"></i>
            </button>`;

            // Next button
            html += `<button class="btn btn-outline-secondary" ${currentPage === totalPages ? 'disabled' : ''} data-page="${currentPage + 1}">
                <i class="fas fa-chevron-right"></i>
            </button>`;

            html += '</div>';
            html += '</div>';

            return html;
        }

        // Function to set up pagination event listeners
        function setupPaginationListeners() {
            // Page navigation buttons
            document.querySelectorAll('.pagination-buttons button').forEach(button => {
                if (!button.disabled) {
                    button.addEventListener('click', function() {
                        const page = parseInt(this.dataset.page);
                        updateItemList(paginatedItems, page);
                    });
                }
            });

            // Items per page selector
            const itemsPerPageSelect = document.getElementById('itemsPerPage');
            if (itemsPerPageSelect) {
                itemsPerPageSelect.addEventListener('change', function() {
                    itemsPerPage = parseInt(this.value);
                    updateItemList(paginatedItems, 1);
                });
            }
        }

        // ฟังก์ชันสำหรับอัพเดทรายการในแถบด้านข้าง
        function updateItemList(items, page = 1) {
            const itemList = document.querySelector('.item-list');
            if (!itemList) return;

            // Store the full list of items for pagination
            paginatedItems = items;

            if (items.length === 0) {
                itemList.innerHTML = '<div class="alert alert-info">ไม่พบรายการที่ตรงกับเงื่อนไขการค้นหา</div>';
                return;
            }

            // Get paginated items
            const paginatedItemsForPage = paginateItems(items, page, itemsPerPage);

            let html = '';

            // Add item count summary (more compact)
            html += `<div class="mb-2 text-muted small">
                <span class="badge bg-primary">${paginatedItemsForPage.length}</span> จาก
                <span class="badge bg-secondary">${items.length}</span> รายการ
            </div>`;

            // Generate item elements
            paginatedItemsForPage.forEach(item => {
                html += `
                    <div class="item-element" data-id="${item.id}" data-lat="${item.latitude}" data-lng="${item.longitude}">
                        <h5>${item.title}</h5>
                        <p>`;

                if (item.location) {
                    html += `<i class="fas fa-map-marker-alt me-1"></i> ${item.location}`;
                }

                // แสดงข้อมูลประเทศและจังหวัด
                if (item.country) {
                    if (item.country === 'th' || item.country === 'TH') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศไทย</span>`;
                    } else if (item.country === 'mm' || item.country === 'MM') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศเมียนมาร์</span>`;
                    } else if (item.country === 'la' || item.country === 'LA') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศลาว</span>`;
                    } else if (item.country === 'kh' || item.country === 'KH') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศกัมพูชา</span>`;
                    } else if (item.country === 'vn' || item.country === 'VN') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศเวียดนาม</span>`;
                    } else if (item.country === 'cn' || item.country === 'CN') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศจีน</span>`;
                    } else {
                        // ดึงชื่อประเทศจาก countryRelation ถ้ามี
                        if (item.country_relation && item.country_relation.name) {
                            html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ${item.country_relation.name}</span>`;
                        } else {
                            html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ${item.country}</span>`;
                        }
                    }
                } else if (item.province) {
                    // ถ้าไม่มีข้อมูลประเทศแต่มีข้อมูลจังหวัด
                    if (item.province === 'MM' || item.province === 'mm') {
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศเมียนมาร์</span>`;
                    } else {
                        // ถ้าเป็นจังหวัดอื่นๆ ให้ถือว่าเป็นประเทศไทย
                        html += `<span class="ms-2"><i class="fas fa-globe me-1"></i> ประเทศไทย</span>`;
                        html += `<span class="ms-2"><i class="fas fa-map me-1"></i> จังหวัด${item.province_name || item.province}</span>`;
                    }
                }

                if (item.item_type) {
                    html += `<span class="ms-2"><i class="fas fa-file-alt me-1"></i> ${item.item_type.name}</span>`;
                }

                html += `</p>
                    </div>`;
            });

            // Add pagination controls
            html += generatePaginationControls(items.length);

            itemList.innerHTML = html;

            // Set up event listeners
            setupItemListeners();
            setupPaginationListeners();
        }
    });
</script>
@endsection
