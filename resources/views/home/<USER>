@extends('layouts.app')

@section('title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))

@section('description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญ'))

@section('keywords', 'คลังข้อมูลดิจิทัล, มรดกทางวัฒนธรรม, ข้อมูลประวัติศาสตร์, เอกสารสำคัญ, ดิจิทัลไลบรารี, digital collection, cultural heritage, historical data, digital library, digital archive')

@section('canonical', route('home'))

@section('og_type', 'website')
@section('og_title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('og_description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))
@section('og_url', route('home'))

@section('twitter_title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('twitter_description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))

@php
    $siteLogo = \App\Models\Setting::get('site_logo');
    $logoUrl = $siteLogo ? get_image_url($siteLogo, 'defaults/logo.png') : asset('images/defaults/logo.png');
@endphp

@section('og_image', $logoUrl)
@section('twitter_image', $logoUrl)

@section('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
    "description": "{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม') }}",
    "url": "{{ config('app.url') }}",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ route('search.index') }}?q={search_term_string}",
        "query-input": "required name=search_term_string"
    },
    "mainEntity": {
        "@type": "Organization",
        "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
        "description": "{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม') }}",
        "url": "{{ config('app.url') }}"
    }
}
</script>
@endsection

@section('styles')
<link rel="stylesheet" href="{{ asset('css/home.css') }}">
@endsection

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}</h1>
        <p>{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล') }}</p>
        <div class="search-box" style="opacity: 1; transform: translateZ(0);">
            <form action="{{ route('search.index') }}" method="GET">
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control shadow-sm" name="q" placeholder="ค้นหารายการ...">
                    <button class="btn btn-primary px-4 shadow-sm" type="submit">
                        <i class="fas fa-search me-2"></i>ค้นหา
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<section class="stats-section">
    <div class="container">
        <div class="section-header">
            <h2>สถิติคลังข้อมูล</h2>
            ข้อมูลสถิติสำคัญของคลังข้อมูลของเรา
        </div>

        <div class="stats-container">
            <div class="row g-4">
                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_items']) }}</h3>
                        <p class="stats-title">รายการทั้งหมด</p>
                    </div>
                </div>

                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_categories']) }}</h3>
                        <p class="stats-title">หมวดหมู่</p>
                    </div>
                </div>

                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_views']) }}</h3>
                        <p class="stats-title">จำนวนการเข้าชม</p>
                    </div>
                </div>

                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_item_types']) }}</h3>
                        <p class="stats-title">ประเภทรายการ</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="categories pt-3 pb-5">
    <div class="container">
        <div class="section-header">
            <h2>หมวดหมู่</h2>
            <a href="{{ route('categories.index') }}" class="view-all btn btn-link">
                ดูทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>

        <div class="row g-4">
            @foreach ($categories as $category)
                <div class="col-6 col-md-3">
                    <a href="{{ route('categories.show', $category->id) }}" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <img src="{{ get_image_url($category->image_path, 'defaults/category-default.svg') }}" alt="{{ $category->name }}">
                            </div>
                            <h3>{{ $category->name }}</h3>
                            <p>{{ $category->items->count() ?? 0 }} รายการ</p>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>

<section class="item-types py-5">
    <div class="container">
        <div class="section-header">
            <h2>ประเภทรายการ</h2>
        </div>

        <div class="row g-4">
            @forelse ($itemTypes as $type)
                <div class="col-6 col-md-3 mb-4">
                    <a href="{{ route('items.index') }}?item_type_id={{ $type->id }}" class="text-decoration-none">
                        <div class="item-type-card">
                            <div class="item-type-icon">
                                @php
                                    // Use the icon_class from the database, with a fallback
                                    $iconClass = $type->icon_class ?? 'fas fa-file-alt';

                                    // Make sure icon class starts with fa-
                                    if (!empty($iconClass) && strpos($iconClass, 'fa-') === false) {
                                        $iconClass = 'fa-' . $iconClass;
                                    }

                                    // Add fas if not present
                                    if (!empty($iconClass) && strpos($iconClass, 'fas ') !== 0 && strpos($iconClass, 'far ') !== 0 && strpos($iconClass, 'fab ') !== 0) {
                                        $iconClass = 'fas ' . $iconClass;
                                    }

                                    // Set default color based on icon type
                                    $iconColor = '#0d6efd'; // Default blue

                                    // Assign colors based on icon class
                                    if (strpos($iconClass, 'fa-music') !== false || strpos($iconClass, 'fa-file-audio') !== false) {
                                        $iconColor = '#6f42c1'; // Purple for audio
                                    } elseif (strpos($iconClass, 'fa-film') !== false || strpos($iconClass, 'fa-file-video') !== false) {
                                        $iconColor = '#fd7e14'; // Orange for video
                                    } elseif (strpos($iconClass, 'fa-image') !== false || strpos($iconClass, 'fa-file-image') !== false) {
                                        $iconColor = '#20c997'; // Green for images
                                    } elseif (strpos($iconClass, 'fa-scroll') !== false) {
                                        $iconColor = '#dc3545'; // Red for ancient documents
                                    }
                                @endphp
                                <i class="{{ $iconClass }}" style="color: {{ $iconColor }};"></i>
                            </div>
                            <h3>{{ $type->name }}</h3>
                            <p>{{ $type->items_count }} รายการ</p>
                        </div>
                    </a>
                </div>
            @empty
                <div class="col-12 text-center py-4">
                    <div style="background-color: #f0f7ff; padding: 30px; border-radius: 8px;">
                        <i class="fas fa-folder-open text-primary mb-3" style="font-size: 2rem;"></i>
                        <h4 class="mt-3">ไม่พบประเภทรายการ</h4>
                        <p class="text-muted">ยังไม่มีการเพิ่มประเภทรายการในระบบ</p>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</section>

<section class="latest-items py-5">
    <div class="container">
        <div class="section-header">
            <h2>รายการล่าสุด</h2>
            <a href="{{ route('items.index') }}" class="view-all btn btn-link">
                ดูทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>

        <div class="row g-4">
            @foreach ($latestItems as $item)
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="item-card">
                        <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none">
                            <img src="{{ get_image_url($item->image_path, 'defaults/item-default.svg') }}" alt="{{ $item->title }}" class="item-img">
                            <div class="item-info">
                                <h3>{{ $item->title }}</h3>
                                <p>{{ $item->year }}</p>
                            </div>
                            <div class="item-meta">
                                @if($item->itemType)
                                    <span class="badge bg-secondary"><i class="fas fa-file-alt me-1"></i> {{ $item->itemType->name }}</span>
                                @endif
                                <span class="badge bg-secondary"><i class="far fa-calendar-alt me-1"></i> {{ $item->created_at->format('d/m/Y') }}</span>
                            </div>
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script src="{{ asset('js/home.js') }}"></script>
@endsection