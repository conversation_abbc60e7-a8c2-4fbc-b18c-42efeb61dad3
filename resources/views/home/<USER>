@extends('layouts.app')

@section('title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))

@section('description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญ'))

@section('keywords', 'คลังข้อมูลดิจิทัล, มรดกทางวัฒนธรรม, ข้อมูลประวัติศาสตร์, เอกสารสำคัญ, ดิจิทัลไลบรารี, digital collection, cultural heritage, historical data, digital library, digital archive')

@section('canonical', route('home'))

@section('og_type', 'website')
@section('og_title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('og_description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))
@section('og_url', route('home'))

@section('twitter_title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('twitter_description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))

@php
    $siteLogo = \App\Models\Setting::get('site_logo');
    $logoUrl = $siteLogo ? get_image_url($siteLogo, 'defaults/logo.png') : asset('images/defaults/logo.png');
@endphp

@section('og_image', $logoUrl)
@section('twitter_image', $logoUrl)

@section('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
    "description": "{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม') }}",
    "url": "{{ config('app.url') }}",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ route('search.index') }}?q={search_term_string}",
        "query-input": "required name=search_term_string"
    },
    "mainEntity": {
        "@type": "Organization",
        "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
        "description": "{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม') }}",
        "url": "{{ config('app.url') }}"
    }
}
</script>
@endsection

@section('styles')
<style>
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .section-header h2 {
        font-weight: 700;
        color: #333;
        position: relative;
        display: inline-block;
    }

    .section-header .view-all {
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .section-header .view-all:hover {
        transform: translateX(5px);
    }

    /* Custom styles for the hero section */
    .hero .input-group {
        border-radius: 50px;
        overflow: hidden;
        position: relative;
        z-index: 10;
    }

    .hero .form-control {
        border-radius: 50px 0 0 50px;
        height: 54px;
        font-size: 1.1rem;
        border: none;
        position: relative;
    }

    .hero .btn-primary {
        border-radius: 0 50px 50px 0;
        padding-left: 25px;
        padding-right: 25px;
        font-weight: 500;
        font-size: 1.1rem;
        position: relative;
    }

    /* Fix search box position */
    .search-box {
        position: relative;
        max-width: 600px;
        margin: 30px auto 10px;
        transform: translateZ(0);
        backface-visibility: hidden;
        will-change: transform;
    }

    /* Stats Section Styles */
    .stats-section {
        background: #f8f9fa;
        position: relative;
        overflow: hidden;
        padding: 2rem 0;
        margin-top: 0;
        z-index: 1;
    }

    .stats-section .section-title {
        text-align: center;
        margin-bottom: 2.5rem;
        position: relative;
    }

    .stats-section .section-title h2 {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        position: relative;
        display: inline-block;
    }

    .stats-section .section-title h2::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: #0d6efd;
        border-radius: 2px;
    }

    .stats-section .section-title p {
        font-size: 1rem;
        color: #6c757d;
        max-width: 700px;
        margin: 0 auto;
    }

    /* Main Stats Card Container */
    .stats-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 2rem;
        position: relative;
        overflow: hidden;
        z-index: 1;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Individual Stats Card */
    .stats-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem 1rem;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
        height: 100%;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .stats-number.animate {
        animation: pulse 0.5s ease-in-out;
    }

    /* Stats Icon */
    .stats-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: white;
        width: 60px;
        height: 60px;
        line-height: 60px;
        border-radius: 50%;
        display: inline-block;
        position: relative;
        z-index: 1;
        transition: all 0.3s ease;
    }

    .stats-card:nth-child(1) .stats-icon {
        background: #0d6efd;
    }

    .stats-card:nth-child(2) .stats-icon {
        background: #6610f2;
    }

    .stats-card:nth-child(3) .stats-icon {
        background: #198754;
    }

    .stats-card:nth-child(4) .stats-icon {
        background: #dc3545;
    }

    /* Stats Number */
    .stats-number {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        line-height: 1.2;
        transition: all 0.3s ease;
        position: relative;
    }

    .stats-number::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: currentColor;
        transition: width 0.3s ease;
    }

    .stats-card:hover .stats-number::after {
        width: 50px;
    }

    .stats-card:nth-child(1) .stats-number {
        color: #0d6efd;
    }

    .stats-card:nth-child(2) .stats-number {
        color: #6610f2;
    }

    .stats-card:nth-child(3) .stats-number {
        color: #198754;
    }

    .stats-card:nth-child(4) .stats-number {
        color: #dc3545;
    }

    /* Stats Title */
    .stats-title {
        font-size: 1rem;
        color: #6c757d;
        margin-bottom: 0;
        font-weight: 500;
        position: relative;
        display: inline-block;
    }

    /* Featured Cards */
    .featured-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        height: 100%;
        transition: all 0.3s ease;
    }

    .featured-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .featured-header {
        background: linear-gradient(90deg, #4a6bff, #2541b8);
        color: white;
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
    }

    .featured-icon {
        background: rgba(255, 255, 255, 0.2);
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        margin-right: 1rem;
    }

    .featured-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .featured-body {
        padding: 1.5rem;
    }

    .featured-image {
        width: 120px;
        height: 120px;
        min-width: 120px;
        border-radius: 10px;
        overflow: hidden;
        margin-right: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .featured-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .featured-content h5 {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .featured-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 0.5rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* Responsive adjustments */
    @media (max-width: 767px) {
        .featured-image {
            width: 80px;
            height: 80px;
            min-width: 80px;
        }

        .featured-content h5 {
            font-size: 1rem;
        }

        .featured-meta {
            flex-direction: column;
            gap: 0.3rem;
        }

        .stats-number {
            font-size: 1.8rem;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            line-height: 60px;
            font-size: 1.8rem;
        }
    }
</style>
@endsection

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}</h1>
        <p>{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล') }}</p>
        <div class="search-box" style="opacity: 1; transform: translateZ(0);">
            <form action="{{ route('search.index') }}" method="GET">
                <div class="input-group input-group-lg">
                    <input type="text" class="form-control shadow-sm" name="q" placeholder="ค้นหารายการ...">
                    <button class="btn btn-primary px-4 shadow-sm" type="submit">
                        <i class="fas fa-search me-2"></i>ค้นหา
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<section class="stats-section">
    <div class="container">
        <div class="section-header">
            <h2>สถิติคลังข้อมูล</h2>
            ข้อมูลสถิติสำคัญของคลังข้อมูลของเรา
        </div>

        <div class="stats-container">
            <div class="row g-4">
                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_items']) }}</h3>
                        <p class="stats-title">รายการทั้งหมด</p>
                    </div>
                </div>

                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_categories']) }}</h3>
                        <p class="stats-title">หมวดหมู่</p>
                    </div>
                </div>

                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_views']) }}</h3>
                        <p class="stats-title">จำนวนการเข้าชม</p>
                    </div>
                </div>

                <div class="col-6 col-md-3">
                    <div class="stats-card text-center">
                        <div class="stats-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                        <h3 class="stats-number">{{ number_format($stats['total_item_types']) }}</h3>
                        <p class="stats-title">ประเภทรายการ</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="categories pt-3 pb-5">
    <div class="container">
        <div class="section-header">
            <h2>หมวดหมู่</h2>
            <a href="{{ route('categories.index') }}" class="view-all btn btn-link">
                ดูทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>

        <div class="row g-4">
            @foreach ($categories as $category)
                <div class="col-6 col-md-3">
                    <a href="{{ route('categories.show', $category->id) }}" class="text-decoration-none">
                        <div class="category-card">
                            <div class="category-icon">
                                <img src="{{ get_image_url($category->image_path, 'defaults/category-default.svg') }}" alt="{{ $category->name }}">
                            </div>
                            <h3>{{ $category->name }}</h3>
                            <p>{{ $category->items->count() ?? 0 }} รายการ</p>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>

<section class="item-types py-5">
    <div class="container">
        <div class="section-header">
            <h2>ประเภทรายการ</h2>
        </div>

        <div class="row g-4">
            @forelse ($itemTypes as $type)
                <div class="col-6 col-md-3 mb-4">
                    <a href="{{ route('items.index') }}?item_type_id={{ $type->id }}" class="text-decoration-none">
                        <div class="item-type-card">
                            <div class="item-type-icon">
                                @php
                                    // Use the icon_class from the database, with a fallback
                                    $iconClass = $type->icon_class ?? 'fas fa-file-alt';

                                    // Make sure icon class starts with fa-
                                    if (!empty($iconClass) && strpos($iconClass, 'fa-') === false) {
                                        $iconClass = 'fa-' . $iconClass;
                                    }

                                    // Add fas if not present
                                    if (!empty($iconClass) && strpos($iconClass, 'fas ') !== 0 && strpos($iconClass, 'far ') !== 0 && strpos($iconClass, 'fab ') !== 0) {
                                        $iconClass = 'fas ' . $iconClass;
                                    }

                                    // Set default color based on icon type
                                    $iconColor = '#0d6efd'; // Default blue

                                    // Assign colors based on icon class
                                    if (strpos($iconClass, 'fa-music') !== false || strpos($iconClass, 'fa-file-audio') !== false) {
                                        $iconColor = '#6f42c1'; // Purple for audio
                                    } elseif (strpos($iconClass, 'fa-film') !== false || strpos($iconClass, 'fa-file-video') !== false) {
                                        $iconColor = '#fd7e14'; // Orange for video
                                    } elseif (strpos($iconClass, 'fa-image') !== false || strpos($iconClass, 'fa-file-image') !== false) {
                                        $iconColor = '#20c997'; // Green for images
                                    } elseif (strpos($iconClass, 'fa-scroll') !== false) {
                                        $iconColor = '#dc3545'; // Red for ancient documents
                                    }
                                @endphp
                                <i class="{{ $iconClass }}" style="color: {{ $iconColor }};"></i>
                            </div>
                            <h3>{{ $type->name }}</h3>
                            <p>{{ $type->items_count }} รายการ</p>
                        </div>
                    </a>
                </div>
            @empty
                <div class="col-12 text-center py-4">
                    <div style="background-color: #f0f7ff; padding: 30px; border-radius: 8px;">
                        <i class="fas fa-folder-open text-primary mb-3" style="font-size: 2rem;"></i>
                        <h4 class="mt-3">ไม่พบประเภทรายการ</h4>
                        <p class="text-muted">ยังไม่มีการเพิ่มประเภทรายการในระบบ</p>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</section>

<section class="latest-items py-5">
    <div class="container">
        <div class="section-header">
            <h2>รายการล่าสุด</h2>
            <a href="{{ route('items.index') }}" class="view-all btn btn-link">
                ดูทั้งหมด <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>

        <div class="row g-4">
            @foreach ($latestItems as $item)
                <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                    <div class="item-card">
                        <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none">
                            <img src="{{ get_image_url($item->image_path, 'defaults/item-default.svg') }}" alt="{{ $item->title }}" class="item-img">
                            <div class="item-info">
                                <h3>{{ $item->title }}</h3>
                                <p>{{ $item->year }}</p>
                            </div>
                            <div class="item-meta">
                                @if($item->itemType)
                                    <span class="badge bg-secondary"><i class="fas fa-file-alt me-1"></i> {{ $item->itemType->name }}</span>
                                @endif
                                <span class="badge bg-secondary"><i class="far fa-calendar-alt me-1"></i> {{ $item->created_at->format('d/m/Y') }}</span>
                            </div>
                        </a>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Animation for stats numbers
        const statsNumbers = document.querySelectorAll('.stats-number');

        // Intersection Observer to trigger animation when stats are visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Start animation for all stats numbers when they become visible
                    statsNumbers.forEach(animateNumber);
                    // Unobserve after animation starts
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        // Observe the stats section
        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Function to animate a number from 0 to its final value
        function animateNumber(element) {
            const finalValue = parseInt(element.textContent.replace(/,/g, ''));
            const duration = 2000; // Animation duration in milliseconds
            const frameDuration = 1000 / 60; // 60fps
            const totalFrames = Math.round(duration / frameDuration);
            let frame = 0;

            // Start with 0
            let currentNumber = 0;
            element.textContent = '0';

            // Use requestAnimationFrame for smooth animation
            const animate = () => {
                frame++;

                // Calculate progress (0 to 1)
                const progress = frame / totalFrames;

                // Use easeOutQuad for smoother animation
                const easeProgress = 1 - Math.pow(1 - progress, 3);

                // Calculate current number
                currentNumber = Math.floor(easeProgress * finalValue);

                // Format number with commas
                element.textContent = new Intl.NumberFormat().format(currentNumber);

                // Continue animation until complete
                if (frame < totalFrames) {
                    requestAnimationFrame(animate);
                } else {
                    // Ensure final value is set exactly
                    element.textContent = new Intl.NumberFormat().format(finalValue);
                    // Add pulse animation when counting completes
                    element.classList.add('animate');
                    // Remove animation class after animation completes
                    setTimeout(() => {
                        element.classList.remove('animate');
                    }, 500);
                }
            };

            // Start animation
            requestAnimationFrame(animate);
        }

        // Simple hover effects for stats cards
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('hovered');
            });

            card.addEventListener('mouseleave', function() {
                this.classList.remove('hovered');
            });
        });
    });
</script>
@endsection