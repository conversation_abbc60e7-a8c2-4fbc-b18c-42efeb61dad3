@extends('layouts.app')

@section('title')
@if($searchTerm)
ผลการค้นหา "{{ $searchTerm }}" - {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}
@else
ค้นหารายการ - {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}
@endif
@endsection

@section('description')
@if($searchTerm)
ผลการค้นหา "{{ $searchTerm }}" จากคลังข้อมูลดิจิทัล ค้นพบข้อมูลที่เกี่ยวข้องกับคำค้นหาของคุณ
@else
ค้นหารายการจากคลังข้อมูลดิจิทัล ค้นพบข้อมูลมรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญ
@endif
@endsection

@section('keywords')
@if($searchTerm)
{{ $searchTerm }}, ผลการค้นหา, คลังข้อมูลดิจิทัล, ค้นหาข้อมูล, search results
@else
ค้นหา, คลังข้อมูลดิจิทัล, ค้นหาข้อมูล, search, digital collection, find information
@endif
@endsection

@section('canonical', route('search.index', $searchTerm ? ['q' => $searchTerm] : []))

@section('robots')
@if($searchTerm)
noindex, follow
@else
index, follow
@endif
@endsection

@section('og_type', 'website')
@section('og_title')
@if($searchTerm)
ผลการค้นหา "{{ $searchTerm }}" - {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}
@else
ค้นหารายการ - {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}
@endif
@endsection

@section('og_description')
@if($searchTerm)
ผลการค้นหา "{{ $searchTerm }}" จากคลังข้อมูลดิจิทัล ค้นพบข้อมูลที่เกี่ยวข้องกับคำค้นหาของคุณ
@else
ค้นหารายการจากคลังข้อมูลดิจิทัล ค้นพบข้อมูลมรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญ
@endif
@endsection

@section('og_url', route('search.index', $searchTerm ? ['q' => $searchTerm] : []))

@section('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "SearchResultsPage",
    "name": "@if($searchTerm)ผลการค้นหา \"{{ $searchTerm }}\"@elseค้นหารายการ@endif",
    "description": "@if($searchTerm)ผลการค้นหา \"{{ $searchTerm }}\" จากคลังข้อมูลดิจิทัล@elseค้นหารายการจากคลังข้อมูลดิจิทัล@endif",
    "url": "{{ route('search.index', $searchTerm ? ['q' => $searchTerm] : []) }}",
    "isPartOf": {
        "@type": "WebSite",
        "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
        "url": "{{ config('app.url') }}"
    }@if($searchTerm),
    "mainEntity": {
        "@type": "SearchAction",
        "query": "{{ $searchTerm }}",
        "target": "{{ route('search.index') }}?q={{ urlencode($searchTerm) }}"
    }@endif
}
</script>
@endsection

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>ค้นหารายการ</h1>
        <p>ค้นหารายการจากคลังข้อมูลของเรา</p>
    </div>
</div>

<section class="py-4">
    <div class="container">
        @livewire('search-items', ['searchTerm' => $searchTerm])
    </div>
</section>
@endsection
