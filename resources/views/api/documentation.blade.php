@extends('layouts.app')

@section('title', 'API Documentation - คลังข้อมูลดิจิทัล')

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>API Documentation</h1>
        <p>เอกสารสำหรับนักพัฒนาที่ต้องการใช้งาน API ของระบบ</p>
    </div>
</div>

<div class="container py-5">
    <div class="row">
        <div class="col-12">

            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">Overview</h2>
                </div>
                <div class="card-body">
                    <p>This API provides access to the Digital Collections database. All endpoints return data in JSON format.</p>
                    <p>The API is read-only and does not require authentication.</p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h5 mb-0">Endpoints</h2>
                </div>
                <div class="card-body">
                    <h3 class="h6">Get All Items</h3>
                    <pre><code>GET /api/items</code></pre>
                    <p>Returns a list of all items in the collection.</p>

                    <h4 class="mt-4">Example Response:</h4>
                    <pre><code>[
  {
    "id": "CMRU-CT-01-A-0001",
    "title": "ปารมี",
    "alt.title": "",
    "type": "คัมภีร์ใบลาน",
    "subject": "ธรรมทั่วไป",
    "language": "ไทขืน",
    "script": "ไทขึน",
    "author": "วัดอินทบุปผาราม เมืองเชียงตุง",
    "year": "2532",
    "description": null,
    "spatial": "วัดอินทบุปผาราม เมืองเชียงตุง",
    "country": "เมียนมาร์",
    "province": "เชียงตุง",
    "source.uri": "https://example.com/items/12",
    "image": "https://example.com/storage/images/000-DSCF8430(1).jpg"
  },
  ...
]</code></pre>

                    <h3 class="h6 mt-5">Get Item by ID</h3>
                    <pre><code>GET /api/items/{id}</code></pre>
                    <p>Returns detailed information about a specific item.</p>

                    <h4 class="mt-4">Parameters:</h4>
                    <ul>
                        <li><code>id</code> - The item ID</li>
                    </ul>

                    <h4 class="mt-4">Example Response:</h4>
                    <pre><code>{
  "id": "CMRU-CT-01-A-0001",
  "title": "ปารมี",
  "alt.title": "",
  "type": "คัมภีร์ใบลาน",
  "subject": "ธรรมทั่วไป",
  "language": "ไทขืน",
  "script": "ไทขึน",
  "author": "วัดอินทบุปผาราม เมืองเชียงตุง",
  "year": "2532",
  "description": "คัมภีร์ใบลานโบราณจากวัดอินทบุปผาราม",
  "spatial": "วัดอินทบุปผาราม เมืองเชียงตุง",
  "country": "เมียนมาร์",
  "province": "เชียงตุง",
  "source.uri": "https://example.com/items/12",
  "image": "https://example.com/storage/images/000-DSCF8430(1).jpg",
  "images": [
    "https://example.com/storage/images/000-DSCF8430(1).jpg",
    "https://example.com/storage/images/000-DSCF8431.jpg"
  ],
  "files": [
    {
      "name": "ปารมี.pdf",
      "path": "https://example.com/storage/files/ปารมี.pdf",
      "type": "pdf",
      "size": 1024000,
      "is_main": true
    }
  ],
  "created_at": "2023-01-01T00:00:00.000000Z",
  "updated_at": "2023-01-01T00:00:00.000000Z"
}</code></pre>

                    <h3 class="h6 mt-5">Legacy API Endpoint</h3>
                    <pre><code>GET /manuscript-api.php</code></pre>
                    <p>This endpoint is provided for backward compatibility. It returns the same data as the <code>/api/items</code> endpoint.</p>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="h5 mb-0">Example Usage</h2>
                </div>
                <div class="card-body">
                    <h3 class="h6">JavaScript Example</h3>
                    <pre><code>fetch('/api/items')
  .then(response => response.json())
  .then(data => {
    console.log(data);
    // Process the data
  })
  .catch(error => console.error('Error:', error));</code></pre>

                    <h3 class="h6 mt-4">PHP Example</h3>
                    <pre><code>$url = 'https://example.com/api/items';
$response = file_get_contents($url);
$data = json_decode($response, true);

// Process the data
print_r($data);</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
