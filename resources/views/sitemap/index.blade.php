<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">

    <!-- Homepage -->
    <url>
        <loc>{{ route('home') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>

    <!-- Items Index -->
    <url>
        <loc>{{ route('items.index') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>daily</changefreq>
        <priority>0.9</priority>
    </url>

    <!-- Categories Index -->
    <url>
        <loc>{{ route('categories.index') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>

    <!-- Search Page -->
    <url>
        <loc>{{ route('search.index') }}</loc>
        <lastmod>{{ now()->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.7</priority>
    </url>

    <!-- Categories -->
    @foreach($categories as $category)
    <url>
        <loc>{{ route('categories.show', $category->id) }}</loc>
        <lastmod>{{ $category->updated_at ? $category->updated_at->toISOString() : now()->toISOString() }}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
            @if(isset($category->image_path) && $category->image_path)
        <image:image>
            <image:loc>{{ get_image_url($category->image_path) }}</image:loc>
            <image:title>{{ $category->name }}</image:title>
        </image:image>
        @endif
    </url>
    @endforeach

    <!-- Items -->
    @foreach($items as $item)
    <url>
        <loc>{{ route('items.show', $item->id) }}</loc>
        <lastmod>{{ $item->updated_at ? $item->updated_at->toISOString() : now()->toISOString() }}</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.6</priority>
        @if(isset($item->first_image) && $item->first_image)
        <image:image>
            <image:loc>{{ get_image_url($item->first_image) }}</image:loc>
            <image:title>{{ $item->title }}</image:title>
        </image:image>
        @endif
    </url>
    @endforeach

</urlset>
