@extends('layouts.app')

@section('title', 'รายการทั้งหมด - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))

@section('description', 'ค้นพบรายการที่น่าสนใจจากคลังข้อมูลดิจิทัล รวบรวมมรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญต่างๆ')

@section('keywords', 'รายการทั้งหมด, คลังข้อมูลดิจิทัล, มรดกทางวัฒนธรรม, ข้อมูลประวัติศาสตร์, เอกสารสำคัญ, digital collection, cultural heritage')

@section('canonical', route('items.index'))

@section('og_type', 'website')
@section('og_title', 'รายการทั้งหมด - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('og_description', 'ค้นพบรายการที่น่าสนใจจากคลังข้อมูลดิจิทัล รวบรวมมรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญต่างๆ')
@section('og_url', route('items.index'))

@section('twitter_title', 'รายการทั้งหมด - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('twitter_description', 'ค้นพบรายการที่น่าสนใจจากคลังข้อมูลดิจิทัล รวบรวมมรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญต่างๆ')

@section('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "รายการทั้งหมด",
    "description": "ค้นพบรายการที่น่าสนใจจากคลังข้อมูลดิจิทัล รวบรวมมรดกทางวัฒนธรรม ข้อมูลประวัติศาสตร์ และเอกสารสำคัญต่างๆ",
    "url": "{{ route('items.index') }}",
    "isPartOf": {
        "@type": "WebSite",
        "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
        "url": "{{ config('app.url') }}"
    },
    "mainEntity": {
        "@type": "ItemList",
        "name": "รายการในคลังข้อมูลดิจิทัล",
        "description": "รวบรวมมรดกทางวัฒนธรรมและข้อมูลประวัติศาสตร์"
    }
}
</script>
@endsection

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>รายการทั้งหมด</h1>
        <p>ค้นพบรายการที่น่าสนใจจากคลังรายการของเรา</p>
    </div>
</div>

<section class="items py-5">
    <div class="container">
        @livewire('items-list')
    </div>
</section>
@endsection
