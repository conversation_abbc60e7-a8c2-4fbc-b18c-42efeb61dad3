@extends('layouts.app')

@section('title', $item->title . ' - คลังข้อมูลดิจิทัล')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/item-viewer.css') }}">
<style>
    /* Office document viewer styles */
    #office-loading {
        background-color: rgba(255, 255, 255, 0.8);
        padding: 20px;
        border-radius: 8px;
        z-index: 100;
    }

    .item-viewer {
        position: relative;
    }

    /* Video player styles */
    .video-player video {
        width: 100%;
        height: auto;
        max-height: 500px;
        background-color: #000;
        overflow: hidden;
    }

    .ratio-16x9 {
        max-height: 500px;
        background-color: #000;
        overflow: hidden;
    }

    /* Hide scrollbars for video players */
    .video-player::-webkit-scrollbar,
    .ratio-16x9::-webkit-scrollbar {
        display: none;
    }

    .video-player,
    .ratio-16x9 {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }

    /* Modern tab styling */
    .nav-tabs {
        position: relative;
        border-bottom: 1px solid #dee2e6 !important;
    }

    /* Important override for Bootstrap */
    .card-header-tabs.nav-tabs {
        margin-bottom: 0 !important;
    }

    /* Direct style for the card header */
    .card-header {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        padding-top: 0.5rem !important;
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
        border-bottom: none !important;
    }

    /* Add space between tabs and content */
    .tab-content {
        padding-top: 1.25rem !important;
    }

    /* Remove unnecessary pseudo-elements */
    .card-header::after,
    .card-header::before {
        display: none !important;
    }

    /* File section styling */
    .file-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }

    .file-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .file-info {
        flex: 1;
        min-width: 0; /* Ensures text truncation works */
    }

    .file-actions {
        white-space: nowrap;
    }

    .image-preview {
        transition: all 0.3s ease;
    }

    .image-preview:hover {
        transform: scale(1.05);
    }

    /* Improve tab styling */
    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 0;
        border: none;
        border-bottom: 3px solid transparent;
        color: #6c757d;
        transition: all 0.2s ease;
    }

    .nav-tabs .nav-link:hover {
        border-bottom-color: #dee2e6;
        background-color: rgba(0, 0, 0, 0.02);
    }

    .nav-tabs .nav-link.active {
        color: #0d6efd;
        border-bottom-color: #0d6efd;
        background-color: transparent;
    }

    /* Card styling improvements */
    .card {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: none;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .card-body {
        padding: 1.5rem;
    }

    /* List group styling */
    .list-group-item {
        border-left: none;
        border-right: none;
        padding: 1rem;
    }

    .list-group-item:first-child {
        border-top: none;
    }

    .list-group-item:last-child {
        border-bottom: none;
    }

    .nav-tabs .nav-item {
        margin-bottom: 0;
        position: relative;
        z-index: 2;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        font-weight: 500;
        padding: 0.5rem 0.75rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .nav-tabs .nav-link:hover {
        color: #495057;
        background-color: transparent;
        border-color: transparent;
    }

    .nav-tabs .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background-color: transparent;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link.active {
        color: #0d6efd;
        background-color: transparent;
        border-color: transparent;
    }

    .nav-tabs .nav-link.active::after {
        background-color: #0d6efd;
    }

    /* This style is already defined above */

    .tab-pane {
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
</style>
@endsection

@section('content')
<div class="container-md">
    <div class="mb-4 d-flex justify-content-between align-items-center">
        <h1 class="h3 fw-semibold mb-0">{{ $item->title }}</h1>
        <a href="{{ route('items.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i><span class="d-none d-sm-inline">กลับไปยังรายการทั้งหมด</span>
        </a>
    </div>

    <div class="mb-4">
        <div class="d-flex flex-wrap gap-2 mt-2">
            @if($item->year)
                <span class="badge bg-secondary"><i class="fas fa-calendar-alt me-1"></i> {{ $item->year }}</span>
            @endif
            @if($item->category)
                <span class="badge bg-secondary"><i class="fas fa-folder me-1"></i> {{ $item->category->name }}</span>
            @endif
            @if($item->itemType)
                <span class="badge bg-secondary"><i class="fas fa-file-alt me-1"></i> {{ $item->itemType->name }}</span>
            @endif
            @if($item->language)
                <span class="badge bg-secondary"><i class="fas fa-language me-1"></i> {{ $item->language->name }}</span>
            @endif
            @if($item->province_id || $item->province)
                <span class="badge bg-secondary">
                    <i class="fas fa-map-marker-alt me-1"></i> {{ $item->province_name }}
                </span>
            @endif
            <span class="badge bg-secondary"><i class="fas fa-eye me-1"></i> {{ $item->views }}</span>
        </div>
    </div>

    <div class="item-container">
        <div id="item-main" class="item-main">
            <div class="card shadow-sm">
                @php
                    // ตรวจสอบประเภทของรายการจากเมธอด getFileType ที่ปรับปรุงแล้ว
                    $fileType = $item->getFileType();

                    // ตรวจสอบไฟล์หลัก
                    $mainFilePath = $item->main_file;

                    // ตรวจสอบว่าเป็นไฟล์ default หรือไม่
                    $isDefaultFile = $mainFilePath === 'defaults/no-file-available.svg';

                    // ถ้าเป็นไฟล์ default ให้แสดงเป็นรูปภาพ
                    if ($isDefaultFile) {
                        $fileType = 'image';
                    }
                    // ตรวจสอบว่าเป็น YouTube URL หรือไม่
                    elseif ($mainFilePath && (strpos($mainFilePath, 'youtube.com') !== false || strpos($mainFilePath, 'youtu.be') !== false)) {
                        $fileType = 'youtube';
                    }
                @endphp

                {{-- แสดงเครื่องมือตามประเภทของไฟล์ --}}
                @if ($fileType === 'image')
                    {{-- เครื่องมือสำหรับดูภาพ --}}
                    <div class="item-viewer">
                        <div class="viewer-toolbar bg-light p-2 d-flex justify-content-between">
                            <div class="toolbar-left d-flex gap-2">
                                <button class="btn btn-sm btn-outline-secondary" id="zoom-in" title="ขยาย"><i class="fas fa-search-plus"></i></button>
                                <button class="btn btn-sm btn-outline-secondary" id="zoom-out" title="ย่อ"><i class="fas fa-search-minus"></i></button>
                                <button class="btn btn-sm btn-outline-secondary" id="reset-zoom" title="คืนค่า"><i class="fas fa-sync-alt"></i></button>
                            </div>
                            <div class="toolbar-right d-flex gap-2">
                                <button class="btn btn-sm btn-outline-secondary" id="rotate-left" title="หมุนซ้าย"><i class="fas fa-undo"></i></button>
                                <button class="btn btn-sm btn-outline-secondary" id="rotate-right" title="หมุนขวา"><i class="fas fa-redo"></i></button>
                                <button class="btn btn-sm btn-outline-secondary" id="sidebar-toggle" title="เปิด/ปิดรายละเอียด"><i class="fas fa-info-circle"></i></button>
                                <button class="btn btn-sm btn-outline-secondary" id="fullscreen" title="เต็มหน้าจอ"><i class="fas fa-expand"></i></button>
                            </div>
                        </div>

                        <div class="main-image-container bg-light d-flex justify-content-center align-items-center">
                            <div class="main-image" id="image-viewer">
                                @php
                                    // ถ้าเป็นไฟล์ default ให้ใช้ไฟล์ default แทน
                                    if ($isDefaultFile) {
                                        $imagePath = $mainFilePath;
                                    } else {
                                        $imagePath = $item->first_image;
                                    }

                                    // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                                    $imageExists = false;
                                    if (!empty($imagePath)) {
                                        if (str_starts_with($imagePath, 'defaults/')) {
                                            $imageExists = true;
                                        } else {
                                            $normalizedPath = normalize_storage_path($imagePath, 'images');
                                            $fullPath = public_path($normalizedPath['urlPath']);
                                            $imageExists = @file_exists($fullPath);
                                        }
                                    }

                                    // กำหนดภาพที่จะแสดง
                                    if (!$imageExists) {
                                        $displayImage = asset('images/defaults/missing-image.svg');
                                    } else {
                                        $displayImage = get_image_url($imagePath, 'defaults/item-default.svg');
                                    }
                                @endphp
                                <img src="{{ $displayImage }}?v={{ time() }}" alt="{{ $item->title }}" id="item-image" class="img-fluid" style="transition: transform 0.3s ease; cursor: move;">
                            </div>
                        </div>

                        <div class="viewer-pagination bg-light p-2 d-flex justify-content-between align-items-center">
                            <button class="btn btn-sm btn-outline-secondary" id="prev-page" disabled><i class="fas fa-chevron-left me-1"></i> หน้าก่อน</button>
                            <span class="page-info">หน้า <span id="current-page">1</span> จาก <span id="total-pages">1</span></span>
                            <button class="btn btn-sm btn-outline-secondary" id="next-page" disabled>หน้าถัดไป <i class="fas fa-chevron-right ms-1"></i></button>
                        </div>
                    </div>

                    <div id="thumbnails-container" class="thumbnails d-flex gap-2 p-2 bg-white" style="cursor: grab;">
                        @if($item->images->isNotEmpty())
                            @foreach($item->images as $index => $image)
                                <div class="thumbnail {{ $index === 0 ? 'active' : '' }}" data-page="{{ $index + 1 }}" style="width: 80px; height: 60px; border: 2px solid {{ $index === 0 ? '#0d6efd' : 'transparent' }}; border-radius: 4px; overflow: hidden; cursor: pointer; display: inline-block; flex-shrink: 0; z-index: 10;">
                                    @php
                                        // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                                        $thumbExists = false;
                                        if (!empty($image->image_path)) {
                                            $normalizedPath = normalize_storage_path($image->image_path, 'images');
                                            $fullPath = public_path($normalizedPath['urlPath']);
                                            $thumbExists = @file_exists($fullPath);
                                        }

                                        // กำหนดภาพที่จะแสดง
                                        if (!$thumbExists) {
                                            $thumbImage = asset('images/defaults/missing-image.svg');
                                        } else {
                                            $thumbImage = get_image_url($image->image_path, 'defaults/item-default.svg');
                                        }
                                    @endphp
                                    <img src="{{ $thumbImage }}" alt="Thumbnail {{ $index + 1 }}" style="width: 100%; height: 100%; object-fit: cover; pointer-events: none;">
                                </div>
                            @endforeach
                        @else
                            <div class="thumbnail active" data-page="1" style="width: 80px; height: 60px; border: 2px solid #0d6efd; border-radius: 4px; overflow: hidden; cursor: pointer; display: inline-block; flex-shrink: 0; z-index: 10;">
                                @php
                                    // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                                    $thumbExists = false;
                                    if (!empty($item->image_path)) {
                                        $normalizedPath = normalize_storage_path($item->image_path, 'images');
                                        $fullPath = public_path($normalizedPath['urlPath']);
                                        $thumbExists = @file_exists($fullPath);
                                    }

                                    // กำหนดภาพที่จะแสดง
                                    if (!$thumbExists) {
                                        $thumbImage = asset('images/defaults/missing-image.svg');
                                    } else {
                                        $thumbImage = get_image_url($item->image_path, 'defaults/item-default.svg');
                                    }
                                @endphp
                                <img src="{{ $thumbImage }}" alt="Thumbnail 1" style="width: 100%; height: 100%; object-fit: cover; pointer-events: none;">
                            </div>
                        @endif
                    </div>
                @elseif ($fileType === 'audio')
                    {{-- เครื่องมือสำหรับไฟล์เสียง --}}
                    <div class="card-body p-4">
                        <h5 class="card-title mb-3"><i class="fas fa-music me-2"></i>ไฟล์เสียง: {{ $item->title }}</h5>
                        @if ($mainFilePath && !str_starts_with($mainFilePath, 'defaults/'))
                            <div class="audio-player bg-light p-3 rounded mb-3">
                                <audio controls class="w-100">
                                    <source src="{{ url($mainFilePath) }}" type="audio/mpeg">
                                    <source src="{{ url($mainFilePath) }}" type="audio/wav">
                                    <source src="{{ url($mainFilePath) }}" type="audio/ogg">
                                    เบราว์เซอร์ของคุณไม่รองรับการเล่นไฟล์เสียงนี้
                                </audio>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> ไม่มีไฟล์เสียงในระบบ
                            </div>
                        @endif
                        <div class="card-text">
                            <p>{{ $item->description }}</p>
                        </div>
                    </div>

                @elseif ($fileType === 'document')
                    {{-- เครื่องมือสำหรับไฟล์รายการ --}}
                    <div class="card-body p-4">
                        <h5 class="card-title mb-3"><i class="fas fa-file-alt me-2"></i>ไฟล์รายการ: {{ $item->title }}</h5>
                        @if ($mainFilePath && !str_starts_with($mainFilePath, 'defaults/'))
                            <div class="item-viewer bg-light p-3 rounded mb-3">
                                <div class="ratio ratio-16x9" style="min-height: 500px;">
                                    @php
                                        $fileExtension = strtolower(pathinfo($mainFilePath, PATHINFO_EXTENSION));
                                        $isOfficeFile = in_array($fileExtension, ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']);
                                        $fileUrl = url($mainFilePath);
                                        $encodedFileUrl = urlencode($fileUrl);
                                    @endphp

                                    @if ($isOfficeFile)
                                        {{-- ใช้ Microsoft Office Online Viewer สำหรับไฟล์ Office --}}
                                        <iframe id="office-viewer"
                                                src="https://view.officeapps.live.com/op/embed.aspx?src={{ $encodedFileUrl }}"
                                                width="100%" height="100%" frameborder="0" allowfullscreen>
                                        </iframe>
                                        <div id="office-loading" class="position-absolute top-50 start-50 translate-middle text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">กำลังโหลด...</span>
                                            </div>
                                            <p class="mt-2">กำลังโหลดเอกสาร...</p>
                                        </div>
                                    @elseif ($fileExtension === 'pdf')
                                        {{-- ใช้ PDF Viewer ปกติสำหรับไฟล์ PDF และมี Google Docs Viewer เป็น fallback --}}
                                        <iframe id="pdf-viewer" src="{{ $fileUrl }}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>
                                        <div id="pdf-fallback" style="display: none;">
                                            <iframe src="https://docs.google.com/viewer?url={{ $encodedFileUrl }}&embedded=true"
                                                    width="100%" height="100%" frameborder="0" allowfullscreen>
                                            </iframe>
                                        </div>
                                    @else
                                        {{-- สำหรับไฟล์ประเภทอื่นๆ --}}
                                        <iframe src="{{ $fileUrl }}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>
                                    @endif
                                </div>
                            </div>
                            <div class="card-text">
                                <p>{{ $item->description }}</p>
                            </div>
                            @if ($isOfficeFile)
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle me-2"></i> หากไม่สามารถแสดงเอกสารได้ กรุณาใช้ปุ่มดาวน์โหลดด้านล่างเพื่อเปิดไฟล์ในโปรแกรม Microsoft Office
                            </div>
                            @endif
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> ไม่มีไฟล์เอกสารในระบบ
                            </div>
                            <div class="card-text">
                                <p>{{ $item->description }}</p>
                            </div>
                        @endif
                    </div>
                @elseif ($fileType === 'youtube')
                    {{-- เครื่องมือสำหรับลิงค์ YouTube --}}
                    <div class="card-body p-4">
                        <h5 class="card-title mb-3"><i class="fab fa-youtube me-2"></i>YouTube Video: {{ $item->title }}</h5>
                        <div class="youtube-player bg-light p-3 rounded mb-3">
                            @php
                                // Extract YouTube video ID
                                $youtubeUrl = $mainFilePath;
                                $videoId = null;

                                if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/', $youtubeUrl, $match)) {
                                    $videoId = $match[1];
                                }
                            @endphp

                            @if($videoId)
                                <div class="ratio ratio-16x9">
                                    <iframe src="https://www.youtube.com/embed/{{ $videoId }}"
                                        title="{{ $item->title }}"
                                        frameborder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                        allowfullscreen>
                                    </iframe>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    Invalid YouTube URL format. Please check the URL.
                                </div>
                            @endif
                        </div>
                        <div class="card-text">
                            <p>{{ $item->description }}</p>
                        </div>
                    </div>
                @elseif ($fileType === 'video')
                    {{-- เครื่องมือสำหรับไฟล์วิดีโอ --}}
                    <div class="card-body p-4">
                        <h5 class="card-title mb-3"><i class="fas fa-video me-2"></i>ไฟล์วิดีโอ: {{ $item->title }}</h5>
                        @if ($mainFilePath && !str_starts_with($mainFilePath, 'defaults/'))
                            <div class="video-player bg-light p-3 rounded mb-3">
                                <div class="ratio ratio-16x9" style="max-height: 500px;">
                                    <video controls controlsList="nodownload nofullscreen noremoteplayback">
                                        <source src="{{ url($mainFilePath) }}" type="video/mp4">
                                        <source src="{{ url($mainFilePath) }}" type="video/webm">
                                        <source src="{{ url($mainFilePath) }}" type="video/ogg">
                                        เบราว์เซอร์ของคุณไม่รองรับการเล่นไฟล์วิดีโอนี้
                                    </video>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> ไม่มีไฟล์วิดีโอในระบบ
                            </div>
                        @endif
                        <div class="card-text">
                            <p>{{ $item->description }}</p>
                        </div>
                    </div>
                @elseif ($fileType === 'pdf')
                    {{-- เครื่องมือสำหรับไฟล์ PDF --}}
                    <div class="card-body p-4">
                        <h5 class="card-title mb-3"><i class="fas fa-file-pdf me-2"></i>ไฟล์รายการ: {{ $item->title }}</h5>
                        @if ($mainFilePath && !str_starts_with($mainFilePath, 'defaults/'))
                            <div class="pdf-viewer bg-light p-3 rounded mb-3" style="height: 500px;">
                                <iframe src="{{ url($mainFilePath) }}" class="w-100 h-100" frameborder="0"></iframe>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> ไม่มีไฟล์ PDF ในระบบ
                            </div>
                        @endif
                        <div class="card-text">
                            <p>{{ $item->description }}</p>
                        </div>
                    </div>
                @endif

                @if ($mainFilePath && !str_starts_with($mainFilePath, 'http') && !str_starts_with($mainFilePath, 'defaults/'))
                    <div class="card-footer bg-white p-3 text-center">
                        <a href="{{ url($mainFilePath) }}" class="btn btn-primary" download>
                            <i class="fas fa-download me-2"></i>ดาวน์โหลดไฟล์
                        </a>
                    </div>
                @endif
            </div>

        </div>
        <div id="item-sidebar" class="item-sidebar">
            <div class="card shadow-sm" style="overflow: hidden;">
                <div class="card-header bg-white" style="border-bottom: none;">
                    <ul class="nav nav-tabs card-header-tabs" id="itemTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                                <i class="fas fa-info-circle me-1"></i>รายละเอียด
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="metadata-tab" data-bs-toggle="tab" data-bs-target="#metadata" type="button" role="tab" aria-controls="metadata" aria-selected="false">
                                <i class="fas fa-database me-1"></i>ข้อมูลเมตา
                            </button>
                        </li>
                        @if($item->remark)
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">
                                <i class="fas fa-note-sticky me-1"></i>หมายเหตุ
                            </button>
                        </li>
                        @endif
                        @if(count($itemFiles['pdf']) > 0 || count($itemFiles['image']) > 0 || count($itemFiles['audio']) > 0 || count($itemFiles['video']) > 0 || count($itemFiles['other']) > 0)
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab" aria-controls="files" aria-selected="false">
                                <i class="fas fa-file-alt me-1"></i>ไฟล์รายการ
                            </button>
                        </li>
                        @endif
                        @if($item->latitude && $item->longitude)
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="map-tab" data-bs-toggle="tab" data-bs-target="#map" type="button" role="tab" aria-controls="map" aria-selected="false">
                                <i class="fas fa-map-marker-alt me-1"></i>แผนที่
                            </button>
                        </li>
                        @endif
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="itemTabsContent">
                        <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                            <h5 class="card-title mb-2">รายละเอียด</h5>
                            <p class="card-text">{{ $item->description }}</p>
                        </div>

                        <div class="tab-pane fade" id="metadata" role="tabpanel" aria-labelledby="metadata-tab">
                            <h5 class="card-title mb-3">ข้อมูลเมตา</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-borderless">
                                    <tbody>
                                        @if($item->identifier_no)
                                        <tr>
                                            <th scope="row" style="width: 40%;">รหัสรายการ</th>
                                            <td>{{ $item->identifier_no }}</td>
                                        </tr>
                                        @endif
                                        @if($item->category_id)
                                        <tr>
                                            <th scope="row">หมวดหมู่</th>
                                            <td>{{ $item->category ? $item->category->name  : '' }}</td>
                                        </tr>
                                        @endif
                                        @if($item->material_id)
                                        <tr>
                                            <th scope="row">วัสดุ</th>
                                            <td>{{ $material ? $material->name : '' }}</td>
                                        </tr>
                                        @endif
                                        @if($item->language_id)
                                        <tr>
                                            <th scope="row">ภาษา</th>
                                            <td>{{ $language ? $language->name : '' }}</td>
                                        </tr>
                                        @endif
                                        @if($item->script_id)
                                        <tr>
                                            <th scope="row">ตัวอักษร</th>
                                            <td>{{ $script ? $script->name : '' }}</td>
                                        </tr>
                                        @endif
                                        @if($item->item_type_id)
                                        <tr>
                                            <th scope="row">ประเภทรายการ</th>
                                            <td>{{ $itemType ? $itemType->name : '' }}</td>
                                        </tr>
                                        @endif
                                        @if($item->creator)
                                        <tr>
                                            <th scope="row">ผู้สร้าง</th>
                                            <td>{{ $item->creator }}</td>
                                        </tr>
                                        @endif
                                        @if($item->author)
                                        <tr>
                                            <th scope="row">ผู้แต่ง</th>
                                            <td>{{ $item->author }}</td>
                                        </tr>
                                        @endif
                                        @if($item->location)
                                        <tr>
                                            <th scope="row">สถานที่</th>
                                            <td>{{ $item->location }}</td>
                                        </tr>
                                        @endif
                                        @if($item->country && $item->countryRelation)
                                        <tr>
                                            <th scope="row">ประเทศ</th>
                                            <td>{{ $item->countryRelation->name }}</td>
                                        </tr>
                                        @endif
                                        @if($item->province_id || $item->province)
                                        <tr>
                                            <th scope="row">จังหวัด</th>
                                            <td>{{ $item->province_name }}</td>
                                        </tr>
                                        @endif
                                        @if($item->manuscript_condition)
                                        <tr>
                                            <th scope="row">สภาพ</th>
                                            <td>{{ $item->manuscript_condition }}</td>
                                        </tr>
                                        @endif

                                        <tr>
                                            <th scope="row">วันที่อัพโหลด</th>
                                            <td>{{ $item->created_at->format('d/m/Y') }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        @if($item->remark)
                        <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                            <h5 class="card-title mb-3">หมายเหตุ</h5>
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <p class="card-text">{{ $item->remark }}</p>
                                </div>
                            </div>
                        </div>
                        @endif

                        @if(count($itemFiles['pdf']) > 0 || count($itemFiles['image']) > 0 || count($itemFiles['audio']) > 0 || count($itemFiles['video']) > 0 || count($itemFiles['other']) > 0)
                        <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                            <h5 class="card-title mb-3">ไฟล์รายการ</h5>

                            @if(count($itemFiles['pdf']) > 0)
                            <div class="file-section mb-4">
                                <h6 class="file-section-title"><i class="fas fa-file-pdf text-danger me-2"></i>ไฟล์ PDF</h6>
                                <div class="list-group">
                                    @foreach($itemFiles['pdf'] as $file)
                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div class="file-info">
                                            <div class="file-name">{{ $file->file_name ?? basename($file->file_path) }}</div>
                                            <small class="text-muted">{{ strtoupper(pathinfo($file->file_path, PATHINFO_EXTENSION)) }}</small>
                                        </div>
                                        <div class="file-actions">
                                            @php
                                                $fileExists = false;
                                                if (!empty($file->file_path)) {
                                                    $normalizedPath = normalize_storage_path($file->file_path, 'files');
                                                    $fullPath = public_path($normalizedPath['urlPath']);
                                                    $fileExists = @file_exists($fullPath);
                                                }
                                            @endphp
                                            @if($fileExists)
                                            <a href="{{ url($file->file_path) }}" class="btn btn-sm btn-outline-primary me-1" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url($file->file_path) }}" class="btn btn-sm btn-outline-success" download>
                                                <i class="fas fa-download"></i>
                                            </a>
                                            @else
                                            <button class="btn btn-sm btn-outline-secondary me-1" disabled>
                                                <i class="fas fa-eye-slash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-times"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            @if(count($itemFiles['image']) > 0)
                            <div class="file-section mb-4">
                                <h6 class="file-section-title"><i class="fas fa-image text-primary me-2"></i>รูปภาพ</h6>
                                <div class="row g-3">
                                    @foreach($itemFiles['image'] as $file)
                                    <div class="col-6 col-md-4 col-lg-3">
                                        <div class="card h-100">
                                            <div class="card-img-top image-preview" style="height: 120px; background-image: url('{{ get_image_url($file->file_path, 'defaults/missing-image.svg') }}'); background-size: cover; background-position: center;"></div>
                                            <div class="card-body p-2">
                                                <p class="card-text small text-truncate">{{ $file->file_name ?? basename($file->file_path) }}</p>
                                                <div class="btn-group btn-group-sm w-100">
                                                    <a href="{{ get_image_url($file->file_path, 'defaults/missing-image.svg') }}" class="btn btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if(file_exists(public_path(normalize_storage_path($file->file_path, 'images')['urlPath'])))
                                                    <a href="{{ url($file->file_path) }}" class="btn btn-outline-success" download>
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    @else
                                                    <button class="btn btn-outline-secondary" disabled>
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            @if(count($itemFiles['audio']) > 0)
                            <div class="file-section mb-4">
                                <h6 class="file-section-title"><i class="fas fa-music text-warning me-2"></i>ไฟล์เสียง</h6>
                                <div class="list-group">
                                    @foreach($itemFiles['audio'] as $file)
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="file-info">
                                                <div class="file-name">{{ $file->file_name ?? basename($file->file_path) }}</div>
                                                <small class="text-muted">{{ strtoupper(pathinfo($file->file_path, PATHINFO_EXTENSION)) }}</small>
                                            </div>
                                            <div class="file-actions">
                                                @php
                                                    $fileExists = false;
                                                    if (!empty($file->file_path)) {
                                                        $normalizedPath = normalize_storage_path($file->file_path, 'files');
                                                        $fullPath = public_path($normalizedPath['urlPath']);
                                                        $fileExists = @file_exists($fullPath);
                                                    }
                                                @endphp
                                                @if($fileExists)
                                                <a href="{{ url($file->file_path) }}" class="btn btn-sm btn-outline-success" download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                @else
                                                <button class="btn btn-sm btn-outline-secondary" disabled>
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                @endif
                                            </div>
                                        </div>
                                        <audio controls class="w-100">
                                            @if($fileExists)
                                            <source src="{{ url($file->file_path) }}" type="audio/mpeg">
                                            เบราว์เซอร์ของคุณไม่รองรับการเล่นไฟล์เสียงนี้
                                            @else
                                            <div class="alert alert-warning mt-2">
                                                <i class="fas fa-exclamation-triangle me-2"></i> ไฟล์เสียงไม่พบในระบบ
                                            </div>
                                            @endif
                                        </audio>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            @if(count($itemFiles['video']) > 0)
                            <div class="file-section mb-4">
                                <h6 class="file-section-title"><i class="fas fa-video text-info me-2"></i>ไฟล์วิดีโอ</h6>
                                <div class="list-group">
                                    @foreach($itemFiles['video'] as $file)
                                    <div class="list-group-item">
                                        @php
                                            $fileExists = false;
                                            if (!empty($file->file_path)) {
                                                $normalizedPath = normalize_storage_path($file->file_path, 'files');
                                                $fullPath = public_path($normalizedPath['urlPath']);
                                                $fileExists = @file_exists($fullPath);
                                            }
                                        @endphp

                                        @if($fileExists)
                                        <div class="ratio ratio-16x9" style="max-height: 400px;">
                                            <video controls controlsList="nodownload nofullscreen noremoteplayback">
                                                <source src="{{ url($file->file_path) }}" type="video/mp4">
                                                เบราว์เซอร์ของคุณไม่รองรับการเล่นไฟล์วิดีโอนี้
                                            </video>
                                        </div>
                                        <div class="d-flex justify-content-end mt-2">
                                            <a href="{{ url($file->file_path) }}" class="btn btn-sm btn-outline-success" download>
                                                <i class="fas fa-download me-1"></i>ดาวน์โหลด
                                            </a>
                                        </div>
                                        @else
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i> ไฟล์วิดีโอไม่พบในระบบ
                                        </div>
                                        <div class="d-flex justify-content-end mt-2">
                                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-times me-1"></i>ไม่พบไฟล์
                                            </button>
                                        </div>
                                        @endif
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            @if(count($itemFiles['other']) > 0)
                            <div class="file-section mb-4">
                                <h6 class="file-section-title"><i class="fas fa-file-alt text-secondary me-2"></i>ไฟล์อื่นๆ</h6>
                                <div class="list-group">
                                    @foreach($itemFiles['other'] as $file)
                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div class="file-info">
                                            <div class="file-name">{{ $file->file_name ?? basename($file->file_path) }}</div>
                                            <small class="text-muted">{{ strtoupper(pathinfo($file->file_path, PATHINFO_EXTENSION)) }}</small>
                                        </div>
                                        <div class="file-actions">
                                            @php
                                                $fileExists = false;
                                                if (!empty($file->file_path)) {
                                                    $normalizedPath = normalize_storage_path($file->file_path, 'files');
                                                    $fullPath = public_path($normalizedPath['urlPath']);
                                                    $fileExists = @file_exists($fullPath);
                                                }
                                            @endphp
                                            @if($fileExists)
                                            <a href="{{ url($file->file_path) }}" class="btn btn-sm btn-outline-success" download>
                                                <i class="fas fa-download"></i>
                                            </a>
                                            @else
                                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-times"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                        @endif

                        @if($item->latitude && $item->longitude)
                        <div class="tab-pane fade" id="map" role="tabpanel" aria-labelledby="map-tab">
                            <h5 class="card-title mb-3">แผนที่ตำแหน่ง</h5>
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    @if($item->location)
                                    <p class="mb-1"><strong>สถานที่:</strong> {{ $item->location }}</p>
                                    @endif
                                    @if($item->country && $item->countryRelation)
                                    <p class="mb-1"><strong>ประเทศ:</strong> {{ $item->countryRelation->name }}</p>
                                    @endif
                                    @if($item->province_id || $item->province)
                                    <p class="mb-1"><strong>จังหวัด:</strong> {{ $item->province_name }}</p>
                                    @endif
                                    <p class="mb-0"><strong>พิกัด:</strong> {{ $item->latitude }}, {{ $item->longitude }}</p>
                                </div>
                            </div>
                            <div id="item-map" class="map-container rounded" style="height: 400px; width: 100%;"></div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- เพิ่ม Leaflet CSS และ JavaScript สำหรับแผนที่ -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<!-- JavaScript สำหรับ Document Viewer -->
<script>
    // ตรวจสอบว่ามี Office Viewer หรือไม่
    document.addEventListener('DOMContentLoaded', function() {
        // สำหรับ Office Viewer
        const officeViewer = document.getElementById('office-viewer');
        const officeLoading = document.getElementById('office-loading');

        if (officeViewer && officeLoading) {
            // ซ่อนตัวโหลดเมื่อโหลดเสร็จ
            officeViewer.onload = function() {
                setTimeout(function() {
                    officeLoading.style.display = 'none';
                }, 1000); // รอ 1 วินาทีเพื่อให้แน่ใจว่าโหลดเสร็จจริงๆ
            };

            // ตั้งเวลาสำหรับกรณีที่โหลดไม่สำเร็จ
            setTimeout(function() {
                if (officeLoading.style.display !== 'none') {
                    officeLoading.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>การโหลดเอกสารใช้เวลานานกว่าปกติ กรุณารอสักครู่...</div>';
                }
            }, 10000); // 10 วินาที

            // ตั้งเวลาสำหรับกรณีที่โหลดไม่สำเร็จเป็นเวลานาน
            setTimeout(function() {
                if (officeLoading.style.display !== 'none') {
                    officeLoading.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>ไม่สามารถโหลดเอกสารได้ กรุณาใช้ปุ่มดาวน์โหลดด้านล่างเพื่อเปิดไฟล์ในโปรแกรม Microsoft Office</div>';
                }
            }, 30000); // 30 วินาที
        }

        // สำหรับ PDF Viewer
        const pdfViewer = document.getElementById('pdf-viewer');
        const pdfFallback = document.getElementById('pdf-fallback');

        if (pdfViewer && pdfFallback) {
            // ตรวจสอบการโหลด PDF ไม่สำเร็จ
            pdfViewer.onerror = function() {
                console.log('PDF viewer error, switching to fallback');
                pdfViewer.style.display = 'none';
                pdfFallback.style.display = 'block';
            };

            // ตั้งเวลาสำหรับกรณีที่ PDF ไม่สามารถโหลดได้
            setTimeout(function() {
                // ตรวจสอบว่า PDF โหลดสำเร็จหรือไม่
                try {
                    // ถ้าไม่สามารถเข้าถึงเนื้อหาของ iframe ให้ใช้ fallback
                    if (pdfViewer.contentDocument === null || pdfViewer.contentDocument.body.innerHTML === '') {
                        console.log('PDF viewer empty, switching to fallback');
                        pdfViewer.style.display = 'none';
                        pdfFallback.style.display = 'block';
                    }
                } catch (e) {
                    // ถ้ามีข้อผิดพลาดในการเข้าถึง contentDocument (เช่น cross-origin) ให้ใช้ fallback
                    console.log('PDF viewer access error, switching to fallback', e);
                    pdfViewer.style.display = 'none';
                    pdfFallback.style.display = 'block';
                }
            }, 5000); // 5 วินาที
        }
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Bootstrap จัดการแท็บให้อัตโนมัติแล้ว

        // เพิ่มการทำงานของแท็บแผนที่
        const mapTab = document.getElementById('map-tab');
        if (mapTab) {
            mapTab.addEventListener('shown.bs.tab', function (e) {
                // สร้างแผนที่เมื่อแท็บแผนที่ถูกแสดง
                setTimeout(() => {
                    createMap();
                }, 100);
            });
        }
        // ตัวแปรสำหรับการจัดการภาพ
        let scale = 1;
        let rotation = 0;
        let currentPage = 1;
        let imageIsDragging = false;
        let startX, startY, translateX = 0, translateY = 0;
        const totalPages = document.querySelectorAll('.thumbnail').length;
        const documentImage = document.getElementById('item-image');
        const imageViewer = document.getElementById('image-viewer');
        const currentPageEl = document.getElementById('current-page');
        const totalPagesEl = document.getElementById('total-pages');
        const prevPageBtn = document.getElementById('prev-page');
        const nextPageBtn = document.getElementById('next-page');
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImageContainer = document.querySelector('.main-image-container');

        // อัปเดตจำนวนหน้าทั้งหมด
        if (totalPagesEl) {
            totalPagesEl.textContent = totalPages;
        }

        // ตรวจสอบว่ามีหน้ามากกว่า 1 หน้าหรือไม่
        if (totalPages > 1) {
            nextPageBtn.disabled = false;
        }

        // Drag scrolling for thumbnails container
        const thumbnailsContainer = document.getElementById('thumbnails-container');
        if (thumbnailsContainer) {
            let isDown = false;
            let startX;
            let scrollLeft;

            // Variables to track drag vs click
            let isDragging = false;
            let dragStartTime = 0;
            let dragDistance = 0;
            let clickedThumbnail = null;

            // Function to handle the start of dragging
            const handleDragStart = (clientX, target) => {
                isDown = true;
                isDragging = false;
                dragStartTime = Date.now();
                dragDistance = 0;
                thumbnailsContainer.classList.add('dragging');
                startX = clientX - thumbnailsContainer.offsetLeft;
                scrollLeft = thumbnailsContainer.scrollLeft;

                // Store the clicked thumbnail if any
                clickedThumbnail = target.closest('.thumbnail');
            };

            thumbnailsContainer.addEventListener('mousedown', (e) => {
                handleDragStart(e.pageX, e.target);
            });

            // Touch support for mobile devices
            thumbnailsContainer.addEventListener('touchstart', (e) => {
                handleDragStart(e.touches[0].pageX, e.target);
                // Prevent default to avoid scrolling the page on mobile
                e.preventDefault();
            });

            thumbnailsContainer.addEventListener('mouseleave', () => {
                isDown = false;
                thumbnailsContainer.classList.remove('dragging');
            });

            // Function to handle the end of dragging
            const handleDragEnd = () => {
                isDown = false;
                thumbnailsContainer.classList.remove('dragging');
                thumbnailsContainer.style.cursor = 'grab';

                // Reset pointer events on thumbnails
                if (clickedThumbnail) {
                    clickedThumbnail.style.pointerEvents = 'auto';
                }

                // Reset drag state immediately if the distance is very small (likely a click)
                if (dragDistance < 5) {
                    isDragging = false;
                    dragDistance = 0;
                    clickedThumbnail = null;
                } else {
                    // For actual drags, reset after a short delay
                    setTimeout(() => {
                        isDragging = false;
                        dragDistance = 0;
                        clickedThumbnail = null;
                    }, 50);
                }
            };

            thumbnailsContainer.addEventListener('mouseup', handleDragEnd);
            thumbnailsContainer.addEventListener('mouseleave', handleDragEnd);
            thumbnailsContainer.addEventListener('touchend', handleDragEnd);
            thumbnailsContainer.addEventListener('touchcancel', handleDragEnd);

            // Function to handle drag movement
            const handleDragMove = (clientX) => {
                if (!isDown) return;

                const x = clientX - thumbnailsContainer.offsetLeft;
                const walk = (x - startX) * 2; // Scroll speed multiplier
                const currentDragDistance = Math.abs(walk);

                // Only consider it a drag if we've moved a significant distance
                if (currentDragDistance > 5) {
                    // Update total drag distance
                    dragDistance += currentDragDistance;

                    // Mark as dragging
                    isDragging = true;

                    // Apply the grabbing cursor to the entire container
                    thumbnailsContainer.style.cursor = 'grabbing';

                    // If we have a clicked thumbnail, make it non-clickable during drag
                    if (clickedThumbnail) {
                        clickedThumbnail.style.pointerEvents = 'none';
                    }

                    // Scroll the container
                    thumbnailsContainer.scrollLeft = scrollLeft - walk;
                }
            };

            thumbnailsContainer.addEventListener('mousemove', (e) => {
                e.preventDefault(); // Prevent text selection during drag
                handleDragMove(e.pageX);
            });

            thumbnailsContainer.addEventListener('touchmove', (e) => {
                e.preventDefault(); // Prevent page scrolling during drag
                handleDragMove(e.touches[0].pageX);
            });
        }

        // ปุ่มซูมเข้า
        document.getElementById('zoom-in').addEventListener('click', function() {
            scale += 0.1;
            updateImageTransform();
        });

        // ปุ่มซูมออก
        document.getElementById('zoom-out').addEventListener('click', function() {
            if (scale > 0.5) {
                scale -= 0.1;
                updateImageTransform();
            }
        });

        // ปุ่มรีเซ็ตซูม
        document.getElementById('reset-zoom').addEventListener('click', function() {
            scale = 1;
            rotation = 0;
            translateX = 0;
            translateY = 0;
            updateImageTransform();
        });

        // Mouse wheel zoom
        if (mainImageContainer) {
            mainImageContainer.addEventListener('wheel', function(e) {
                e.preventDefault(); // Prevent page scrolling

                // Determine zoom direction
                const delta = e.deltaY || e.detail || e.wheelDelta;

                if (delta > 0) {
                    // Zoom out
                    if (scale > 0.5) {
                        scale -= 0.1;
                    }
                } else {
                    // Zoom in
                    scale += 0.1;
                }

                updateImageTransform();
            });
        }

        // ปุ่มหมุนซ้าย
        document.getElementById('rotate-left').addEventListener('click', function() {
            rotation -= 90;
            updateImageTransform();
        });

        // ปุ่มหมุนขวา
        document.getElementById('rotate-right').addEventListener('click', function() {
            rotation += 90;
            updateImageTransform();
        });

        // ปุ่มเต็มหน้าจอ
        document.getElementById('fullscreen').addEventListener('click', function() {
            if (imageViewer.requestFullscreen) {
                imageViewer.requestFullscreen();
            } else if (imageViewer.webkitRequestFullscreen) {
                imageViewer.webkitRequestFullscreen();
            } else if (imageViewer.msRequestFullscreen) {
                imageViewer.msRequestFullscreen();
            }
        });

        // ปุ่มหน้าก่อนหน้า
        if (prevPageBtn) {
            prevPageBtn.addEventListener('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    updatePage();
                    // รีเซ็ตการซูมและหมุน
                    scale = 1;
                    rotation = 0;
                    translateX = 0;
                    translateY = 0;
                    updateImageTransform();
                }
            });
        }

        // ปุ่มหน้าถัดไป
        if (nextPageBtn) {
            nextPageBtn.addEventListener('click', function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    updatePage();
                    // รีเซ็ตการซูมและหมุน
                    scale = 1;
                    rotation = 0;
                    translateX = 0;
                    translateY = 0;
                    updateImageTransform();
                }
            });
        }

        // คลิกที่ thumbnail
        if (thumbnails && thumbnails.length > 0) {
            thumbnails.forEach(function(thumbnail) {
                thumbnail.addEventListener('click', function(e) {
                    console.log('Thumbnail clicked');
                    // Always treat as a click - we'll handle drag detection differently

                    // This was a click, proceed with thumbnail selection
                    // ลบคลาส active จากทุก thumbnail
                    thumbnails.forEach(function(thumb) {
                        thumb.classList.remove('active');
                        thumb.style.border = '2px solid transparent';
                    });

                    // เพิ่มคลาส active ให้กับ thumbnail ที่คลิก
                    this.classList.add('active');
                    this.style.border = '2px solid #0d6efd';

                    // อัปเดตหน้าปัจจุบัน
                    currentPage = parseInt(this.getAttribute('data-page'));
                    updatePage();

                    // รีเซ็ตการซูมและหมุน
                    scale = 1;
                    rotation = 0;
                    translateX = 0;
                    translateY = 0;
                    updateImageTransform();
                });
            });
        }

        // อัปเดตหน้าปัจจุบัน
        function updatePage() {
            // อัปเดตตัวเลขหน้า
            if (currentPageEl) {
                currentPageEl.textContent = currentPage;
            }

            // เปิด/ปิดปุ่มเปลี่ยนหน้า
            if (prevPageBtn) {
                prevPageBtn.disabled = currentPage <= 1;
            }
            if (nextPageBtn) {
                nextPageBtn.disabled = currentPage >= totalPages;
            }

            // อัปเดตภาพ
            const activeThumbnail = document.querySelector(`.thumbnail[data-page="${currentPage}"]`);
            if (activeThumbnail) {
                const thumbnailImg = activeThumbnail.querySelector('img');
                if (thumbnailImg && documentImage) {
                    documentImage.src = thumbnailImg.src;
                }

                // อัปเดตคลาส active และ border
                if (thumbnails && thumbnails.length > 0) {
                    thumbnails.forEach(function(thumb) {
                        thumb.classList.remove('active');
                        thumb.style.border = '2px solid transparent';
                    });
                    activeThumbnail.classList.add('active');
                    activeThumbnail.style.border = '2px solid #0d6efd';
                }
            }
        }

        // อัปเดตการแปลงภาพ (ซูม, หมุน, เลื่อน)
        function updateImageTransform() {
            if (documentImage) {
                documentImage.style.transform = `scale(${scale}) rotate(${rotation}deg) translate(${translateX}px, ${translateY}px)`;
            }
        }

        // เพิ่มการทำงานของการลากภาพ
        if (documentImage && mainImageContainer) {
            // เริ่มการลาก
            documentImage.addEventListener('mousedown', function(e) {
                imageIsDragging = true;
                startX = e.clientX - translateX;
                startY = e.clientY - translateY;
                documentImage.style.cursor = 'grabbing';
                e.preventDefault();
            });

            // เคลื่อนย้ายภาพ
            mainImageContainer.addEventListener('mousemove', function(e) {
                if (imageIsDragging) {
                    translateX = e.clientX - startX;
                    translateY = e.clientY - startY;
                    updateImageTransform();
                    e.preventDefault();
                }
            });

            // หยุดการลาก
            mainImageContainer.addEventListener('mouseup', function() {
                imageIsDragging = false;
                documentImage.style.cursor = 'move';
            });

            mainImageContainer.addEventListener('mouseleave', function() {
                imageIsDragging = false;
                documentImage.style.cursor = 'move';
            });

            // Touch support for mobile devices
            documentImage.addEventListener('touchstart', function(e) {
                imageIsDragging = true;
                startX = e.touches[0].clientX - translateX;
                startY = e.touches[0].clientY - translateY;
                e.preventDefault();
            });

            mainImageContainer.addEventListener('touchmove', function(e) {
                if (imageIsDragging) {
                    translateX = e.touches[0].clientX - startX;
                    translateY = e.touches[0].clientY - startY;
                    updateImageTransform();
                    e.preventDefault();
                }
            });

            mainImageContainer.addEventListener('touchend', function() {
                imageIsDragging = false;
            });

            mainImageContainer.addEventListener('touchcancel', function() {
                imageIsDragging = false;
            });
        }

        // เริ่มต้นอัปเดตหน้า
        updatePage();

        // สร้างแผนที่ถ้ามีพิกัด
        @if($item->latitude && $item->longitude)
        // ตัวแปรสำหรับแผนที่
        let map = null;

        // ฟังก์ชันสร้างแผนที่
        function createMap() {
            console.log('Creating map...');
            try {
                // ตรวจสอบว่ามี container หรือไม่
                const mapContainer = document.getElementById('item-map');
                if (!mapContainer) {
                    console.error('Map container not found');
                    return;
                }

                console.log('Map container found:', mapContainer);

                // ถ้ามีแผนที่อยู่แล้ว ให้ลบและสร้างใหม่
                if (map) {
                    map.remove();
                    map = null;
                }

                // สร้างแผนที่ใหม่
                map = L.map('item-map').setView([{{ $item->latitude }}, {{ $item->longitude }}], 13);

                // เพิ่ม tile layer จาก OpenStreetMap
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                // เพิ่มหมุดหมายที่ตำแหน่ง
                const marker = L.marker([{{ $item->latitude }}, {{ $item->longitude }}]).addTo(map);

                // เพิ่ม popup แสดงข้อมูล
                const popupContent = `
                    <strong>{{ $item->title }}</strong><br>
                    @if($item->location)สถานที่: {{ $item->location }}<br>@endif
                    @if($item->province)
                        จังหวัด:
                        @if($item->province === 'MM')
                            ประเทศเมียนมาร์<br>
                        @else
                            {{ $item->province_name ?: $item->province }}<br>
                        @endif
                    @endif
                `;
                marker.bindPopup(popupContent).openPopup();

                // ปรับขนาดแผนที่
                setTimeout(() => {
                    map.invalidateSize();
                    console.log('Map size updated');
                }, 300);

                console.log('Map created successfully');
            } catch (error) {
                console.error('Error creating map:', error);
            }
        }

        // สร้างแผนที่เมื่อโหลดหน้าถ้าแท็บแผนที่เปิดอยู่
        setTimeout(() => {
            const mapTabPane = document.getElementById('map');
            if (mapTabPane && mapTabPane.classList.contains('show') && mapTabPane.classList.contains('active')) {
                createMap();
            }
        }, 500);
        @endif
    });
</script>
<script src="{{ asset('js/item-viewer.js') }}"></script>
@endsection
