@extends('layouts.app')

@section('title', 'สถิติรายการ - คลังข้อมูลดิจิทัล')

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>สถิติข้อมูล</h1>
        <p>ข้อมูลสถิติเกี่ยวกับข้อมูลในคลังข้อมูลดิจิทัล</p>
    </div>
</div>

<div class="container py-4">
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-5 g-4 mb-5">
        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalItems }}</h2>
                        <p class="text-muted mb-0">รายการทั้งหมด</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-folder fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalCategories }}</h2>
                        <p class="text-muted mb-0">หมวดหมู่</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-language fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalLanguages }}</h2>
                        <p class="text-muted mb-0">ภาษา</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalLocations }}</h2>
                        <p class="text-muted mb-0">สถานที่</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-globe fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalCountries }}</h2>
                        <p class="text-muted mb-0">ประเทศ</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-map fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalProvinces }}</h2>
                        <p class="text-muted mb-0">จังหวัด</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card h-100 shadow-sm text-center">
                <div class="card-body">
                    <div class="py-3">
                        <i class="fas fa-font fa-3x text-primary mb-3"></i>
                        <h2 class="display-5 fw-semibold">{{ $totalScripts }}</h2>
                        <p class="text-muted mb-0">ตัวอักษร</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mb-5">
        <div class="card-header bg-white">
            <ul class="nav nav-tabs card-header-tabs" id="statsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab" aria-controls="categories" aria-selected="true">หมวดหมู่</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="document-types-tab" data-bs-toggle="tab" data-bs-target="#document-types" type="button" role="tab" aria-controls="document-types" aria-selected="false">ประเภทรายการ</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="languages-tab" data-bs-toggle="tab" data-bs-target="#languages" type="button" role="tab" aria-controls="languages" aria-selected="false">ภาษา</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations" type="button" role="tab" aria-controls="locations" aria-selected="false">สถานที่</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="years-tab" data-bs-toggle="tab" data-bs-target="#years" type="button" role="tab" aria-controls="years" aria-selected="false">ปี</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials" type="button" role="tab" aria-controls="materials" aria-selected="false">วัสดุ</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="countries-tab" data-bs-toggle="tab" data-bs-target="#countries" type="button" role="tab" aria-controls="countries" aria-selected="false">ประเทศ</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="provinces-tab" data-bs-toggle="tab" data-bs-target="#provinces" type="button" role="tab" aria-controls="provinces" aria-selected="false">จังหวัด</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="scripts-tab" data-bs-toggle="tab" data-bs-target="#scripts" type="button" role="tab" aria-controls="scripts" aria-selected="false">ตัวอักษร</button>
                </li>
            </ul>
        </div>

        <div class="card-body">
            <div class="tab-content" id="statsTabsContent">
                <!-- หมวดหมู่ -->
                <div class="tab-pane fade show active" id="categories" role="tabpanel" aria-labelledby="categories-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="categoryChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามหมวดหมู่</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>หมวดหมู่</th>
                                            <th class="text-end">จำนวนรายการ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($categoryStats as $category)
                                        <tr>
                                            <td>{{ $category->name }}</td>
                                            <td class="text-end">{{ $category->items_count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ประเภทเอกสาร -->
                <div class="tab-pane fade" id="document-types" role="tabpanel" aria-labelledby="document-types-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="documentTypeChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามประเภทรายการ</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ประเภทรายการ</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($itemTypeStats as $type)
                                        <tr>
                                            <td>{{ $type->item_type }}</td>
                                            <td class="text-end">{{ $type->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ภาษา -->
                <div class="tab-pane fade" id="languages" role="tabpanel" aria-labelledby="languages-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="languageChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามภาษา</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ภาษา</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($languageStats as $language)
                                        <tr>
                                            <td>{{ $language->language }}</td>
                                            <td class="text-end">{{ $language->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- สถานที่ -->
                <div class="tab-pane fade" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="locationChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามสถานที่</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>สถานที่</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($locationStats as $location)
                                        <tr>
                                            <td>{{ $location->location }}</td>
                                            <td class="text-end">{{ $location->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ปี -->
                <div class="tab-pane fade" id="years" role="tabpanel" aria-labelledby="years-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="yearChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามช่วงปี</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ทศวรรษ</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($yearStats as $year)
                                        <tr>
                                            <td>{{ $year->decade }}s</td>
                                            <td class="text-end">{{ $year->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- วัสดุ -->
                <div class="tab-pane fade" id="materials" role="tabpanel" aria-labelledby="materials-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="materialChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามวัสดุ</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>วัสดุ</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($materialStats as $material)
                                        <tr>
                                            <td>{{ $material->material }}</td>
                                            <td class="text-end">{{ $material->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ประเทศ -->
                <div class="tab-pane fade" id="countries" role="tabpanel" aria-labelledby="countries-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="countryChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามประเทศ</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ประเทศ</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($countryStats as $country)
                                        <tr>
                                            <td>{{ $country->country }}</td>
                                            <td class="text-end">{{ $country->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- จังหวัด -->
                <div class="tab-pane fade" id="provinces" role="tabpanel" aria-labelledby="provinces-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="provinceChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามจังหวัด</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>จังหวัด</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($provinceStats as $province)
                                        <tr>
                                            <td>{{ $province->province_name }}</td>
                                            <td class="text-end">{{ $province->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ตัวอักษร -->
                <div class="tab-pane fade" id="scripts" role="tabpanel" aria-labelledby="scripts-tab">
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="chart-container" style="position: relative; height: 400px;">
                                <canvas id="scriptChart"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <h5 class="mb-3">ข้อมูลตามตัวอักษร</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ตัวอักษร</th>
                                            <th class="text-end">จำนวน</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($scriptStats as $script)
                                        <tr>
                                            <td>{{ $script->script }}</td>
                                            <td class="text-end">{{ $script->count }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // ตั้งค่า Chart.js ให้ responsive
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;

        // Bootstrap จัดการแท็บให้อัตโนมัติแล้ว

        // สร้างสีสำหรับกราฟ
        function generateColors(count) {
            const colors = [];
            for (let i = 0; i < count; i++) {
                const hue = (i * 137.5) % 360;
                colors.push(`hsl(${hue}, 70%, 60%)`);
            }
            return colors;
        }

        // กราฟหมวดหมู่
        const categoryData = @json($categoryStats);
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryColors = generateColors(categoryData.length);

        new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: categoryData.map(item => item.name),
                datasets: [{
                    label: 'จำนวนรายการ',
                    data: categoryData.map(item => item.items_count),
                    backgroundColor: categoryColors,
                    borderColor: categoryColors.map(color => color.replace('0.6', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามหมวดหมู่',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        enabled: true,
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 11
                            },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });

        // กราฟประเภทรายการ
        const documentTypeData = @json($itemTypeStats);
        const documentTypeCtx = document.getElementById('documentTypeChart').getContext('2d');
        const documentTypeColors = generateColors(documentTypeData.length);

        new Chart(documentTypeCtx, {
            type: 'pie',
            data: {
                labels: documentTypeData.map(item => item.item_type),
                datasets: [{
                    data: documentTypeData.map(item => item.count),
                    backgroundColor: documentTypeColors,
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            },
                            boxWidth: 15,
                            padding: 10
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามประเภท',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // กราฟภาษา
        const languageData = @json($languageStats);
        const languageCtx = document.getElementById('languageChart').getContext('2d');
        const languageColors = generateColors(languageData.length);

        new Chart(languageCtx, {
            type: 'doughnut',
            data: {
                labels: languageData.map(item => item.language),
                datasets: [{
                    data: languageData.map(item => item.count),
                    backgroundColor: languageColors,
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            },
                            boxWidth: 15,
                            padding: 10
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามภาษา',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // กราฟสถานที่
        const locationData = @json($locationStats);
        const locationCtx = document.getElementById('locationChart').getContext('2d');
        const locationColors = generateColors(locationData.length);

        new Chart(locationCtx, {
            type: 'bar',
            data: {
                labels: locationData.map(item => item.location),
                datasets: [{
                    label: 'จำนวนรายการ',
                    data: locationData.map(item => item.count),
                    backgroundColor: locationColors,
                    borderColor: locationColors.map(color => color.replace('0.6', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามสถานที่',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        enabled: true
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });

        // กราฟปี
        const yearData = @json($yearStats);
        const yearCtx = document.getElementById('yearChart').getContext('2d');

        new Chart(yearCtx, {
            type: 'line',
            data: {
                labels: yearData.map(item => `${item.decade}s`),
                datasets: [{
                    label: 'จำนวนรายการ',
                    data: yearData.map(item => item.count),
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามช่วงปี',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        enabled: true
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });

        // กราฟวัสดุ
        const materialData = @json($materialStats);
        const materialCtx = document.getElementById('materialChart').getContext('2d');
        const materialColors = generateColors(materialData.length);

        new Chart(materialCtx, {
            type: 'pie',
            data: {
                labels: materialData.map(item => item.material),
                datasets: [{
                    data: materialData.map(item => item.count),
                    backgroundColor: materialColors,
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            },
                            boxWidth: 15,
                            padding: 10
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามวัสดุ',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // กราฟประเทศ
        const countryData = @json($countryStats);
        const countryCtx = document.getElementById('countryChart').getContext('2d');
        const countryColors = generateColors(countryData.length);

        new Chart(countryCtx, {
            type: 'pie',
            data: {
                labels: countryData.map(item => item.country),
                datasets: [{
                    data: countryData.map(item => item.count),
                    backgroundColor: countryColors,
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            },
                            boxWidth: 15,
                            padding: 10
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามประเทศ',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // กราฟจังหวัด
        const provinceData = @json($provinceStats);
        const provinceCtx = document.getElementById('provinceChart').getContext('2d');
        const provinceColors = generateColors(provinceData.length);

        new Chart(provinceCtx, {
            type: 'pie',
            data: {
                labels: provinceData.map(item => item.province_name),
                datasets: [{
                    data: provinceData.map(item => item.count),
                    backgroundColor: provinceColors,
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            },
                            boxWidth: 15,
                            padding: 10
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามจังหวัด',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // กราฟตัวอักษร
        const scriptData = @json($scriptStats);
        const scriptCtx = document.getElementById('scriptChart').getContext('2d');
        const scriptColors = generateColors(scriptData.length);

        new Chart(scriptCtx, {
            type: 'pie',
            data: {
                labels: scriptData.map(item => item.script),
                datasets: [{
                    data: scriptData.map(item => item.count),
                    backgroundColor: scriptColors,
                    borderColor: '#fff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 11
                            },
                            boxWidth: 15,
                            padding: 10
                        }
                    },
                    title: {
                        display: true,
                        text: 'จำนวนรายการตามตัวอักษร',
                        font: {
                            size: 14
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Location-related charts removed - Province

        // Location-related charts removed - Amphure

        // Location-related charts removed - District
    });
</script>
@endsection
