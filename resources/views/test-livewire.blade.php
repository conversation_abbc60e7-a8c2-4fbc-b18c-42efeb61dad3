<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Test Livewire</title>

    <!-- Styles -->
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 0;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>

    @livewireStyles
</head>
<body>
    <div class="container">
        <h1>Test Livewire</h1>

        <div id="livewire-test">
            <button onclick="testLivewire()">Test Livewire</button>
            <div id="result" class="result">Checking Livewire status...</div>

            <hr>

            <h2>Livewire Component Test</h2>
            <p>If Livewire is working correctly, you should see a counter component below:</p>

            @livewire('counter')
        </div>
    </div>

    @livewireScripts

    <!-- Fix Livewire for subfolder installation -->
    <script>
        // Prevent multiple instances of Livewire
        if (window.Livewire) {
            // Prevent $persist property redefinition error
            if (!Object.prototype.hasOwnProperty('$persist')) {
                Object.defineProperty(Object.prototype, '$persist', {
                    configurable: true,
                    get: function() {
                        return function(key) { return this[key]; };
                    }
                });
            }

            // Fix URLs for subfolder installation
            window.livewireServerUrl = '/dc';

            // Override the original message method to fix URLs
            const originalDispatch = window.Livewire.dispatch;
            window.Livewire.dispatch = function(event, ...params) {
                if (event === 'request' && params[0] && params[0].url && !params[0].url.startsWith('/dc')) {
                    params[0].url = '/dc' + params[0].url;
                }
                return originalDispatch.call(this, event, ...params);
            };
        }
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.Livewire) {
                console.log('Livewire is loaded');
                document.getElementById('result').innerHTML = '<span class="success">✓ Livewire is loaded</span>';
            } else {
                console.log('Livewire is NOT loaded');
                document.getElementById('result').innerHTML = '<span class="error">✗ Livewire is NOT loaded</span>';
            }
        });

        function testLivewire() {
            if (window.Livewire) {
                try {
                    // Check if Livewire is already initialized
                    if (window.Livewire.components) {
                        document.getElementById('result').innerHTML = '<span class="success">✓ Livewire is already initialized and working</span>';
                    } else {
                        document.getElementById('result').innerHTML = '<span class="error">✗ Livewire is loaded but not initialized properly</span>';
                    }
                } catch (e) {
                    document.getElementById('result').innerHTML = '<span class="error">✗ Error testing Livewire: ' + e.message + '</span>';
                    console.error('Error testing Livewire:', e);
                }
            } else {
                document.getElementById('result').innerHTML = '<span class="error">✗ Livewire is not available</span>';
            }
        }
    </script>
</body>
</html>
