<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))</title>
    <meta name="description" content="{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล') }}">

    @php
        $favicon = \App\Models\Setting::get('favicon');
    @endphp
    @if($favicon)
        <link rel="icon" href="{{ get_image_url($favicon, 'defaults/favicon.ico') }}" type="image/x-icon">
        <link rel="shortcut icon" href="{{ get_image_url($favicon, 'defaults/favicon.ico') }}" type="image/x-icon">
    @endif

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Sarabun -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    <link rel="stylesheet" href="{{ asset('css/back-to-top.css') }}">

    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }

        .auth-container {
            width: 100%;
            max-width: 450px;
            padding: 0 1rem;
        }

        .auth-logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-logo img {
            max-height: 80px;
            margin-bottom: 1rem;
        }

        .auth-logo h1 {
            font-size: 1.8rem;
            color: #2a3f6a;
            font-weight: 600;
        }

        .auth-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .auth-card-header {
            background: linear-gradient(135deg, #4a6baf 0%, #2a3f6a 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .auth-card-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .auth-card-body {
            padding: 2rem;
        }

        .auth-footer {
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .auth-footer a {
            color: #4a6baf;
            text-decoration: none;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4a6baf 0%, #2a3f6a 100%);
            border: none;
            padding: 0.6rem 1rem;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #3a5b9f 0%, #1a2f5a 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .form-control:focus {
            border-color: #4a6baf;
            box-shadow: 0 0 0 0.25rem rgba(74, 107, 175, 0.25);
        }


    </style>

    @yield('styles')
</head>
<body>
    <div class="auth-container">
        <div class="auth-logo">
            @php
                $siteLogo = \App\Models\Setting::get('site_logo');
            @endphp
            @if($siteLogo)
                <img src="{{ get_image_url($siteLogo, 'defaults/logo.png') }}" alt="{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}" class="img-fluid">
            @else
                <i class="fas fa-solid fa-box-archive fs-1 text-primary mb-3"></i>
            @endif
            <h1>{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}</h1>
        </div>

        @yield('content')

        <div class="auth-footer">
            <p>&copy; {{ date('Y') }} {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}. {{ \App\Models\Setting::get('copyright_text', 'สงวนลิขสิทธิ์') }}</p>
            <p><a href="{{ route('home') }}"><i class="fas fa-home me-1"></i>กลับไปยังหน้าหลัก</a></p>
        </div>
    </div>



    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    @yield('scripts')

    <!-- Back to Top Button Script -->
    <script src="{{ asset('js/back-to-top.js') }}"></script>
</body>
</html>
