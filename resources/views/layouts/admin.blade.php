<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="app-subfolder" content="{{ env('APP_SUBFOLDER', '') }}">

    <title>@yield('title', 'แผงควบคุม') - {{ \App\Models\Setting::get('site_title', 'คลังเอกสารดิจิทัล') }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">

    <!-- Google Fonts - Sarabun -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Uppy CSS -->
    <link href="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.css" rel="stylesheet">
    <link href="https://releases.transloadit.com/uppy/image-editor/v2.1.1/uppy-ImageEditor.min.css" rel="stylesheet">

    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="{{ asset('css/admin.css') }}">
    <link rel="stylesheet" href="{{ asset('css/back-to-top.css') }}">

    <!-- Vite Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Livewire Styles -->
    @livewireStyles

    <!-- Additional Styles -->
    @yield('styles')

    <style>
        /* Local Sarabun Font */
        @font-face {
            font-family: 'Sarabun';
            src: url('{{ asset('fonts/Sarabun-Regular.ttf') }}') format('truetype');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'Sarabun';
            src: url('{{ asset('fonts/Sarabun-Bold.ttf') }}') format('truetype');
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'Sarabun';
            src: url('{{ asset('fonts/Sarabun-Medium.ttf') }}') format('truetype');
            font-weight: 500;
            font-style: normal;
            font-display: swap;
        }

        body, html {
            font-family: 'Sarabun', sans-serif !important;
        }
        .form-control, .form-select, .btn, .card, .alert, .nav-link, .dropdown-item, h1, h2, h3, h4, h5, h6, p, span, div {
            font-family: 'Sarabun', sans-serif !important;
        }
    </style>
</head>
<body class="bg-light">
    <div class="wrapper">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom fixed-top">
            <div class="container-fluid px-3 py-2">
                <div class="d-flex justify-content-between w-100">
                    <div class="d-flex align-items-center">
                        <button id="toggleSidebarMobile" class="btn d-lg-none me-2 text-secondary" type="button" aria-expanded="true" aria-controls="sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <a href="{{ route('admin.dashboard') }}" class="navbar-brand fs-4 fw-bold d-flex align-items-center">
                            @php
                                $siteLogo = \App\Models\Setting::get('site_logo');
                            @endphp
                            @if($siteLogo)
                                <img src="{{ asset('storage/' . $siteLogo) }}" alt="{{ \App\Models\Setting::get('site_title', 'คลังเอกสารดิจิทัล') }}" height="40" class="d-inline-block align-middle me-2">
                            @else
                                <i class="fas fa-box-archive me-2 fs-3"></i>
                            @endif
                            <span>{{ \App\Models\Setting::get('site_title', 'คลังเอกสารดิจิทัล') }}</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center">
                        <!-- Date display -->
                        <div class="d-none d-lg-flex align-items-center me-3">
                            <span class="text-secondary">
                                <i class="fas fa-calendar-alt me-1"></i> {{ now()->locale('th')->isoFormat('LL') }}
                            </span>
                        </div>

                        <!-- Profile dropdown -->
                        <div class="dropdown">
                            <button class="btn p-0 border-0" type="button" id="user-menu-button" data-bs-toggle="dropdown" aria-expanded="false">
                                <img class="rounded-circle" width="40" height="40" src="{{ Auth::user()->getProfileImageUrl() }}" alt="{{ Auth::user()->name }}">
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end mt-2 shadow-sm" aria-labelledby="user-menu-button">
                                <li class="px-3 py-2">
                                    <span class="d-block fw-semibold">{{ Auth::user()->name }}</span>
                                    <span class="d-block text-secondary small text-truncate">{{ Auth::user()->email }}</span>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.profile') }}">
                                        <i class="fas fa-user-circle me-2"></i> โปรไฟล์
                                    </a>
                                </li>
                                @can('view settings')
                                <li>
                                    <a class="dropdown-item" href="{{ route('admin.settings') }}">
                                        <i class="fas fa-cog me-2"></i> ตั้งค่า
                                    </a>
                                </li>
                                @endcan
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i> ออกจากระบบ
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="d-flex">
            <!-- Sidebar -->
            <aside id="sidebar" class="sidebar">
                <div class="sidebar-content">
                    <ul class="nav flex-column mb-4">
                        <li class="nav-item">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                                <i class="fas fa-tachometer-alt me-3"></i>
                                <span>แดชบอร์ด</span>
                            </a>
                        </li>

                        @can('view items')
                        <li class="nav-item">
                            <a href="{{ route('admin.items') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.items.*') ? 'active' : '' }}">
                                <i class="fas fa-file-alt me-3"></i>
                                <span>รายการ</span>
                            </a>
                        </li>
                        @endcan

                        @can('view categories')
                        <li class="nav-item">
                            <a href="{{ route('admin.categories.index') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}">
                                <i class="fas fa-folder me-3"></i>
                                <span>หมวดหมู่</span>
                            </a>
                        </li>
                        @endcan

                        @can('view types')
                        <li class="nav-item">
                            <a href="{{ route('admin.item-types.index') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.item-types.*') ? 'active' : '' }}">
                                <i class="fas fa-file-invoice me-3"></i>
                                <span>ประเภทรายการ</span>
                            </a>
                        </li>
                        @endcan

                        @can('view materials')
                        <li class="nav-item">
                            <a href="{{ route('admin.materials') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.materials.*') ? 'active' : '' }}">
                                <i class="fas fa-scroll me-3"></i>
                                <span>วัสดุ</span>
                            </a>
                        </li>
                        @endcan

                        @can('view languages')
                        <li class="nav-item">
                            <a href="{{ route('admin.languages') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.languages.*') ? 'active' : '' }}">
                                <i class="fas fa-language me-3"></i>
                                <span>ภาษา</span>
                            </a>
                        </li>
                        @endcan

                        @can('view scripts')
                        <li class="nav-item">
                            <a href="{{ route('admin.scripts') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.scripts.*') ? 'active' : '' }}">
                                <i class="fas fa-pen-fancy me-3"></i>
                                <span>อักษร</span>
                            </a>
                        </li>
                        @endcan

                        @can('view users')
                        <li class="nav-item">
                            <a href="{{ route('admin.users') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                                <i class="fas fa-users me-3"></i>
                                <span>ผู้ใช้งาน</span>
                            </a>
                        </li>
                        @endcan
                        @can('view roles')
                        <li class="nav-item">
                            <a href="{{ route('admin.roles') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.roles') ? 'active' : '' }}">
                                <i class="fas fa-user-tag me-3"></i>
                                <span>บทบาท</span>
                            </a>
                        </li>
                        @endcan

                        @can('view permissions')
                        <li class="nav-item">
                            <a href="{{ route('admin.permissions') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.permissions') ? 'active' : '' }}">
                                <i class="fas fa-key me-3"></i>
                                <span>สิทธิ์การใช้งาน</span>
                            </a>
                        </li>
                        @endcan

                        @can('view settings')
                        <li class="nav-item">
                            <a href="{{ route('admin.settings') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.settings') ? 'active' : '' }}">
                                <i class="fas fa-cog me-3"></i>
                                <span>ตั้งค่า</span>
                            </a>
                        </li>
                        @endcan

                        <li class="nav-item">
                            <a href="{{ route('admin.cleanup.index') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded {{ request()->routeIs('admin.cleanup.*') ? 'active' : '' }}">
                                <i class="fas fa-trash-alt me-3"></i>
                                <span>ลบไฟล์ขยะ</span>
                            </a>
                        </li>
                    </ul>
                    <hr>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a href="{{ route('home') }}" class="nav-link d-flex align-items-center py-2 px-3 rounded">
                                <i class="fas fa-home me-3"></i>
                                <span>กลับไปยังหน้าเว็บไซต์</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="nav-link d-flex align-items-center py-2 px-3 rounded w-100 text-start border-0 bg-transparent">
                                    <i class="fas fa-sign-out-alt me-3"></i>
                                    <span>ออกจากระบบ</span>
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </aside>

            <!-- Mobile sidebar backdrop -->
            <div class="sidebar-backdrop d-none" id="sidebarBackdrop"></div>

            <!-- Main content -->
            <div id="main-content" class="main-content">
                <main class="content-wrapper">
                    <!-- Page header -->
                    @yield('header')

                    <!-- Flash messages -->
                    @if (session('success'))
                        <div class="alert alert-success mb-4" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger mb-4" role="alert">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if (session('warning'))
                        <div class="alert alert-warning mb-4" role="alert">
                            {{ session('warning') }}
                        </div>
                    @endif

                    @if (session('info'))
                        <div class="alert alert-info mb-4" role="alert">
                            {{ session('info') }}
                        </div>
                    @endif

                    <!-- Page content -->
                    @yield('content')
                </main>

                <!-- Footer -->
                <footer class="footer">
                    <div class="d-md-flex justify-content-between align-items-center">
                        <p class="text-secondary small mb-3 mb-md-0 text-center text-md-start">
                            &copy; {{ date('Y') }} pingdev.net
                        </p>
                        <div class="d-flex justify-content-center">
                            <a href="mailto:<EMAIL>" class="text-secondary me-3">
                                <i class="fas fa-envelope"></i>
                                <span class="visually-hidden">Email</span>
                            </a>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
    </div>



    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Sortable.js for drag-and-drop functionality -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <!-- Uppy JS (used by iframe) -->
    <script src="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.js"></script>
    <script src="https://releases.transloadit.com/uppy/image-editor/v2.1.1/uppy-ImageEditor.min.js"></script>
    <script src="https://releases.transloadit.com/uppy/locales/v3.0.1/th_TH.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Admin JS -->
    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleSidebarMobile = document.getElementById('toggleSidebarMobile');
            const sidebar = document.getElementById('sidebar');
            const sidebarBackdrop = document.getElementById('sidebarBackdrop');
            const body = document.body;

            if (toggleSidebarMobile) {
                toggleSidebarMobile.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    sidebarBackdrop.classList.toggle('d-none');
                    body.classList.toggle('sidebar-open');
                });
            }

            if (sidebarBackdrop) {
                sidebarBackdrop.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    sidebarBackdrop.classList.add('d-none');
                    body.classList.remove('sidebar-open');
                });
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 992) {
                    sidebar.classList.remove('show');
                    sidebarBackdrop.classList.add('d-none');
                    body.classList.remove('sidebar-open');
                }
            });
        });
    </script>

    <!-- Livewire Scripts -->
    @livewireScripts

    <!-- Fix Livewire for subfolder installation -->
    <script>
        // Prevent multiple instances of Livewire
        if (window.Livewire) {
            // Prevent $persist property redefinition error
            if (!Object.prototype.hasOwnProperty('$persist')) {
                Object.defineProperty(Object.prototype, '$persist', {
                    configurable: true,
                    get: function() {
                        return function(key) { return this[key]; };
                    }
                });
            }

            // Fix URLs for subfolder installation
            window.livewireServerUrl = '/dc';

            // Override the original message method to fix URLs
            const originalDispatch = window.Livewire.dispatch;
            window.Livewire.dispatch = function(event, ...params) {
                if (event === 'request' && params[0] && params[0].url && !params[0].url.startsWith('/dc')) {
                    params[0].url = '/dc' + params[0].url;
                }
                return originalDispatch.call(this, event, ...params);
            };
        }
    </script>

    <!-- Additional Scripts -->
    @yield('scripts')

    <!-- Stack for scripts -->
    @stack('scripts')

    <!-- Back to Top Button Script -->
    <script src="{{ asset('js/back-to-top.js') }}"></script>
</body>
</html>
