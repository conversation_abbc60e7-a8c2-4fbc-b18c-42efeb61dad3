<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Basic SEO Meta Tags -->
    <title>@yield('title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))</title>
    <meta name="description" content="@yield('description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))">
    <meta name="keywords" content="@yield('keywords', \App\Models\Setting::get('site_keywords', 'คลังข้อมูลดิจิทัล, มรดกทางวัฒนธรรม, ข้อมูลประวัติศาสตร์, ดิจิทัลไลบรารี, digital collection, cultural heritage, historical data'))">
    <meta name="author" content="{{ \App\Models\Setting::get('site_author', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล')) }}">
    <meta name="robots" content="@yield('robots', 'index, follow')">
    <meta name="language" content="th">
    <meta name="revisit-after" content="7 days">

    <!-- Canonical URL -->
    <link rel="canonical" href="@yield('canonical', request()->url())">

    <!-- Open Graph Meta Tags for Facebook -->
    <meta property="og:type" content="@yield('og_type', 'website')">
    <meta property="og:title" content="@yield('og_title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))">
    <meta property="og:description" content="@yield('og_description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))">
    <meta property="og:url" content="@yield('og_url', request()->url())">
    <meta property="og:site_name" content="{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}">
    <meta property="og:locale" content="th_TH">
    @hasSection('og_image')
        <meta property="og:image" content="@yield('og_image')">
        <meta property="og:image:width" content="1200">
        <meta property="og:image:height" content="630">
        <meta property="og:image:alt" content="@yield('og_image_alt', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))">
    @else
        @php
            $siteLogo = \App\Models\Setting::get('site_logo');
        @endphp
        @if($siteLogo)
            <meta property="og:image" content="{{ get_image_url($siteLogo) }}">
            <meta property="og:image:alt" content="{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}">
        @endif
    @endif

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="@yield('twitter_card', 'summary_large_image')">
    <meta name="twitter:title" content="@yield('twitter_title', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))">
    <meta name="twitter:description" content="@yield('twitter_description', \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม'))">
    @hasSection('twitter_image')
        <meta name="twitter:image" content="@yield('twitter_image')">
        <meta name="twitter:image:alt" content="@yield('twitter_image_alt', \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))">
    @else
        @if($siteLogo)
            <meta name="twitter:image" content="{{ get_image_url($siteLogo) }}">
            <meta name="twitter:image:alt" content="{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}">
        @endif
    @endif
    @if(\App\Models\Setting::get('twitter_username'))
        <meta name="twitter:site" content="@{{ \App\Models\Setting::get('twitter_username') }}">
        <meta name="twitter:creator" content="@{{ \App\Models\Setting::get('twitter_username') }}">
    @endif

    <!-- Facebook App ID -->
    @if(\App\Models\Setting::get('facebook_app_id'))
        <meta property="fb:app_id" content="{{ \App\Models\Setting::get('facebook_app_id') }}">
    @endif

    <!-- Google Site Verification -->
    @if(\App\Models\Setting::get('google_site_verification'))
        <meta name="google-site-verification" content="{{ \App\Models\Setting::get('google_site_verification') }}">
    @endif

    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <meta name="application-name" content="{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}">

    <!-- JSON-LD Structured Data -->
    @hasSection('structured_data')
        @yield('structured_data')
    @else
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
            "description": "{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมข้อมูลในรูปแบบดิจิทัล สำหรับการอนุรักษ์และเผยแพร่มรดกทางวัฒนธรรม') }}",
            "url": "{{ config('app.url') }}",
            "potentialAction": {
                "@type": "SearchAction",
                "target": "{{ route('search.index') }}?q={search_term_string}",
                "query-input": "required name=search_term_string"
            }
        }
        </script>
    @endif

    <!-- Google Tag Manager -->
    @if(\App\Models\Setting::get('google_tag_manager_id'))
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','{{ \App\Models\Setting::get('google_tag_manager_id') }}');</script>
    @endif

    <!-- Google Analytics -->
    @if(\App\Models\Setting::get('google_analytics_id'))
        @php
            $gaId = \App\Models\Setting::get('google_analytics_id');
        @endphp
        @if(str_starts_with($gaId, 'G-'))
            <!-- Google Analytics 4 -->
            <script async src="https://www.googletagmanager.com/gtag/js?id={{ $gaId }}"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '{{ $gaId }}');
            </script>
        @elseif(str_starts_with($gaId, 'UA-'))
            <!-- Universal Analytics -->
            <script async src="https://www.googletagmanager.com/gtag/js?id={{ $gaId }}"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '{{ $gaId }}');
            </script>
        @endif
    @endif

    @php
        $favicon = \App\Models\Setting::get('favicon');
    @endphp
    @if($favicon)
        <link rel="icon" href="{{ get_image_url($favicon, 'defaults/favicon.ico') }}" type="image/x-icon">
        <link rel="shortcut icon" href="{{ get_image_url($favicon, 'defaults/favicon.ico') }}" type="image/x-icon">
    @endif

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Sarabun -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    <link rel="stylesheet" href="{{ asset('css/item-viewer-new.css') }}">
    <link rel="stylesheet" href="{{ asset('css/back-to-top.css') }}">

    <!-- Livewire Styles -->
    @livewireStyles

    <!-- Additional Styles for specific pages -->
    <style>
        /* Local Sarabun Font */
        @font-face {
            font-family: 'Sarabun';
            src: url('{{ asset('fonts/Sarabun-Regular.ttf') }}') format('truetype');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'Sarabun';
            src: url('{{ asset('fonts/Sarabun-Bold.ttf') }}') format('truetype');
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'Sarabun';
            src: url('{{ asset('fonts/Sarabun-Medium.ttf') }}') format('truetype');
            font-weight: 500;
            font-style: normal;
            font-display: swap;
        }

        /* Global styles */
        .pagination-wrapper {
            margin: 2rem 0;
        }



        /* Loading indicator for Livewire */
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
            border-width: 0.2em;
        }

        [wire\:loading] {
            opacity: 0.5;
        }

        [wire\:loading\.delay] {
            opacity: 0.5;
        }

        .content-loading {
            filter: blur(2px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .wire-loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.8);
            min-width: 200px;
            animation: fadeIn 0.3s ease-out;
            text-align: center;
            margin: 0 auto;
            /* เพิ่มความแน่ใจว่าจะอยู่ตรงกลางและไม่ถูกองค์ประกอบอื่นทับ */
            width: auto;
            height: auto;
            max-width: 90%;
            pointer-events: all;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -60%); }
            to { opacity: 1; transform: translate(-50%, -50%); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .loading-spinner {
            display: inline-block;
            width: 60px;
            height: 60px;
            border: 4px solid rgba(13, 110, 253, 0.3);
            border-radius: 50%;
            border-top-color: #0d6efd;
            animation: spin 1s ease-in-out infinite, pulse-shadow 2s ease-in-out infinite;
            margin-bottom: 1.5rem;
            position: relative;
            margin-left: auto;
            margin-right: auto;
        }

        .loading-spinner::after {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border-radius: 50%;
            border: 2px solid transparent;
            border-top-color: rgba(13, 110, 253, 0.5);
            animation: spin 1.5s linear infinite reverse;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes pulse-shadow {
            0% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(13, 110, 253, 0); }
            100% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0); }
        }

        .loading-text {
            color: #333;
            font-weight: 600;
            margin-top: 0.5rem;
            font-size: 1.1rem;
            letter-spacing: 0.5px;
        }

        .loading-dots {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 0.5rem;
            height: 24px;
            width: 100%;
        }

        .dot {
            font-size: 2rem;
            line-height: 1;
            color: #0d6efd;
            margin: 0 3px;
            font-weight: bold;
        }

        .dot-1 {
            animation: dotFade 1.5s 0s infinite;
        }

        .dot-2 {
            animation: dotFade 1.5s 0.5s infinite;
        }

        .dot-3 {
            animation: dotFade 1.5s 1s infinite;
        }

        @keyframes dotFade {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 1; }
        }

        /* Progress bar for Livewire loading */
        .livewire-loading-progress {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            width: 0%;
            background: linear-gradient(to right, #0d6efd, #0dcaf0);
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            box-shadow: 0 0 10px rgba(13, 110, 253, 0.5);
        }

        /* Items list header styles */
        .items-header-card {
            background-color: #fff;
            transition: all 0.3s ease;
        }

        .items-header-card:hover {
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08) !important;
        }

        .items-header-card .badge {
            transition: all 0.3s ease;
        }

        .items-header-card:hover .badge {
            transform: scale(1.05);
        }

        /* Responsive styles for mobile devices */
        @media (max-width: 768px) {
            .wire-loading-indicator {
                padding: 1.5rem;
                min-width: 180px;
                max-width: 85%;
            }

            .loading-spinner {
                width: 50px;
                height: 50px;
                margin-bottom: 1rem;
            }

            .loading-text {
                font-size: 1rem;
            }

            .dot {
                font-size: 1.8rem;
                margin: 0 2px;
            }
        }
    </style>

    @yield('styles')
</head>
<body class="{{ request()->routeIs('home') ? 'home-page' : 'not-home' }}">
    <!-- Google Tag Manager (noscript) -->
    @if(\App\Models\Setting::get('google_tag_manager_id'))
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ \App\Models\Setting::get('google_tag_manager_id') }}"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    @endif

    @if(request()->routeIs('home'))
    <div class="home-wrapper">
    @endif

    <header class="w-100">
        <nav class="navbar navbar-expand-lg navbar-light {{ request()->routeIs('home') ? '' : 'bg-white' }}">
            <div class="container">
                <a class="navbar-brand" href="{{ route('home') }}">
                    @php
                        $siteLogo = \App\Models\Setting::get('site_logo');
                    @endphp
                    @if($siteLogo)
                        <img src="{{ get_image_url($siteLogo, 'defaults/logo.png') }}" alt="{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}" height="60" class="d-inline-block align-middle me-2">
                    @else
                        <i class="fas fa-solid fa-box-archive me-2 fs-2"></i>
                    @endif
                    {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}"><i class="fas fa-home me-1"></i>หน้าแรก</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('items.index') || request()->routeIs('items.show') ? 'active' : '' }}" href="{{ route('items.index') }}"><i class="fas fa-file-alt me-1"></i>รายการ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('categories.*') ? 'active' : '' }}" href="{{ route('categories.index') }}"><i class="fas fa-folder me-1"></i>หมวดหมู่</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('search.*') ? 'active' : '' }}" href="{{ route('search.index') }}"><i class="fas fa-search me-1"></i>ค้นหา</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('statistics.*') ? 'active' : '' }}" href="{{ route('statistics.index') }}"><i class="fas fa-chart-bar me-1"></i>สถิติ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('maps.*') ? 'active' : '' }}" href="{{ route('maps.index') }}"><i class="fas fa-map-marked-alt me-1"></i>แผนที่</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('api.documentation') ? 'active' : '' }}" href="{{ route('api.documentation') }}"><i class="fas fa-code me-1"></i>API</a>
                        </li>
                        @auth
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-1"></i> {{ Auth::user()->name }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    @if(Auth::user()->hasAnyPermission(['view items', 'view categories', 'view types', 'view languages', 'view scripts', 'view materials', 'view users', 'view settings', 'view roles', 'view permissions']))
                                    <li>
                                        <a href="{{ route('admin.dashboard') }}" class="dropdown-item">
                                            <i class="fas fa-tachometer-alt me-2"></i>หน้าแอดมิน
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    @endif
                                    <li>
                                        <a href="{{ route('admin.profile') }}" class="dropdown-item">
                                            <i class="fas fa-user-cog me-2"></i>โปรไฟล์
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="{{ route('logout') }}" method="POST">
                                            @csrf
                                            <button type="submit" class="dropdown-item"><i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        @else
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('login') ? 'active' : '' }}" href="{{ route('login') }}"><i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ</a>
                            </li>
                        @endauth
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="{{ request()->routeIs('home') ? '' : 'py-4' }}">
        <div class="container">
            <!-- Flash messages -->
            @if (session('success'))
                <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('warning'))
                <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
                    {{ session('warning') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if (session('info'))
                <div class="alert alert-info alert-dismissible fade show mb-4" role="alert">
                    {{ session('info') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif
        </div>

        @yield('content')
    </main>

    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5 class="mb-3">{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}</h5>
                    <p class="mb-0">{{ \App\Models\Setting::get('site_description', 'แหล่งรวบรวมและอนุรักษ์ข้อมูลที่มีคุณค่าทางประวัติศาสตร์และวัฒนธรรม') }}</p>
                </div>
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5 class="mb-3">ลิงก์ด่วน</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ route('home') }}" class="text-white-50">หน้าแรก</a></li>
                        <li class="mb-2"><a href="{{ route('items.index') }}" class="text-white-50">รายการทั้งหมด</a></li>
                        <li class="mb-2"><a href="{{ route('categories.index') }}" class="text-white-50">หมวดหมู่</a></li>
                        <li class="mb-2"><a href="{{ route('search.index') }}" class="text-white-50">ค้นหา</a></li>
                        <li class="mb-2"><a href="{{ route('maps.index') }}" class="text-white-50">แผนที่ข้อมูล</a></li>
                        <li class="mb-2"><a href="{{ route('api.documentation') }}" class="text-white-50">API Documentation</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3">ติดต่อเรา</h5>
                    <ul class="list-unstyled">
                        @if(\App\Models\Setting::get('contact_email'))
                            <li class="mb-2"><i class="fas fa-envelope me-2"></i> {{ \App\Models\Setting::get('contact_email') }}</li>
                        @endif
                        @if(\App\Models\Setting::get('contact_phone'))
                            <li class="mb-2"><i class="fas fa-phone me-2"></i> {{ \App\Models\Setting::get('contact_phone') }}</li>
                        @endif
                        @if(\App\Models\Setting::get('contact_address'))
                            <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> {{ \App\Models\Setting::get('contact_address') }}</li>
                        @endif
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; {{ date('Y') }} {{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}. {{ \App\Models\Setting::get('copyright_text', 'สงวนลิขสิทธิ์') }}</p>
                    @if(\App\Models\Setting::get('footer_text'))
                        <p class="mt-2 small text-white-50">{{ \App\Models\Setting::get('footer_text') }}</p>
                    @endif
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        @if(\App\Models\Setting::get('facebook_url'))
                            <li class="list-inline-item"><a href="{{ \App\Models\Setting::get('facebook_url') }}" target="_blank" class="text-white-50"><i class="fab fa-facebook-f"></i></a></li>
                        @endif
                        @if(\App\Models\Setting::get('twitter_url'))
                            <li class="list-inline-item"><a href="{{ \App\Models\Setting::get('twitter_url') }}" target="_blank" class="text-white-50"><i class="fab fa-twitter"></i></a></li>
                        @endif
                        @if(\App\Models\Setting::get('instagram_url'))
                            <li class="list-inline-item"><a href="{{ \App\Models\Setting::get('instagram_url') }}" target="_blank" class="text-white-50"><i class="fab fa-instagram"></i></a></li>
                        @endif
                        @if(\App\Models\Setting::get('youtube_url'))
                            <li class="list-inline-item"><a href="{{ \App\Models\Setting::get('youtube_url') }}" target="_blank" class="text-white-50"><i class="fab fa-youtube"></i></a></li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    @if(request()->routeIs('home'))
    </div><!-- End of home-wrapper -->
    @endif



    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    @if(request()->routeIs('home'))
    <script>
        // Add scrolled class to header when scrolling
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('header');
            const body = document.querySelector('body');
            const searchBox = document.querySelector('.search-box');

            // Apply initial styles to prevent movement
            if (searchBox) {
                searchBox.style.opacity = '1';
                searchBox.style.transform = 'translateZ(0)';
            }

            function checkScroll() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                    body.classList.add('navbar-scrolled');
                } else {
                    header.classList.remove('scrolled');
                    body.classList.remove('navbar-scrolled');
                }
            }

            // Check on initial load
            checkScroll();

            // Add scroll event listener
            window.addEventListener('scroll', checkScroll);
        });
    </script>
    @endif

    @yield('scripts')

    <!-- Livewire Scripts -->
    @livewireScripts

    <!-- Fix Livewire for subfolder installation -->
    <script>
        // Prevent multiple instances of Livewire
        if (window.Livewire) {
            // Prevent $persist property redefinition error
            if (!Object.prototype.hasOwnProperty('$persist')) {
                Object.defineProperty(Object.prototype, '$persist', {
                    configurable: true,
                    get: function() {
                        return function(key) { return this[key]; };
                    }
                });
            }

            // Fix URLs for subfolder installation
            window.livewireServerUrl = '/dc';

            // Override the original message method to fix URLs
            const originalDispatch = window.Livewire.dispatch;
            window.Livewire.dispatch = function(event, ...params) {
                if (event === 'request' && params[0] && params[0].url && !params[0].url.startsWith('/dc')) {
                    params[0].url = '/dc' + params[0].url;
                }
                return originalDispatch.call(this, event, ...params);
            };
        }
    </script>

    <!-- Back to Top Button Script -->
    <script src="{{ asset('js/back-to-top.js') }}"></script>

    <!-- Progress Bar for Livewire Loading - Disabled for subfolder installation -->
    {{-- Progress bar removed --}}


</body>
</html>
