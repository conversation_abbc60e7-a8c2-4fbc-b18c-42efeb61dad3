@extends('layouts.app')

@section('title', isset($title) ? $title : 'เกิดข้อผิดพลาด')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm border-0">
                <div class="card-body p-5 text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-circle text-danger" style="font-size: 5rem;"></i>
                    </div>
                    <h1 class="h3 mb-3">{{ isset($title) ? $title : 'เกิดข้อผิดพลาด' }}</h1>
                    <p class="text-muted mb-4">{{ isset($message) ? $message : 'เกิดข้อผิดพลาดในการเข้าถึงหน้านี้' }}</p>
                    
                    @if(isset($details))
                    <div class="alert alert-danger">
                        <p class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ $details }}
                        </p>
                    </div>
                    @endif
                    
                    <div class="mt-4">
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>กลับไปยังหน้าแรก
                        </a>
                        <a href="{{ url()->previous() }}" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>กลับไปยังหน้าก่อนหน้า
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
