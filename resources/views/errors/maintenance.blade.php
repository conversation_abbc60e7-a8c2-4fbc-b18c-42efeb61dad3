<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }} - ระบบอยู่ระหว่างการปรับปรุง</title>
    <link rel="stylesheet" href="{{ asset('css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .maintenance-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        .maintenance-card {
            max-width: 600px;
            width: 100%;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .maintenance-header {
            background: linear-gradient(135deg, #4a6baf 0%, #2a3f6a 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .maintenance-body {
            padding: 2rem;
            background-color: white;
            text-align: center;
        }
        .maintenance-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
            color: #4a6baf;
        }
        .admin-link {
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-card">
            <div class="maintenance-header">
                <h1>ระบบอยู่ระหว่างการปรับปรุง</h1>
            </div>
            <div class="maintenance-body">
                <div class="maintenance-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h2>ขออภัยในความไม่สะดวก</h2>
                <p class="lead">{{ $message ?? \App\Models\Setting::get('maintenance_message', 'ระบบกำลังอยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่ในภายหลัง') }}</p>

                @auth
                <div class="admin-link">
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>เข้าสู่หน้าแอดมิน
                    </a>
                </div>
                @else
                <div class="admin-link">
                    <a href="{{ route('login') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                    </a>
                </div>
                @endauth
            </div>
        </div>
    </div>

    <script src="{{ asset('js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>
