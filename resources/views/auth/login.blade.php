@extends('layouts.auth')

@section('title', 'เข้าสู่ระบบ - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))

@section('content')
<div class="auth-card">
    <div class="auth-card-header">
        <h2><i class="fas fa-lock me-2"></i>เข้าสู่ระบบ</h2>
    </div>
    <div class="auth-card-body">
        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <form method="POST" action="{{ route('login') }}">
            @csrf

            <div class="mb-4">
                <label for="email" class="form-label">อีเมล</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus placeholder="กรอกอีเมลของคุณ">
                </div>
                @error('email')
                    <div class="text-danger mt-1 small">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="mb-4">
                <label for="password" class="form-label">รหัสผ่าน</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-key"></i></span>
                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" required autocomplete="current-password" placeholder="กรอกรหัสผ่านของคุณ">
                </div>
                @error('password')
                    <div class="text-danger mt-1 small">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="mb-4 form-check">
                <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                <label class="form-check-label" for="remember">
                    จดจำการเข้าสู่ระบบ
                </label>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // เพิ่มเอฟเฟกต์เมื่อโหลดหน้า
        const authCard = document.querySelector('.auth-card');
        setTimeout(() => {
            authCard.style.transition = 'all 0.5s ease';
            authCard.style.transform = 'translateY(0)';
            authCard.style.opacity = '1';
        }, 100);
    });
</script>
@endsection

@section('styles')
<style>
    .auth-card {
        transform: translateY(20px);
        opacity: 0.8;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }

    .input-group .form-control {
        border-left: none;
    }

    .input-group .form-control:focus {
        border-color: #ced4da;
        box-shadow: none;
    }

    .input-group:focus-within .input-group-text {
        border-color: #4a6baf;
    }

    .input-group:focus-within .form-control {
        border-color: #4a6baf;
    }
</style>
@endsection
