@extends('layouts.admin')

@section('title', 'จัดการอักษร - ระบบจัดการคลังเอกสารโบราณดิจิทัล')

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="fs-4 fw-bold mb-2">จัดการอักษร</h3>
                <span class="text-secondary">รายการอักษรทั้งหมดในระบบ</span>
            </div>
            <div>
                <a href="{{ route('admin.scripts.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มอักษรใหม่
                </a>
            </div>
        </div>

        <!-- Scripts Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ชื่ออักษร
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            รหัสอักษร
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จำนวนรายการ
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            วันที่สร้าง
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จัดการ
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($scripts as $script)
                    <tr>
                        <td class="fw-medium">
                            {{ $script->name }}
                        </td>
                        <td class="text-secondary">
                            {{ $script->code ?: '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ number_format($script->items_count) }}
                        </td>
                        <td class="text-secondary">
                            {{ $script->created_at ? $script->created_at->format('d/m/Y') : '-' }}
                        </td>
                        <td>
                            <div class="d-flex gap-3">
                                <a href="{{ route('admin.scripts.edit', $script) }}" class="text-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.scripts.destroy', $script) }}" method="POST" class="d-inline" onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบอักษรนี้?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center text-secondary py-3">
                            ไม่พบอักษร
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4">
            {{ $scripts->links() }}
        </div>
    </div>
</div>
@endsection
