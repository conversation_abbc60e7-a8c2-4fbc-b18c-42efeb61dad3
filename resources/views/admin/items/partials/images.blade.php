@if($item->images->isNotEmpty())
    <div class="mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Current Images</h6>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="toggle-view-btn">
                        <i class="fas fa-table me-1"></i> Table View
                    </button>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <!-- Table view (hidden by default) -->
                <div id="table-view" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 150px;">Image</th>
                                    <th style="width: 80px;">Order</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($item->images->sortBy('sort_order') as $index => $image)
                                    <tr>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <img src="{{ get_image_url($image->image_path, 'defaults/missing-image.svg') }}?v={{ time() }}" class="img-thumbnail mb-1" alt="Item Image" style="width: 80px; height: 80px; object-fit: cover;">
                                                <small class="text-muted text-truncate" style="max-width: 120px;" title="{{ $image->image_path }}">
                                                    {{ basename($image->image_path) }}
                                                </small>
                                            </div>
                                        </td>
                                        <td class="align-middle text-center">
                                            <span class="badge bg-secondary fs-6">{{ $image->sort_order }}</span>
                                        </td>

                                        <td class="align-middle">
                                            <div class="d-flex gap-1">






                                                <!-- Delete Button -->
                                                <form action="{{ route('admin.items.delete-image', $image->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this image? This will only remove the image attachment, not the item itself.');">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete image">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Grid view with drag and drop (shown by default) -->
                <div id="grid-view">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-1"></i> คลิกที่หัวข้อรูปภาพและลากเพื่อจัดเรียงลำดับใหม่ การเปลี่ยนแปลงจะถูกบันทึกโดยอัตโนมัติ
                        <br><small>(Drag and drop images to reorder them. Changes are saved automatically.)</small>
                    </div>

                    <div id="sortable-container" class="row" data-document-id="{{ $item->id }}">
                        @foreach($item->images->sortBy('sort_order') as $image)
                            <div class="col-md-3 col-sm-4 col-6 mb-3 sortable-item" data-id="{{ $image->id }}">
                                <div class="card h-100">
                                    <div class="card-header p-2 sortable-handle bg-light" style="cursor: move;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-secondary">Order: {{ $image->sort_order }}</span>
                                            <i class="fas fa-arrows-alt text-muted"></i>
                                        </div>
                                    </div>
                                    <div class="position-relative">
                                        <img src="{{ get_image_url($image->image_path, 'defaults/missing-image.svg') }}?v={{ time() }}" class="card-img-top" alt="Item Image" style="height: 150px; object-fit: cover;">
                                    </div>
                                    <div class="card-body p-2">
                                        <p class="small text-muted text-truncate mb-1" title="{{ $image->image_path }}">
                                            <i class="fas fa-file-image me-1"></i> {{ basename($image->image_path) }}
                                        </p>
                                    </div>
                                    <div class="card-footer p-2">
                                        <div class="d-flex justify-content-between">


                                            <form action="{{ route('admin.items.delete-image', $image->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this image? This will only remove the image attachment, not the item itself.');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete image">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div id="order-status" class="mt-2" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
@else
    <p class="text-muted">No images uploaded yet.</p>
@endif

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    console.log('SortableJS script loaded in images.blade.php');
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between table and grid view
        const toggleViewBtn = document.getElementById('toggle-view-btn');
        const tableView = document.getElementById('table-view');
        const gridView = document.getElementById('grid-view');

        if (toggleViewBtn && tableView && gridView) {
            toggleViewBtn.addEventListener('click', function() {
                if (gridView.style.display !== 'none') {
                    // Switch to table view
                    gridView.style.display = 'none';
                    tableView.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-th me-1"></i> Grid View';
                } else {
                    // Switch to grid view
                    tableView.style.display = 'none';
                    gridView.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-table me-1"></i> Table View';
                }
            });
        }



        // Initialize Sortable for drag and drop
        const sortableContainer = document.getElementById('sortable-container');
        if (sortableContainer) {
            const documentId = sortableContainer.getAttribute('data-document-id');

            const sortable = new Sortable(sortableContainer, {
                animation: 150,
                handle: '.sortable-handle',
                ghostClass: 'sortable-ghost',
                onEnd: function(evt) {
                    // Get all items
                    const items = sortableContainer.querySelectorAll('.sortable-item');
                    const imageIds = Array.from(items).map(item => item.getAttribute('data-id'));

                    // Show status
                    const status = document.getElementById('order-status');
                    if (status) {
                        status.innerHTML = '<div class="alert alert-info">Saving new order...</div>';
                        status.style.display = 'block';
                    }

                    // Send the new order to the server
                    fetch(`{{ url('/admin/items') }}/${documentId}/update-image-order`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({ imageIds: imageIds })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (status) {
                            if (data.success) {
                                status.innerHTML = '<div class="alert alert-success">Order updated successfully!</div>';

                                // Update the order numbers displayed in the UI
                                items.forEach((item, index) => {
                                    const orderBadge = item.querySelector('.badge');
                                    if (orderBadge) {
                                        orderBadge.textContent = `Order: ${index + 1}`;
                                    }
                                });
                            } else {
                                status.innerHTML = `<div class="alert alert-danger">Error: ${data.message || 'Unknown error'}</div>`;
                            }

                            // Hide the status after 3 seconds
                            setTimeout(() => {
                                status.style.display = 'none';
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('Error updating order:', error);
                        if (status) {
                            status.innerHTML = `<div class="alert alert-danger">Error updating order: ${error.message}. Please try again.</div>`;

                            // Hide the status after 5 seconds
                            setTimeout(() => {
                                status.style.display = 'none';
                            }, 5000);
                        }
                    });
                }
            });
        }
    });
</script>
