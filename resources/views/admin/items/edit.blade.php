@extends('layouts.admin')

@section('title', 'แก้ไขรายการ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('styles')
<!-- Dropzone CSS -->
<link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
<style>
<style>
    /* Dropzone custom styles - improved design */
    .dropzone {
        margin-bottom: 1.5rem;
        font-family: inherit;
        background-color: #f8fafc;
        border: 2px dashed #4e73df; /* Primary color from SB Admin theme */
        border-radius: 0.5rem;
        min-height: 150px;
        padding: 25px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .dropzone:hover {
        background-color: #eef2ff;
        border-color: #2e59d9;
    }

    .dropzone .dz-message {
        color: #5a5c69; /* Text color from SB Admin theme */
        font-size: 1.1rem;
        text-align: center;
        margin: 2em 0;
        font-weight: 500;
    }

    .dropzone .dz-message .text-muted {
        font-size: 0.9rem;
        margin-top: 0.5rem;
        display: block;
    }

    .dropzone .dz-preview {
        margin: 12px;
        position: relative;
    }

    .dropzone .dz-preview .dz-image {
        border-radius: 10px;
        overflow: hidden;
        width: 120px;
        height: 120px;
        position: relative;
        display: block;
        z-index: 10;
        border: 1px solid #e3e6f0;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .dropzone .dz-preview .dz-details {
        padding: 6px;
        text-align: center;
        color: #5a5c69;
        font-size: 0.85rem;
    }

    /* Fix for error message position */
    .dropzone .dz-preview .dz-error-message {
        top: 150px; /* Position further below the image */
        left: 0;
        right: 0;
        text-align: center;
        background: rgba(220, 53, 69, 0.9);
        color: white;
        border-radius: 5px;
        padding: 5px;
        font-size: 0.8rem;
        width: 120px;
        margin: 0 auto;
        z-index: 25; /* Lower than alert messages */
    }

    /* Custom alert messages inside dropzone */
    .dropzone .dz-preview .alert {
        position: absolute;
        top: 150px; /* Position below the preview element */
        left: -40px; /* Extend beyond the preview element */
        width: 200px; /* Wider than the preview */
        z-index: 30;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    /* Fix for remove button position */
    .dropzone .dz-preview .dz-remove {
        position: absolute;
        top: -10px;
        right: -10px;
        z-index: 20;
        font-size: 0.8rem;
        background-color: #e74a3b; /* Danger color from SB Admin theme */
        color: white;
        border-radius: 50%;
        width: 22px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        text-decoration: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    }

    .dropzone .dz-preview .dz-remove:hover {
        background-color: #c23321;
        text-decoration: none;
    }

    .dropzone .dz-preview .dz-success-mark,
    .dropzone .dz-preview .dz-error-mark {
        margin-top: -25px;
    }

    .dropzone .dz-preview.dz-success .dz-success-mark {
        opacity: 1;
    }

    .dropzone .dz-preview.dz-error .dz-error-mark {
        opacity: 1;
    }

    /* Progress bar styling */
    .dropzone .dz-preview .dz-progress {
        height: 10px;
        border-radius: 5px;
        background: rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .dropzone .dz-preview .dz-progress .dz-upload {
        background: #4e73df; /* Primary color */
        height: 100%;
        transition: width 0.3s ease;
    }

    /* File type indicators - with proper icons */
    .dropzone .dz-preview .dz-image:before {
        content: "\f15b"; /* Default file icon */
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 15;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        font-size: 12px;
        color: #4e73df;
    }

    /* Image file icon */
    .dropzone .dz-preview[data-type^="image"] .dz-image:before {
        content: "\f03e"; /* Image icon */
        color: #4e73df;
    }

    /* PDF file icon */
    .dropzone .dz-preview[data-type="application/pdf"] .dz-image:before {
        content: "\f1c1"; /* PDF icon */
        color: #e74a3b;
    }

    /* Audio file icon */
    .dropzone .dz-preview[data-type^="audio"] .dz-image:before {
        content: "\f1c7"; /* Audio icon */
        color: #1cc88a;
    }

    /* Video file icon */
    .dropzone .dz-preview[data-type^="video"] .dz-image:before {
        content: "\f1c8"; /* Video icon */
        color: #f6c23e;
    }

    /* Document file icon */
    .dropzone .dz-preview[data-type^="application/vnd.openxmlformats-officedocument"] .dz-image:before,
    .dropzone .dz-preview[data-type^="application/msword"] .dz-image:before {
        content: "\f15c"; /* Document icon */
        color: #36b9cc;
    }
</style>
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>เกิดข้อผิดพลาดในการบันทึกข้อมูล:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">แก้ไขรายการ: {{ $item->title }}</h1>
        <div>
            <a href="{{ route('items.show', $item->id) }}" class="btn btn-info me-2" target="_blank">
                <i class="fas fa-eye me-2"></i>ดูรายการ
            </a>
            <a href="{{ route('admin.items') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการทั้งหมด
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-2">ข้อมูลรายการ</h5>
            <p class="card-subtitle text-muted">แก้ไขข้อมูลรายการ: {{ $item->title }}</p>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.items.update', $item->id) }}" method="POST" enctype="multipart/form-data" id="item-edit-form">
                @csrf
                @method('PUT')
                <!-- Hidden input to preserve file path - ALWAYS set to true to prevent file loss -->
                <input type="hidden" name="preserve_file_path" id="preserve_file_path" value="true">

                <div class="row">
                    <!-- ข้อมูลพื้นฐาน -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลพื้นฐาน</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">ชื่อรายการ <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $item->title) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    @livewire('item-identifier-checker', ['itemId' => $item->id])
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="other_title1" class="form-label">ชื่อรอง 1</label>
                                            <input type="text" class="form-control @error('other_title1') is-invalid @enderror" id="other_title1" name="other_title1" value="{{ old('other_title1', $item->other_title1) }}">
                                            @error('other_title1')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="other_title2" class="form-label">ชื่อรอง 2</label>
                                            <input type="text" class="form-control @error('other_title2') is-invalid @enderror" id="other_title2" name="other_title2" value="{{ old('other_title2', $item->other_title2) }}">
                                            @error('other_title2')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="other_title3" class="form-label">ชื่อรอง 3</label>
                                            <input type="text" class="form-control @error('other_title3') is-invalid @enderror" id="other_title3" name="other_title3" value="{{ old('other_title3', $item->other_title3) }}">
                                            @error('other_title3')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">คำอธิบาย</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="4">{{ old('description', $item->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="brief" class="form-label">สรุปย่อ</label>
                                    <textarea class="form-control @error('brief') is-invalid @enderror" id="brief" name="brief" rows="3">{{ old('brief', $item->brief) }}</textarea>
                                    @error('brief')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="year" class="form-label">ปี</label>
                                    <input type="text" class="form-control @error('year') is-invalid @enderror" id="year" name="year" value="{{ old('year', $item->year) }}">
                                    @error('year')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- ข้อมูลผู้สร้าง -->
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลผู้สร้าง</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="creator" class="form-label">ผู้สร้าง</label>
                                    <input type="text" class="form-control @error('creator') is-invalid @enderror" id="creator" name="creator" value="{{ old('creator', $item->creator) }}">
                                    @error('creator')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="author" class="form-label">ผู้แต่ง</label>
                                    <input type="text" class="form-control @error('author') is-invalid @enderror" id="author" name="author" value="{{ old('author', $item->author) }}">
                                    @error('author')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="contributor" class="form-label">ผู้มีส่วนร่วม</label>
                                    <input type="text" class="form-control @error('contributor') is-invalid @enderror" id="contributor" name="contributor" value="{{ old('contributor', $item->contributor) }}">
                                    @error('contributor')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลเพิ่มเติม -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลเพิ่มเติม</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">หมวดหมู่</label>
                                    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                        <option value="">-- เลือกหมวดหมู่ --</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id', $item->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="item_type_id" class="form-label">ประเภทรายการ</label>
                                    <select class="form-select @error('item_type_id') is-invalid @enderror" id="item_type_id" name="item_type_id">
                                        <option value="">-- เลือกประเภทรายการ --</option>
                                        @foreach($itemTypes as $type)
                                            <option value="{{ $type->id }}" {{ old('item_type_id', $item->item_type_id) == $type->id ? 'selected' : '' }}>
                                                {{ $type->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('item_type_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="material_id" class="form-label">วัสดุ</label>
                                    <select class="form-select @error('material_id') is-invalid @enderror" id="material_id" name="material_id">
                                        <option value="">-- เลือกวัสดุ --</option>
                                        @foreach($materials as $material)
                                            <option value="{{ $material->id }}" {{ old('material_id', $item->material_id) == $material->id ? 'selected' : '' }}>
                                                {{ $material->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('material_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="quantity" class="form-label">จำนวน</label>
                                    <input type="text" class="form-control @error('quantity') is-invalid @enderror" id="quantity" name="quantity" value="{{ old('quantity', $item->quantity) }}">
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="language_id" class="form-label">ภาษา</label>
                                    <select class="form-select @error('language_id') is-invalid @enderror" id="language_id" name="language_id">
                                        <option value="">-- เลือกภาษา --</option>
                                        @foreach($languages as $language)
                                            <option value="{{ $language->id }}" {{ old('language_id', $item->language_id) == $language->id ? 'selected' : '' }}>
                                                {{ $language->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('language_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="script_id" class="form-label">ตัวอักษร</label>
                                    <select class="form-select @error('script_id') is-invalid @enderror" id="script_id" name="script_id">
                                        <option value="">-- เลือกตัวอักษร --</option>
                                        @foreach($scripts as $script)
                                            <option value="{{ $script->id }}" {{ old('script_id', $item->script_id) == $script->id ? 'selected' : '' }}>
                                                {{ $script->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('script_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="manuscript_condition" class="form-label">สภาพรายการ</label>
                                    <input type="text" class="form-control @error('manuscript_condition') is-invalid @enderror" id="manuscript_condition" name="manuscript_condition" value="{{ old('manuscript_condition', $item->manuscript_condition) }}">
                                    @error('manuscript_condition')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="remark" class="form-label">หมายเหตุ</label>
                                    <textarea class="form-control @error('remark') is-invalid @enderror" id="remark" name="remark" rows="3">{{ old('remark', $item->remark) }}</textarea>
                                    @error('remark')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- ข้อมูลสถานที่ -->
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลสถานที่</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="location" class="form-label">สถานที่</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" id="location" name="location" value="{{ old('location', $item->location) }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="country" class="form-label">ประเทศ</label>
                                    <select class="form-select @error('country') is-invalid @enderror" id="country" name="country">
                                        <option value="">-- เลือกประเทศ --</option>
                                        @foreach($countries as $country)
                                            <option value="{{ strtolower($country->code) }}" {{ old('country', $item->country) == strtolower($country->code) ? 'selected' : '' }}>
                                                {{ $country->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="province" class="form-label">จังหวัด (เฉพาะประเทศไทย)</label>
                                    <select class="form-select @error('province') is-invalid @enderror" id="province" name="province" {{ $item->country != 'th' ? 'disabled' : '' }}>
                                        <option value="">-- เลือกจังหวัด --</option>
                                        @if($item->country == 'th')
                                            @foreach($provinces as $province)
                                                <option value="{{ $province->code }}" {{ old('province', $item->province) == $province->code || old('province', strtolower($item->province)) == strtolower($province->code) ? 'selected' : '' }}>
                                                    {{ $province->name_th }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('province')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                @livewire('coordinate-validator', ['latitude' => old('latitude', $item->latitude), 'longitude' => old('longitude', $item->longitude)])
                            </div>
                        </div>
                    </div>

                <!-- ไฟล์และรูปภาพ -->
                    <div class="col-12 mt-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">อัพโหลดไฟล์และรูปภาพ</h6>
                                <p class="card-subtitle text-muted">อัพโหลดไฟล์รูปภาพ เอกสาร เสียง และวิดีโอ</p>
                            </div>
                            <div class="card-body">
                                <!-- File Upload Info -->
                                <div class="alert alert-info mb-3">
                                    <div class="d-flex">
                                        <div class="me-2">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div>
                                            <strong>อัพโหลดไฟล์ได้หลายประเภทพร้อมกัน</strong> ระบบจะแยกประเภทไฟล์ให้อัตโนมัติ
                                            <div class="mt-2 small">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <ul class="mb-0 ps-3">
                                                            <li><i class="fas fa-image text-primary me-1"></i> รูปภาพ - JPG, PNG, GIF, WEBP</li>
                                                            <li><i class="fas fa-file-pdf text-danger me-1"></i> เอกสาร - PDF, DOC, DOCX</li>
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <ul class="mb-0 ps-3">
                                                            <li><i class="fas fa-music text-success me-1"></i> เสียง - MP3, WAV, OGG</li>
                                                            <li><i class="fas fa-video text-warning me-1"></i> วิดีโอ - MP4, WEBM, MOV</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <!-- Dropzone container -->
                                <div id="dropzone-container">
                                    <form id="dropzone-upload" class="dropzone" action="{{ route('admin.items.upload-file-new') }}">
                                        @csrf
                                        <div class="dz-message" data-dz-message>
                                            <i class="fas fa-cloud-upload-alt mb-2" style="font-size: 2rem; color: #4e73df;"></i><br>
                                            <span><i class="fas fa-file-upload me-1"></i> ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br>
                                            <span class="text-muted small">รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์)</span>
                                        </div>
                                    </form>
                                </div>

                                <hr>

                                <div class="mt-4">
                                    <h6 class="fw-bold mb-3">รูปภาพที่อัพโหลดแล้ว</h6>
                                    @livewire('item-image-manager', ['item' => $item])
                                </div>

                                <hr>

                                <div class="mt-4">
                                    <h6 class="fw-bold mb-3">ไฟล์ที่อัพโหลดแล้ว</h6>
                                    @livewire('item-file-manager', ['item' => $item])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields for uploaded files -->
                <input type="hidden" name="uploaded_images" id="uploaded_images" value="">
                <input type="hidden" name="uploaded_files" id="uploaded_files" value="">

                <div class="d-flex justify-content-end mt-4">
                    <button type="reset" class="btn btn-secondary me-2">
                        <i class="fas fa-undo me-2"></i>รีเซ็ต
                    </button>
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- jQuery (required for AJAX operations) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Dropzone JS -->
<script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>

<!-- Location Selector Script -->
<script src="{{ asset('js/location-selector.js') }}?v={{ time() }}"></script>

<!-- Custom Dropzone Configuration -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Arrays to store uploaded files
        let uploadedImages = [];
        let uploadedFiles = [];
        let dropzone;

        // Add global event listener for upload-queue-complete
        window.addEventListener('upload-queue-complete', function() {
            console.log('Received upload-queue-complete event');
            setTimeout(function() {
                // Force refresh the page components after a short delay
                if (window.Livewire) {
                    Livewire.dispatch('refreshImages');
                    Livewire.dispatch('refreshFiles');
                }
            }, 500);
        });

        // Function to update hidden fields
        function updateHiddenFields() {
            document.getElementById('uploaded_images').value = JSON.stringify(uploadedImages);
            document.getElementById('uploaded_files').value = JSON.stringify(uploadedFiles);
            console.log('Updated hidden fields:');
            console.log('Images:', uploadedImages);
            console.log('Files:', uploadedFiles);
        }

        // Listen for image updates from Livewire
        window.addEventListener('imagesUpdated', function(event) {
            // Get images from Livewire component
            if (window.Livewire) {
                const imageComponents = Livewire.all().filter(component =>
                    component.name === 'item-image-manager' ||
                    (component.snapshot && component.snapshot.memo &&
                     (component.snapshot.memo.name === 'item-image-manager' ||
                      component.snapshot.memo.name === 'ItemImageManager'))
                );

                if (imageComponents.length > 0) {
                    const imageComponent = imageComponents[0];
                    if (imageComponent.tempImages) {
                        uploadedImages = [...imageComponent.tempImages];
                        updateHiddenFields();
                    }
                }
            }
        });

        // Listen for file updates from Livewire
        window.addEventListener('filesUpdated', function(event) {
            // Get files from Livewire component
            if (window.Livewire) {
                const fileComponents = Livewire.all().filter(component =>
                    component.name === 'item-file-manager' ||
                    (component.snapshot && component.snapshot.memo &&
                     (component.snapshot.memo.name === 'item-file-manager' ||
                      component.snapshot.memo.name === 'ItemFileManager'))
                );

                if (fileComponents.length > 0) {
                    const fileComponent = fileComponents[0];
                    if (fileComponent.tempFiles) {
                        uploadedFiles = [...fileComponent.tempFiles];
                        updateHiddenFields();
                    }
                }
            }
        });

        // Initialize Dropzone
        function initializeDropzone() {
            try {
                console.log('Initializing Dropzone...');

                // Get the container element
                const container = document.getElementById('dropzone-container');
                let element;

                // Check if the form element already exists
                element = document.getElementById('dropzone-upload');

                if (!element) {
                    console.log('Creating new dropzone element...');

                    if (container) {
                        element = document.createElement('form');
                        element.id = 'dropzone-upload';
                        element.className = 'dropzone';
                        element.action = '{{ route('admin.items.upload-file-new') }}';

                        // Add CSRF token
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = '_token';
                        csrfInput.value = '{{ csrf_token() }}';
                        element.appendChild(csrfInput);

                        // Add message div
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'dz-message';
                        messageDiv.setAttribute('data-dz-message', '');
                        messageDiv.innerHTML = '<i class="fas fa-cloud-upload-alt mb-2" style="font-size: 2rem; color: #4e73df;"></i><br><span><i class="fas fa-file-upload me-1"></i> ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์)</span>';
                        element.appendChild(messageDiv);

                        // Clear container and add the new form
                        container.innerHTML = '';
                        container.appendChild(element);

                        console.log('Created new dropzone form element:', element);
                    } else {
                        console.error('Dropzone container not found: #dropzone-container');
                        return;
                    }
                }

                // Disable Dropzone auto-discover
                Dropzone.autoDiscover = false;

                // Create Dropzone instance with configuration
                const dropzoneOptions = {
                    url: '{{ route('admin.items.upload-file-new') }}',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'X-File-Type': 'mixed'
                    },
                    paramName: 'file', // The name that will be used to transfer the file
                    maxFilesize: 50, // MB
                    maxFiles: null, // ไม่จำกัดจำนวนไฟล์
                    acceptedFiles: 'image/*,application/pdf,audio/*,video/*,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    dictDefaultMessage: '<i class="fas fa-cloud-upload-alt mb-2" style="font-size: 2rem; color: #4e73df;"></i><br><span><i class="fas fa-file-upload me-1"></i> ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์)</span>',
                    addRemoveLinks: true,
                    dictRemoveFile: '×',
                    dictCancelUpload: '⊗',
                    dictCancelUploadConfirmation: 'ยกเลิกการอัพโหลดนี้?',
                    dictFileTooBig: 'ไฟล์ใหญ่เกินไป ขนาดไฟล์สูงสุดคือ: 50MB',
                    dictInvalidFileType: 'ไม่รองรับไฟล์ประเภทนี้',
                    dictMaxFilesExceeded: 'ไม่สามารถอัพโหลดเพิ่มได้',
                    autoProcessQueue: true,
                    createImageThumbnails: true,
                    thumbnailWidth: 120,
                    thumbnailHeight: 120
                };

                console.log('Creating Dropzone with options:', dropzoneOptions);

                // Create Dropzone instance
                try {
                    console.log('Creating Dropzone with element:', element);

                    // Always use the element directly
                    dropzone = new Dropzone(element, dropzoneOptions);
                    console.log('Dropzone instance created successfully:', dropzone);

                    // Force a redraw of the element
                    element.style.display = 'none';
                    setTimeout(() => {
                        element.style.display = 'block';
                        console.log('Dropzone element redisplayed');
                    }, 10);
                } catch (error) {
                    console.error('Error creating Dropzone with element:', error);

                    // Try with a simpler configuration
                    try {
                        console.log('Trying with simpler configuration');
                        const simpleOptions = {
                            url: '{{ route('admin.items.upload-file-new') }}',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            paramName: 'file',
                            maxFilesize: 50,
                            maxFiles: null, // ไม่จำกัดจำนวนไฟล์
                            acceptedFiles: 'image/*,application/pdf,audio/*,video/*'
                        };

                        dropzone = new Dropzone(element, simpleOptions);
                        console.log('Dropzone created with simpler configuration');
                    } catch (lastError) {
                        console.error('All attempts to create Dropzone failed:', lastError);

                        // Create a completely new element as last resort
                        try {
                            const container = document.querySelector('#dropzone-container');
                            if (container) {
                                container.innerHTML = '<div id="fallback-dropzone" class="border border-dashed p-5 text-center">ไม่สามารถโหลด Dropzone ได้ กรุณารีเฟรชหน้าเว็บ</div>';
                            }
                        } catch (e) {
                            console.error('Failed to create fallback element:', e);
                        }
                    }
                }

                // Add event listeners
                dropzone.on('success', function(file, response) {
                    console.log('File successfully uploaded:', file.name);
                    console.log('Server response:', response);

                    if (response && response.success) {
                        // Create fileData object from response
                        const fileData = response;

                        // Add file information if not present
                        fileData.name = fileData.name || file.name;
                        fileData.file_name = fileData.file_name || file.name;
                        fileData.size = fileData.size || file.size;
                        fileData.file_size = fileData.file_size || file.size;

                        // Get file extension
                        const extension = file.name.split('.').pop().toLowerCase();
                        fileData.extension = fileData.extension || extension;
                        fileData.file_extension = fileData.file_extension || extension;

                        // Determine if file is an image based on MIME type or extension
                        const isImageByType = file.type && file.type.startsWith('image/');
                        const isImageByExt = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension);
                        fileData.is_image = fileData.is_image !== undefined ? fileData.is_image : (isImageByType || isImageByExt);

                        console.log('Processed file data:', fileData);

                        // Call the file processed handler
                        handleFileProcessed(fileData, file);

                        // Add the server response to the file
                        file.serverResponse = response;
                    }
                });

                dropzone.on('error', function(file, errorMessage, xhr) {
                    console.error('Error uploading file:', file.name);
                    console.error('Error message:', errorMessage);

                    // Display error message on the file preview
                    if (typeof errorMessage === 'string') {
                        const errorElement = document.createElement('div');
                        errorElement.className = 'alert alert-danger';
                        errorElement.style.fontSize = '0.8rem';
                        errorElement.style.padding = '5px';
                        errorElement.style.textAlign = 'center';
                        errorElement.style.position = 'absolute';
                        errorElement.style.top = '150px';
                        errorElement.style.left = '-40px';
                        errorElement.style.width = '200px';
                        errorElement.style.zIndex = '30';
                        errorElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
                        errorElement.innerHTML = errorMessage;
                        file.previewElement.appendChild(errorElement);
                    } else if (errorMessage && errorMessage.error) {
                        const errorElement = document.createElement('div');
                        errorElement.className = 'alert alert-danger';
                        errorElement.style.fontSize = '0.8rem';
                        errorElement.style.padding = '5px';
                        errorElement.style.textAlign = 'center';
                        errorElement.style.position = 'absolute';
                        errorElement.style.top = '150px';
                        errorElement.style.left = '-40px';
                        errorElement.style.width = '200px';
                        errorElement.style.zIndex = '30';
                        errorElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
                        errorElement.innerHTML = errorMessage.error;
                        file.previewElement.appendChild(errorElement);
                    }
                });

                // Add custom event for file processed
                dropzone.on('complete', function(file) {
                    if (file.status === 'success' && file.serverResponse) {
                        window.dispatchEvent(new CustomEvent('dropzone-file-uploaded', {
                            detail: {
                                fileData: file.serverResponse,
                                file: file
                            }
                        }));
                    }
                });

                // Add event listener for when all files are processed
                dropzone.on('queuecomplete', function() {
                    console.log('All files have been processed');

                    // Force refresh Livewire components after all uploads are complete
                    if (window.Livewire) {
                        // Refresh image manager component
                        const imageComponents = document.querySelectorAll('[wire\\:id]');
                        imageComponents.forEach(component => {
                            const wireId = component.getAttribute('wire:id');
                            const livewireComponent = window.Livewire.find(wireId);

                            if (livewireComponent &&
                                (component.classList.contains('item-image-manager') ||
                                component.querySelector('.item-image-manager'))) {
                                console.log('Refreshing ItemImageManager component');
                                livewireComponent.call('refreshImages');
                            }

                            if (livewireComponent &&
                                (component.classList.contains('item-file-manager') ||
                                component.querySelector('.item-file-manager'))) {
                                console.log('Refreshing ItemFileManager component');
                                livewireComponent.call('refreshFiles');
                            }
                        });

                        // Dispatch global event to refresh all components
                        window.dispatchEvent(new CustomEvent('upload-queue-complete'));
                    }
                });
            } catch (error) {
                console.error('Error initializing Dropzone:', error);
            }
        }

        // Handle processed files
        function handleFileProcessed(fileData, file) {
            console.log('File processed:', fileData);

            // ตรวจสอบประเภทไฟล์จาก MIME type หรือนามสกุลไฟล์
            const fileType = fileData.type || fileData.file_type || file.fileType || '';
            const fileExtension = (fileData.extension || fileData.file_extension ||
                                  (file.filename ? file.filename.split('.').pop() : '') || '').toLowerCase();

            // ตรวจสอบว่าเป็นรูปภาพหรือไม่
            const isImageByMime = fileType.startsWith('image/');
            const isImageByExt = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension);
            const isImageFile = isImageByMime || isImageByExt || fileData.is_image === true;

            console.log('File type detection:', {
                fileType,
                fileExtension,
                isImageByMime,
                isImageByExt,
                isImageFile
            });

            // อัพเดทค่า is_image ในข้อมูลไฟล์
            fileData.is_image = isImageFile;

            if (isImageFile) {
                // เป็นรูปภาพ ให้เพิ่มในอาร์เรย์รูปภาพ
                const imageExists = uploadedImages.some(img => img.path === fileData.path);
                if (!imageExists) {
                    console.log('Adding new image to uploadedImages array:', fileData);
                    uploadedImages.push(fileData);
                    updateHiddenFields();

                    // ส่งข้อมูลไปยัง ItemImageManager component โดยตรง
                    try {
                        const imageManagerComponents = document.querySelectorAll('[wire\\:id]');
                        imageManagerComponents.forEach(component => {
                            const wireId = component.getAttribute('wire:id');
                            const livewireComponent = window.Livewire.find(wireId);

                            if (livewireComponent &&
                                (component.classList.contains('item-image-manager') ||
                                component.querySelector('.item-image-manager'))) {
                                console.log('Found ItemImageManager component, sending image data');
                                livewireComponent.call('handleUppyUpload', fileData);
                            }
                        });
                    } catch (e) {
                        console.error('Error sending image data to Livewire component:', e);
                    }
                }
            } else {
                // ไม่ใช่รูปภาพ ให้เพิ่มในอาร์เรย์ไฟล์
                // ตรวจสอบไฟล์ซ้ำอย่างละเอียด
                let fileExists = false;

                // ตรวจสอบจาก path
                if (fileData.path) {
                    fileExists = uploadedFiles.some(f => f.path === fileData.path);
                }

                // ตรวจสอบจากชื่อไฟล์ถ้ายังไม่พบ
                if (!fileExists && fileData.name) {
                    fileExists = uploadedFiles.some(f => f.name === fileData.name || f.file_name === fileData.name);
                }

                // ตรวจสอบจาก file_name ถ้ายังไม่พบ
                if (!fileExists && fileData.file_name) {
                    fileExists = uploadedFiles.some(f => f.name === fileData.file_name || f.file_name === fileData.file_name);
                }

                if (!fileExists) {
                    console.log('Adding new file to uploadedFiles array:', fileData);
                    uploadedFiles.push(fileData);
                    updateHiddenFields();

                    // ส่งข้อมูลไปยัง ItemFileManager component
                    try {
                        const fileManagerComponents = document.querySelectorAll('[wire\\:id]');
                        fileManagerComponents.forEach(component => {
                            const wireId = component.getAttribute('wire:id');
                            const livewireComponent = window.Livewire.find(wireId);

                            if (livewireComponent &&
                                (component.classList.contains('item-file-manager') ||
                                 component.querySelector('.item-file-manager'))) {
                                livewireComponent.call('handleUppyUpload', fileData);
                            }
                        });
                    } catch (e) {
                        console.error('Error sending file data to Livewire component:', e);
                    }
                }
            }
        }

        // Initialize Dropzone when the DOM is loaded
        initializeDropzone();

        // Form submission
        document.getElementById('item-edit-form').addEventListener('submit', function(e) {
            console.log('Form submission started');
            console.log('Form action:', this.action);
            console.log('Form method:', this.method);

            // Update hidden fields before submission
            updateHiddenFields();

            // Log form data for debugging
            const formData = new FormData(this);
            console.log('Form data being submitted:');
            for (const pair of formData.entries()) {
                console.log(`${pair[0]}: ${pair[1]}`);
            }

            // Check if form has required fields
            const title = document.getElementById('title').value;
            if (!title || title.trim() === '') {
                console.error('Title is required but empty');
                e.preventDefault();
                alert('กรุณากรอกชื่อรายการ');
                return false;
            }

            console.log('Form validation passed, submitting...');

            // Show loading state
            const submitBtn = document.getElementById('submit-btn');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';
            }

            // Add a timeout to re-enable button if something goes wrong
            setTimeout(function() {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง';
                }
            }, 10000); // 10 seconds timeout
        });

        // Also handle the submit button click directly
        document.getElementById('submit-btn').addEventListener('click', function(e) {
            console.log('Submit button clicked');

            // Check if form is valid
            const form = document.getElementById('item-edit-form');
            if (!form.checkValidity()) {
                console.log('Form validation failed');
                e.preventDefault();
                form.reportValidity();
                return false;
            }

            console.log('Form is valid, proceeding with submission');

            // Manually trigger form submission if needed
            setTimeout(function() {
                console.log('Manually submitting form...');
                form.submit();
            }, 100);
        });

        // Add error handling for form submission
        window.addEventListener('beforeunload', function(e) {
            console.log('Page is about to unload');
        });

        // Check for any JavaScript errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.error);
            console.error('Error message:', e.message);
            console.error('Error filename:', e.filename);
            console.error('Error line:', e.lineno);
        });
    });
</script>
@endsection
