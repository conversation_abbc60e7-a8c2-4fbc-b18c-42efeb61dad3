@extends('layouts.admin')

@section('title', 'เพิ่มรายการใหม่ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('styles')
<!-- Uppy CSS -->
<link href="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.css" rel="stylesheet">

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
<style>
    .uppy-Dashboard-inner {
        width: 100% !important;
        height: 400px !important;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">เพิ่มรายการใหม่</h1>
        <a href="{{ route('admin.items') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการทั้งหมด
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">ข้อมูลรายการ</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.items.store') }}" method="POST" enctype="multipart/form-data" id="item-form">
                @csrf
                <!-- ส่วนของฟอร์มอื่นๆ ยังคงเหมือนเดิม -->
                
                <!-- Hidden fields for uploaded files -->
                <input type="hidden" name="uploaded_images" id="uploaded_images" value="">
                <input type="hidden" name="uploaded_files" id="uploaded_files" value="">

                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="btn btn-primary" id="submit-form">
                        <i class="fas fa-save me-2"></i>บันทึกรายการ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
