@extends('layouts.admin')

@section('title', 'เพิ่มรายการใหม่ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('styles')
<!-- Admin Forms CSS -->
<link rel="stylesheet" href="{{ asset('css/admin-forms.css') }}?v={{ time() }}" type="text/css" />
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">เพิ่มรายการใหม่</h1>
        <a href="{{ route('admin.items') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการทั้งหมด
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-2  ">ข้อมูลรายการ</h5>
            <p class="card-subtitle text-muted">กรอกข้อมูลรายการใหม่ที่ต้องการเพิ่มเข้าสู่ระบบ</p>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.items.store') }}" method="POST" enctype="multipart/form-data" id="item-form">
                @csrf
                <div class="row">
                    <!-- ข้อมูลพื้นฐาน -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลพื้นฐาน</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">ชื่อรายการ <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    @livewire('item-identifier-checker')
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="other_title1" class="form-label">ชื่อรอง 1</label>
                                            <input type="text" class="form-control @error('other_title1') is-invalid @enderror" id="other_title1" name="other_title1" value="{{ old('other_title1') }}">
                                            @error('other_title1')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="other_title2" class="form-label">ชื่อรอง 2</label>
                                            <input type="text" class="form-control @error('other_title2') is-invalid @enderror" id="other_title2" name="other_title2" value="{{ old('other_title2') }}">
                                            @error('other_title2')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="other_title3" class="form-label">ชื่อรอง 3</label>
                                            <input type="text" class="form-control @error('other_title3') is-invalid @enderror" id="other_title3" name="other_title3" value="{{ old('other_title3') }}">
                                            @error('other_title3')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">คำอธิบาย</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="4">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="brief" class="form-label">สรุปย่อ</label>
                                    <textarea class="form-control @error('brief') is-invalid @enderror" id="brief" name="brief" rows="3">{{ old('brief') }}</textarea>
                                    @error('brief')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="year" class="form-label">ปี</label>
                                    <input type="text" class="form-control @error('year') is-invalid @enderror" id="year" name="year" value="{{ old('year') }}">
                                    @error('year')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- ข้อมูลผู้สร้าง -->
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลผู้สร้าง</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="creator" class="form-label">ผู้สร้าง</label>
                                    <input type="text" class="form-control @error('creator') is-invalid @enderror" id="creator" name="creator" value="{{ old('creator') }}">
                                    @error('creator')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="author" class="form-label">ผู้แต่ง</label>
                                    <input type="text" class="form-control @error('author') is-invalid @enderror" id="author" name="author" value="{{ old('author') }}">
                                    @error('author')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="contributor" class="form-label">ผู้มีส่วนร่วม</label>
                                    <input type="text" class="form-control @error('contributor') is-invalid @enderror" id="contributor" name="contributor" value="{{ old('contributor') }}">
                                    @error('contributor')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลเพิ่มเติม -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลเพิ่มเติม</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">หมวดหมู่</label>
                                    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                        <option value="">-- เลือกหมวดหมู่ --</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="item_type_id" class="form-label">ประเภทรายการ</label>
                                    <select class="form-select @error('item_type_id') is-invalid @enderror" id="item_type_id" name="item_type_id">
                                        <option value="">-- เลือกประเภทรายการ --</option>
                                        @foreach($itemTypes as $type)
                                            <option value="{{ $type->id }}" {{ old('item_type_id') == $type->id ? 'selected' : '' }}>
                                                {{ $type->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('item_type_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="material_id" class="form-label">วัสดุ</label>
                                    <select class="form-select @error('material_id') is-invalid @enderror" id="material_id" name="material_id">
                                        <option value="">-- เลือกวัสดุ --</option>
                                        @foreach($materials as $material)
                                            <option value="{{ $material->id }}" {{ old('material_id') == $material->id ? 'selected' : '' }}>
                                                {{ $material->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('material_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="quantity" class="form-label">จำนวน</label>
                                    <input type="text" class="form-control @error('quantity') is-invalid @enderror" id="quantity" name="quantity" value="{{ old('quantity') }}">
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="language_id" class="form-label">ภาษา</label>
                                    <select class="form-select @error('language_id') is-invalid @enderror" id="language_id" name="language_id">
                                        <option value="">-- เลือกภาษา --</option>
                                        @foreach($languages as $language)
                                            <option value="{{ $language->id }}" {{ old('language_id') == $language->id ? 'selected' : '' }}>
                                                {{ $language->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('language_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="script_id" class="form-label">ตัวอักษร</label>
                                    <select class="form-select @error('script_id') is-invalid @enderror" id="script_id" name="script_id">
                                        <option value="">-- เลือกตัวอักษร --</option>
                                        @foreach($scripts as $script)
                                            <option value="{{ $script->id }}" {{ old('script_id') == $script->id ? 'selected' : '' }}>
                                                {{ $script->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('script_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="manuscript_condition" class="form-label">สภาพรายการ</label>
                                    <input type="text" class="form-control @error('manuscript_condition') is-invalid @enderror" id="manuscript_condition" name="manuscript_condition" value="{{ old('manuscript_condition') }}">
                                    @error('manuscript_condition')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="remark" class="form-label">หมายเหตุ</label>
                                    <textarea class="form-control @error('remark') is-invalid @enderror" id="remark" name="remark" rows="3">{{ old('remark') }}</textarea>
                                    @error('remark')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- ข้อมูลสถานที่ -->
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">ข้อมูลสถานที่</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="location" class="form-label">สถานที่</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" id="location" name="location" value="{{ old('location') }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="country" class="form-label">ประเทศ</label>
                                    <select class="form-select @error('country') is-invalid @enderror" id="country" name="country">
                                        <option value="">-- เลือกประเทศ --</option>
                                        @foreach($countries as $country)
                                            <option value="{{ strtolower($country->code) }}" {{ old('country') == strtolower($country->code) ? 'selected' : '' }}>
                                                {{ $country->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="province" class="form-label">จังหวัด (เฉพาะประเทศไทย)</label>
                                    <select class="form-select @error('province') is-invalid @enderror" id="province" name="province" disabled>
                                        <option value="">-- เลือกจังหวัด --</option>
                                        @if(old('country') == 'th')
                                            @foreach($provinces as $province)
                                                <option value="{{ $province->code }}" {{ old('province') == $province->code ? 'selected' : '' }}>
                                                    {{ $province->name_th }}
                                                </option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('province')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                @livewire('coordinate-validator', ['latitude' => old('latitude'), 'longitude' => old('longitude')])
                            </div>
                        </div>
                    </div>

                    <!-- ไฟล์และรูปภาพ -->
                    <div class="col-12 mt-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-white py-3">
                                <h6 class="card-title mb-0">อัพโหลดไฟล์และรูปภาพ</h6>
                                <p class="card-subtitle text-muted">อัพโหลดไฟล์รูปภาพ เอกสาร เสียง และวิดีโอ</p>
                            </div>
                            <div class="card-body">
                                <!-- File Upload Info -->
                                <div class="alert alert-info mb-3">
                                    <div class="d-flex">
                                        <div class="me-2">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div>
                                            <strong>อัพโหลดไฟล์ได้หลายประเภทพร้อมกัน</strong> ระบบจะแยกประเภทไฟล์ให้อัตโนมัติ
                                            <div class="mt-2 small">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <ul class="mb-0 ps-3">
                                                            <li><i class="fas fa-image text-primary me-1"></i> รูปภาพ - JPG, PNG, GIF, WEBP</li>
                                                            <li><i class="fas fa-file-pdf text-danger me-1"></i> เอกสาร - PDF, DOC, DOCX</li>
                                                        </ul>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <ul class="mb-0 ps-3">
                                                            <li><i class="fas fa-music text-success me-1"></i> เสียง - MP3, WAV, OGG</li>
                                                            <li><i class="fas fa-video text-warning me-1"></i> วิดีโอ - MP4, WEBM, MOV</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="dropzone-container">
                                    <form action="{{ route('admin.items.upload-file-new') }}" class="dropzone" id="dropzone-upload">
                                        @csrf
                                        <div class="dz-message" data-dz-message>
                                            <i class="fas fa-cloud-upload-alt mb-2" style="font-size: 2rem; color: #4e73df;"></i><br>
                                            <span><i class="fas fa-file-upload me-1"></i> ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br>
                                            <span class="text-muted small">รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์)</span>
                                        </div>
                                    </form>
                                </div>

                                <hr>

                                <div class="mt-4">
                                    <h6 class="fw-bold mb-3">รูปภาพที่อัพโหลดแล้ว</h6>
                                    @livewire('item-image-manager', ['item' => null])
                                </div>

                                <hr>

                                <div class="mt-4">
                                    <h6 class="fw-bold mb-3">ไฟล์ที่อัพโหลดแล้ว</h6>
                                    @livewire('item-file-manager', ['item' => null])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields for uploaded files -->
                <input type="hidden" name="uploaded_images" id="uploaded_images" value="[]">
                <input type="hidden" name="uploaded_files" id="uploaded_files" value="[]">

                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="btn btn-primary" id="submit-form">
                        <i class="fas fa-save me-2"></i>บันทึกรายการ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- jQuery (required for AJAX operations) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Vite Assets (includes Dropzone) -->
@vite(['resources/js/app.js'])

<!-- Dropzone Config -->
<script src="{{ asset('js/dropzone-config.js') }}?v={{ time() }}"></script>

<!-- Location Selector Script -->
<script src="{{ asset('js/location-selector.js') }}?v={{ time() }}"></script>

<!-- Custom Document Create JS -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Arrays to store uploaded files
        let uploadedImages = [];
        let uploadedFiles = [];

        // Function to update hidden fields
        function updateHiddenFields() {
            // Make sure we have arrays
            if (!Array.isArray(uploadedImages)) {
                console.warn('uploadedImages is not an array, initializing empty array');
                uploadedImages = [];
            }

            if (!Array.isArray(uploadedFiles)) {
                console.warn('uploadedFiles is not an array, initializing empty array');
                uploadedFiles = [];
            }

            // ตรวจสอบข้อมูลในอาร์เรย์
            console.log('Before update - uploadedImages:', JSON.stringify(uploadedImages));
            console.log('Before update - uploadedFiles:', JSON.stringify(uploadedFiles));

            // ตรวจสอบว่ามีข้อมูลที่ไม่ถูกต้องหรือไม่
            if (uploadedImages.length > 0) {
                uploadedImages = uploadedImages.filter(img => {
                    if (!img || !img.path) {
                        console.warn('Found invalid image data, removing:', img);
                        return false;
                    }
                    return true;
                });
            }

            if (uploadedFiles.length > 0) {
                uploadedFiles = uploadedFiles.filter(file => {
                    if (!file || !file.path) {
                        console.warn('Found invalid file data, removing:', file);
                        return false;
                    }
                    return true;
                });
            }

            // ตรวจสอบว่ามีข้อมูลในอาร์เรย์หรือไม่
            if (uploadedImages.length === 0) {
                console.warn('No images found, setting empty array');
            }

            if (uploadedFiles.length === 0) {
                console.warn('No files found, setting empty array');
            }

            console.log('Updating hidden fields - current state:');
            console.log('Current uploadedImages:', uploadedImages);
            console.log('Current uploadedFiles:', uploadedFiles);

            // Check if we need to get data from Livewire components
            try {
                // Try to get temporary images from Livewire components
                if (window.Livewire) {
                    // Check for ItemImageManager component
                    const imageManagerComponent = window.Livewire.find(
                        document.querySelector('[wire\\:id]')?.getAttribute('wire:id')
                    );

                    if (imageManagerComponent && imageManagerComponent.get('tempImages')) {
                        const tempImages = imageManagerComponent.get('tempImages');
                        console.log('Found tempImages in Livewire component:', tempImages);

                        // If we have temp images from Livewire but none in our local array, use those
                        if (tempImages && tempImages.length > 0 && (!uploadedImages || uploadedImages.length === 0)) {
                            console.log('Using tempImages from Livewire component');
                            uploadedImages = tempImages;
                        }
                    }

                    // Similar check for ItemFileManager component could be added here
                }
            } catch (error) {
                console.error('Error getting data from Livewire components:', error);
            }

            // Update hidden fields with JSON stringified arrays
            document.getElementById('uploaded_images').value = JSON.stringify(uploadedImages);
            document.getElementById('uploaded_files').value = JSON.stringify(uploadedFiles);

            console.log('Updated hidden fields:');
            console.log('Images count:', uploadedImages.length);
            console.log('Images:', uploadedImages);
            console.log('Files count:', uploadedFiles.length);
            console.log('Files:', uploadedFiles);

            // Verify the hidden fields were updated correctly
            console.log('Hidden field values:');
            console.log('uploaded_images:', document.getElementById('uploaded_images').value);
            console.log('uploaded_files:', document.getElementById('uploaded_files').value);

            // Double check that the JSON is valid
            try {
                JSON.parse(document.getElementById('uploaded_images').value);
                JSON.parse(document.getElementById('uploaded_files').value);
                console.log('JSON validation passed for both hidden fields');
            } catch (error) {
                console.error('JSON validation failed:', error);
            }
        }

        // ฟังก์ชันสำหรับอัพเดทข้อมูลจาก Livewire components
        function updateLivewireData() {
            if (window.Livewire) {
                // Find all Livewire components
                const components = document.querySelectorAll('[wire\\:id]');

                // แยกการดึงข้อมูลจาก components ให้ชัดเจน
                let imageManagerComponent = null;
                let fileManagerComponent = null;

                // ค้นหา components ที่ต้องการ
                components.forEach(component => {
                    const wireId = component.getAttribute('wire:id');
                    const livewireComponent = window.Livewire.find(wireId);

                    if (livewireComponent) {
                        // Check if it's an image manager
                        if (component.classList.contains('item-image-manager') ||
                            component.querySelector('.item-image-manager')) {
                            imageManagerComponent = livewireComponent;
                        }

                        // Check if it's a file manager
                        if (component.classList.contains('item-file-manager') ||
                            component.querySelector('.item-file-manager')) {
                            fileManagerComponent = livewireComponent;
                        }
                    }
                });

                // ดึงข้อมูลรูปภาพ
                if (imageManagerComponent) {
                    try {
                        const tempImages = imageManagerComponent.get('tempImages');
                        if (tempImages && tempImages.length > 0) {
                            // ตรวจสอบว่าเป็นรูปภาพจริงๆ
                            const validImages = tempImages.filter(img => {
                                // ตรวจสอบประเภทไฟล์จาก MIME type หรือนามสกุลไฟล์
                                const type = img.type || img.image_type || '';
                                const path = img.path || img.image_path || '';
                                const ext = path.split('.').pop().toLowerCase();

                                return type.startsWith('image/') ||
                                       ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext);
                            });

                            // อัพเดทข้อมูลในฟอร์ม
                            document.getElementById('uploaded_images').value = JSON.stringify(validImages);
                            console.log('Updated uploaded_images with', validImages.length, 'images');
                        }
                    } catch (e) {
                        console.error('Error getting tempImages:', e);
                    }
                }

                // ดึงข้อมูลไฟล์
                if (fileManagerComponent) {
                    try {
                        const tempFiles = fileManagerComponent.get('tempFiles');
                        if (tempFiles && tempFiles.length > 0) {
                            // ตรวจสอบว่าเป็นไฟล์ (ไม่ใช่รูปภาพ)
                            const validFiles = tempFiles.filter(file => {
                                // ตรวจสอบประเภทไฟล์จาก MIME type หรือนามสกุลไฟล์
                                const type = file.type || file.file_type || '';
                                const path = file.path || file.file_path || '';
                                const ext = path.split('.').pop().toLowerCase();

                                // ถ้าเป็นรูปภาพ ให้ข้าม
                                if (type.startsWith('image/') ||
                                    ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)) {
                                    return false;
                                }

                                return true;
                            });

                            // อัพเดทข้อมูลในฟอร์ม
                            document.getElementById('uploaded_files').value = JSON.stringify(validFiles);
                            console.log('Updated uploaded_files with', validFiles.length, 'files');
                        }
                    } catch (e) {
                        console.error('Error getting tempFiles:', e);
                    }
                }
            }
        }

        // อัพเดทข้อมูลทุก 2 วินาที
        setInterval(updateLivewireData, 2000);

        // อัพเดทข้อมูลครั้งแรก
        setTimeout(updateLivewireData, 500);

        // Listen for image updates from Livewire
        // For Livewire v3, we need to use Livewire.on() to listen for events
        if (window.Livewire) {
            // เมื่อ Livewire พร้อมใช้งาน
            document.addEventListener('livewire:initialized', function() {
                updateLivewireData();
            });

            // Listen for imagesUpdated event
            Livewire.on('imagesUpdated', (data) => {
                console.log('imagesUpdated event received from Livewire:', data);

                if (data && data.tempImages) {
                    console.log('tempImages found in event data:', data.tempImages);
                    uploadedImages = [...data.tempImages];
                    updateHiddenFields();
                } else {
                    console.warn('No tempImages found in event data');

                    // Fallback: try to get tempImages from Livewire component
                    const imageComponents = Livewire.all().filter(component => {
                        console.log('Checking component:', component);
                        return component.name === 'item-image-manager' ||
                               (component.snapshot && component.snapshot.memo &&
                               (component.snapshot.memo.name === 'item-image-manager' ||
                                component.snapshot.memo.name === 'ItemImageManager'));
                    });

                    console.log('Found image components:', imageComponents);

                    if (imageComponents.length > 0) {
                        const imageComponent = imageComponents[0];
                        console.log('Image component data:', imageComponent);

                        if (imageComponent.tempImages) {
                            console.log('tempImages found in component:', imageComponent.tempImages);
                            uploadedImages = [...imageComponent.tempImages];
                            updateHiddenFields();
                        } else {
                            console.warn('No tempImages found in component');
                        }
                    } else {
                        console.warn('No image components found');
                    }
                }
            });
        } else {
            console.warn('Livewire not available');

            // Fallback: use regular event listener
            window.addEventListener('imagesUpdated', function(event) {
                console.log('imagesUpdated DOM event received:', event);

                if (event.detail && event.detail.tempImages) {
                    console.log('tempImages found in event detail:', event.detail.tempImages);
                    uploadedImages = [...event.detail.tempImages];
                    updateHiddenFields();
                }
            });
        }

        // Listen for dropzone-file-uploaded event which contains image data
        window.addEventListener('dropzone-file-uploaded', function(event) {
            console.log('dropzone-file-uploaded event received', event.detail);

            const fileData = event.detail.fileData;

            // Check if this is an image based on the is_image flag
            if (fileData && fileData.is_image) {
                // This is an image
                // Check if this image is already in uploadedImages
                const imageExists = uploadedImages.some(img => img.path === fileData.path);
                if (!imageExists) {
                    uploadedImages.push(fileData);
                    updateHiddenFields();
                    console.log('Added image to uploadedImages from dropzone-file-uploaded event');
                }
            } else if (fileData && !fileData.is_image) {
                // This is a file, not an image
                // ตรวจสอบไฟล์ซ้ำอย่างละเอียด
                let fileExists = false;

                // ตรวจสอบจาก path
                if (fileData.path) {
                    fileExists = uploadedFiles.some(f => f.path === fileData.path);
                }

                // ตรวจสอบจากชื่อไฟล์ถ้ายังไม่พบ
                if (!fileExists && fileData.name) {
                    fileExists = uploadedFiles.some(f => f.name === fileData.name || f.file_name === fileData.name);
                }

                // ตรวจสอบจาก file_name ถ้ายังไม่พบ
                if (!fileExists && fileData.file_name) {
                    fileExists = uploadedFiles.some(f => f.name === fileData.file_name || f.file_name === fileData.file_name);
                }

                if (!fileExists) {
                    console.log('Adding file to uploadedFiles from dropzone-file-uploaded event:', fileData);
                    uploadedFiles.push(fileData);
                    updateHiddenFields();

                    // ลองส่งข้อมูลไฟล์ไปยัง ItemFileManager component อีกครั้ง
                    try {
                        if (typeof Livewire !== 'undefined' && typeof Livewire.dispatch === 'function') {
                            console.log('Dispatching file data to Livewire from event handler');
                            Livewire.dispatch('uppyFileUploaded', fileData);
                        }
                    } catch (e) {
                        console.error('Error dispatching file data from event handler:', e);
                    }
                } else {
                    console.log('File already exists in uploadedFiles, skipping:', fileData);
                }
            }
        });

        // เพิ่ม event listener สำหรับ uppyFileUploaded event
        document.addEventListener('uppyFileUploaded', function(event) {
            console.log('uppyFileUploaded DOM event received', event.detail);

            if (event.detail && event.detail.fileData) {
                const fileData = event.detail.fileData;

                // ตรวจสอบว่าเป็นไฟล์ (ไม่ใช่รูปภาพ)
                if (!fileData.is_image) {
                    // เพิ่มไฟล์ลงใน uploadedFiles array
                    const fileExists = uploadedFiles.some(f => f.path === fileData.path);
                    if (!fileExists) {
                        uploadedFiles.push(fileData);
                        updateHiddenFields();
                        console.log('Added file to uploadedFiles from uppyFileUploaded DOM event');
                    }
                }
            }
        });

        // Listen for file updates from Livewire
        // For Livewire v3, we need to use Livewire.on() to listen for events
        if (window.Livewire) {
            // Listen for filesUpdated event
            Livewire.on('filesUpdated', (data) => {
                console.log('filesUpdated event received from Livewire:', data);

                if (data && data.tempFiles) {
                    console.log('tempFiles found in event data:', data.tempFiles);
                    uploadedFiles = [...data.tempFiles];
                    updateHiddenFields();
                } else {
                    console.warn('No tempFiles found in event data');

                    // Fallback: try to get tempFiles from Livewire component
                    // ค้นหา component ด้วยวิธีที่ละเอียดมากขึ้น
                    const fileComponents = [];

                    // ค้นหาทุก Livewire component
                    document.querySelectorAll('[wire\\:id]').forEach(element => {
                        const wireId = element.getAttribute('wire:id');
                        const component = Livewire.find(wireId);

                        if (component) {
                            console.log('Checking component:', component, element);

                            // ตรวจสอบหลายวิธี
                            const isFileManagerByClass = element.classList.contains('item-file-manager');
                            const isFileManagerByChild = element.querySelector('.item-file-manager') !== null;
                            const isFileManagerByName = element.getAttribute('wire:initial-data') &&
                                JSON.parse(element.getAttribute('wire:initial-data')).fingerprint.name.includes('item-file-manager');

                            if (isFileManagerByClass || isFileManagerByChild || isFileManagerByName) {
                                console.log('Found ItemFileManager component by DOM check');
                                fileComponents.push(component);
                            }

                            // ตรวจสอบจาก component properties
                            if (component.name === 'item-file-manager' ||
                               (component.snapshot && component.snapshot.memo &&
                               (component.snapshot.memo.name === 'item-file-manager' ||
                                component.snapshot.memo.name === 'ItemFileManager'))) {
                                console.log('Found ItemFileManager component by component properties');
                                if (!fileComponents.includes(component)) {
                                    fileComponents.push(component);
                                }
                            }
                        }
                    });

                    console.log('Found file components:', fileComponents);

                    if (fileComponents.length > 0) {
                        const fileComponent = fileComponents[0];
                        console.log('File component data:', fileComponent);

                        if (fileComponent.tempFiles) {
                            console.log('tempFiles found in component:', fileComponent.tempFiles);
                            uploadedFiles = [...fileComponent.tempFiles];
                            updateHiddenFields();
                        } else {
                            console.warn('No tempFiles found in component');
                        }
                    } else {
                        console.warn('No file components found');
                    }
                }
            });
        } else {
            // Fallback: use regular event listener
            window.addEventListener('filesUpdated', function(event) {
                console.log('filesUpdated DOM event received:', event);

                if (event.detail && event.detail.tempFiles) {
                    console.log('tempFiles found in event detail:', event.detail.tempFiles);
                    uploadedFiles = [...event.detail.tempFiles];
                    updateHiddenFields();
                }
            });
        }

        // Also listen for dropzone-file-uploaded event which might contain file data
        window.addEventListener('dropzone-file-uploaded', function(event) {
            console.log('dropzone-file-uploaded event received for files', event.detail);

            const fileData = event.detail.fileData;

            // Check if this is not an image based on the is_image flag
            if (fileData && !fileData.is_image) {
                // This is a file, not an image
                // Check if this file is already in uploadedFiles
                const fileExists = uploadedFiles.some(f => f.path === fileData.path);
                if (!fileExists) {
                    uploadedFiles.push(fileData);
                    updateHiddenFields();
                    console.log('Added file to uploadedFiles from dropzone-file-uploaded event');
                }
            }
        });

        // Form validation and other functionality can be added here

        // Current file type - default to mixed (support all file types)
        let currentFileType = 'mixed';

        // Initialize Dropzone
        let dropzone;

        // File processed handler
        function handleFileProcessed(fileData, file) {
            console.log('File processed:', fileData, file);

            // Check if this is an image based on the is_image flag
            if (fileData && fileData.is_image) {
                // This is an image
                const imageExists = uploadedImages.some(img => img.path === fileData.path);
                if (!imageExists) {
                    uploadedImages.push(fileData);
                    updateHiddenFields();
                    console.log('Added image to uploadedImages');
                }
            } else if (fileData && !fileData.is_image) {
                // This is a file, not an image
                const fileExists = uploadedFiles.some(f => f.path === fileData.path);
                if (!fileExists) {
                    uploadedFiles.push(fileData);
                    updateHiddenFields();
                    console.log('Added file to uploadedFiles');
                }
            }
        }

        // Initialize Dropzone when the page is fully loaded
        window.addEventListener('load', function() {
            // Check if Dropzone is available
            if (typeof Dropzone !== 'undefined') {
                console.log('Dropzone is available, initializing...');
                setTimeout(initializeDropzone, 100); // Short delay to ensure everything is loaded
            } else {
                console.error('Dropzone not found');
                console.log('Dropzone available:', typeof Dropzone);

                // Try to load Dropzone from CDN
                var script = document.createElement('script');
                script.src = 'https://unpkg.com/dropzone@6.0.0-beta.2/dist/dropzone-min.js';
                script.onload = function() {
                    console.log('Dropzone loaded from CDN');
                    setTimeout(initializeDropzone, 100);
                };
                document.head.appendChild(script);
            }
        });

        function initializeDropzone() {
            try {
                console.log('Initializing Dropzone using config function...');

                // Use the dropzone config function
                if (typeof initDropzone === 'function') {
                    const selector = '#dropzone-upload';
                    const uploadUrl = '{{ route('admin.items.upload-file-new') }}';
                    const csrfToken = '{{ csrf_token() }}';
                    const fileType = 'mixed';

                    // Check if element exists
                    let element = document.querySelector(selector);
                    if (!element) {
                        // Create dropzone element if it doesn't exist
                        const container = document.querySelector('#dropzone-container');
                        if (container) {
                            element = document.createElement('form');
                            element.id = 'dropzone-upload';
                            element.className = 'dropzone';
                            element.action = uploadUrl;

                            // Add CSRF token
                            const csrfInput = document.createElement('input');
                            csrfInput.type = 'hidden';
                            csrfInput.name = '_token';
                            csrfInput.value = csrfToken;
                            element.appendChild(csrfInput);

                            container.appendChild(element);
                        }
                    }

                    if (element) {
                        dropzone = initDropzone(selector, uploadUrl, csrfToken, fileType, handleFileProcessed);
                        console.log('Dropzone initialized successfully using config function');
                    } else {
                        console.error('Could not create or find dropzone element');
                    }
                } else {
                    console.error('initDropzone function not available');
                    // Fallback to manual initialization
                    manualDropzoneInit();
                }
            } catch (error) {
                console.error('Error in initializeDropzone:', error);
                manualDropzoneInit();
            }
        }

        function manualDropzoneInit() {
            console.log('Falling back to manual Dropzone initialization...');

            // Check if the element exists - try different selectors
            let element = document.querySelector('#dropzone-upload');
            if (!element) {
                console.log('Dropzone element not found with #dropzone-upload, trying form.dropzone');
                element = document.querySelector('form.dropzone');
            }
            if (!element) {
                console.log('Dropzone element not found with form.dropzone, trying #dropzone-container .dropzone');
                element = document.querySelector('#dropzone-container .dropzone');
            }
            if (!element) {
                console.error('Dropzone element not found with any selector. Creating a new form element.');

                // Create a new form element if none exists
                const container = document.querySelector('#dropzone-container');
                    if (container) {
                        element = document.createElement('form');
                        element.id = 'dropzone-upload';
                        element.className = 'dropzone';
                        element.action = '{{ route('admin.items.upload-file-new') }}';

                        // Add CSRF token
                        const csrfInput = document.createElement('input');
                        csrfInput.type = 'hidden';
                        csrfInput.name = '_token';
                        csrfInput.value = '{{ csrf_token() }}';
                        element.appendChild(csrfInput);

                        // Add message div
                        const messageDiv = document.createElement('div');
                        messageDiv.className = 'dz-message';
                        messageDiv.setAttribute('data-dz-message', '');
                        messageDiv.innerHTML = '<span>ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</span><br><span class="text-muted small">รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์)</span>';
                        element.appendChild(messageDiv);

                        // Clear container and add the new form
                        container.innerHTML = '';
                        container.appendChild(element);

                        console.log('Created new dropzone form element:', element);
                    } else {
                        console.error('Dropzone container not found: #dropzone-container');
                        return;
                    }
                }

                // Disable Dropzone auto-discover
                Dropzone.autoDiscover = false;

                // Create Dropzone instance with configuration
                const dropzoneOptions = {
                    url: '{{ route('admin.items.upload-file-new') }}',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'X-File-Type': 'mixed'
                    },
                    paramName: 'file', // The name that will be used to transfer the file
                    maxFilesize: 50, // MB
                    maxFiles: 20,
                    acceptedFiles: 'image/*,application/pdf,audio/*,video/*,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    dictDefaultMessage: 'ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์<br>รองรับไฟล์รูปภาพ, เอกสาร, เสียง, วิดีโอ (สูงสุด 50MB ต่อไฟล์)',
                    addRemoveLinks: true,
                    dictRemoveFile: '×',
                    dictCancelUpload: '⊗',
                    dictCancelUploadConfirmation: 'ยกเลิกการอัพโหลดนี้?',
                    dictFileTooBig: 'ไฟล์ใหญ่เกินไป ขนาดไฟล์สูงสุดคือ: 50MB',
                    dictInvalidFileType: 'ไม่รองรับไฟล์ประเภทนี้',
                    dictMaxFilesExceeded: 'ไม่สามารถอัพโหลดเพิ่มได้ สูงสุด: 5 ไฟล์',
                    autoProcessQueue: true,
                    createImageThumbnails: true,
                    thumbnailWidth: 120,
                    thumbnailHeight: 120
                };

                console.log('Creating Dropzone with options:', dropzoneOptions);

                // Create Dropzone instance
                try {
                    console.log('Creating Dropzone with element:', element);

                    // Always use the element directly
                    dropzone = new Dropzone(element, dropzoneOptions);
                    console.log('Dropzone instance created successfully:', dropzone);

                    // Force a redraw of the element
                    element.style.display = 'none';
                    setTimeout(() => {
                        element.style.display = 'block';
                        console.log('Dropzone element redisplayed');
                    }, 10);
                } catch (error) {
                    console.error('Error creating Dropzone with element:', error);

                    // Try with a simpler configuration
                    try {
                        console.log('Trying with simpler configuration');
                        const simpleOptions = {
                            url: '{{ route('admin.items.upload-file-new') }}',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            paramName: 'file',
                            maxFilesize: 50,
                            maxFiles: 5,
                            acceptedFiles: 'image/*,application/pdf,audio/*,video/*'
                        };

                        dropzone = new Dropzone(element, simpleOptions);
                        console.log('Dropzone created with simpler configuration');
                    } catch (lastError) {
                        console.error('All attempts to create Dropzone failed:', lastError);

                        // Create a completely new element as last resort
                        try {
                            const container = document.querySelector('#dropzone-container');
                            if (container) {
                                container.innerHTML = '<div id="fallback-dropzone" class="border border-dashed p-5 text-center">ไม่สามารถโหลด Dropzone ได้ กรุณารีเฟรชหน้าเว็บ</div>';
                            }
                        } catch (e) {
                            console.error('Failed to create fallback element:', e);
                        }
                    }
                }
                console.log('Dropzone instance created:', dropzone);

                // Add event listeners
                dropzone.on('success', function(file, response) {
                    console.log('File successfully uploaded:', file.name);
                    console.log('Server response:', response);

                    if (response && response.success) {
                        // Create fileData object from response
                        const fileData = response;

                        // Add file information if not present
                        fileData.name = fileData.name || file.name;
                        fileData.file_name = fileData.file_name || file.name;
                        fileData.size = fileData.size || file.size;
                        fileData.file_size = fileData.file_size || file.size;

                        // Get file extension
                        const extension = file.name.split('.').pop().toLowerCase();
                        fileData.extension = fileData.extension || extension;
                        fileData.file_extension = fileData.file_extension || extension;

                        // Determine if file is an image based on MIME type or extension
                        const isImageByType = file.type && file.type.startsWith('image/');
                        const isImageByExt = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension);
                        fileData.is_image = fileData.is_image !== undefined ? fileData.is_image : (isImageByType || isImageByExt);

                        console.log('Processed file data:', fileData);

                        // Call the file processed handler
                        handleFileProcessed(fileData, file);

                        // Add the server response to the file
                        file.serverResponse = response;
                    }
                });

                dropzone.on('error', function(file, errorMessage, xhr) {
                    console.error('Error uploading file:', file.name);
                    console.error('Error message:', errorMessage);

                    // Display error message on the file preview
                    if (typeof errorMessage === 'string') {
                        const errorElement = document.createElement('div');
                        errorElement.className = 'alert alert-danger';
                        errorElement.style.fontSize = '0.8rem';
                        errorElement.style.padding = '5px';
                        errorElement.style.textAlign = 'center';
                        errorElement.style.position = 'absolute';
                        errorElement.style.top = '150px';
                        errorElement.style.left = '-40px';
                        errorElement.style.width = '200px';
                        errorElement.style.zIndex = '30';
                        errorElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
                        errorElement.innerHTML = errorMessage;
                        file.previewElement.appendChild(errorElement);
                    } else if (errorMessage && errorMessage.error) {
                        const errorElement = document.createElement('div');
                        errorElement.className = 'alert alert-danger';
                        errorElement.style.fontSize = '0.8rem';
                        errorElement.style.padding = '5px';
                        errorElement.style.textAlign = 'center';
                        errorElement.style.position = 'absolute';
                        errorElement.style.top = '150px';
                        errorElement.style.left = '-40px';
                        errorElement.style.width = '200px';
                        errorElement.style.zIndex = '30';
                        errorElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
                        errorElement.innerHTML = errorMessage.error;
                        file.previewElement.appendChild(errorElement);
                    }
                });

                // Add custom event for file processed
                dropzone.on('complete', function(file) {
                    if (file.status === 'success' && file.serverResponse) {
                        window.dispatchEvent(new CustomEvent('dropzone-file-uploaded', {
                            detail: {
                                fileData: file.serverResponse,
                                file: file
                            }
                        }));
                    }
                });
            } catch (error) {
                console.error('Error initializing Dropzone:', error);
            }
        }

        // Handle processed files
        function handleFileProcessed(fileData, file) {
            console.log('File processed:', fileData);

            // ตรวจสอบประเภทไฟล์จาก MIME type หรือนามสกุลไฟล์
            const fileType = fileData.type || fileData.file_type || file.fileType || '';
            const fileExtension = (fileData.extension || fileData.file_extension ||
                                  (file.filename ? file.filename.split('.').pop() : '') || '').toLowerCase();

            // ตรวจสอบว่าเป็นรูปภาพหรือไม่
            const isImageByMime = fileType.startsWith('image/');
            const isImageByExt = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension);
            const isImageFile = isImageByMime || isImageByExt || fileData.is_image === true;

            console.log('File type detection:', {
                fileType,
                fileExtension,
                isImageByMime,
                isImageByExt,
                isImageFile
            });

            // อัพเดทค่า is_image ในข้อมูลไฟล์
            fileData.is_image = isImageFile;

            if (isImageFile) {
                // เป็นรูปภาพ ให้เพิ่มในอาร์เรย์รูปภาพ
                const imageExists = uploadedImages.some(img => img.path === fileData.path);
                if (!imageExists) {
                    console.log('Adding new image to uploadedImages array:', fileData);
                    uploadedImages.push(fileData);
                    updateHiddenFields();

                    // ส่งข้อมูลไปยัง ItemImageManager component โดยตรง
                    try {
                        const imageManagerComponents = document.querySelectorAll('[wire\\:id]');
                        imageManagerComponents.forEach(component => {
                            const wireId = component.getAttribute('wire:id');
                            const livewireComponent = window.Livewire.find(wireId);

                            if (livewireComponent &&
                                (component.classList.contains('item-image-manager') ||
                                component.querySelector('.item-image-manager'))) {
                                console.log('Found ItemImageManager component, sending image data');
                                livewireComponent.call('handleUppyUpload', fileData);
                            }
                        });
                    } catch (e) {
                        console.error('Error sending image data to Livewire component:', e);
                    }
                }
            } else {
                // ไม่ใช่รูปภาพ ให้เพิ่มในอาร์เรย์ไฟล์
                // ตรวจสอบไฟล์ซ้ำอย่างละเอียด
                let fileExists = false;

                // ตรวจสอบจาก path
                if (fileData.path) {
                    fileExists = uploadedFiles.some(f => f.path === fileData.path);
                }

                // ตรวจสอบจากชื่อไฟล์ถ้ายังไม่พบ
                if (!fileExists && fileData.name) {
                    fileExists = uploadedFiles.some(f => f.name === fileData.name || f.file_name === fileData.name);
                }

                // ตรวจสอบจาก file_name ถ้ายังไม่พบ
                if (!fileExists && fileData.file_name) {
                    fileExists = uploadedFiles.some(f => f.name === fileData.file_name || f.file_name === fileData.file_name);
                }

                if (!fileExists) {
                    console.log('Adding new file to uploadedFiles array:', fileData);
                    uploadedFiles.push(fileData);
                    updateHiddenFields();

                    // ส่งข้อมูลไปยัง ItemFileManager component โดยตรง
                    try {
                        // ค้นหา ItemFileManager component ด้วยวิธีที่ตรงไปตรงมาที่สุด
                        const fileManagerElement = document.querySelector('.item-file-manager');
                        let foundFileManager = false;
                        let livewireComponent = null;

                        // วิธีที่ 1: ค้นหาจาก DOM โดยตรง
                        if (fileManagerElement) {
                            console.log('Found ItemFileManager element in DOM:', fileManagerElement);

                            // หา Livewire component ที่เกี่ยวข้อง
                            const closestWireElement = fileManagerElement.closest('[wire\\:id]');
                            if (closestWireElement) {
                                const wireId = closestWireElement.getAttribute('wire:id');
                                livewireComponent = window.Livewire.find(wireId);

                                if (livewireComponent) {
                                    console.log('Found ItemFileManager component by DOM traversal:', livewireComponent);
                                    foundFileManager = true;
                                }
                            }
                        }

                        // วิธีที่ 2: ค้นหาจาก Livewire components ทั้งหมด
                        if (!foundFileManager) {
                            console.log('Searching through all Livewire components...');

                            document.querySelectorAll('[wire\\:id]').forEach(component => {
                                if (foundFileManager) return; // ถ้าเจอแล้วให้ข้าม

                                const wireId = component.getAttribute('wire:id');
                                const componentInstance = window.Livewire.find(wireId);

                                if (!componentInstance) return; // ถ้าไม่มี component instance ให้ข้าม

                                // ตรวจสอบว่าเป็น ItemFileManager component หรือไม่
                                const initialData = component.getAttribute('wire:initial-data');
                                let isFileManager = false;

                                // ตรวจสอบจาก DOM
                                isFileManager = component.classList.contains('item-file-manager') ||
                                               component.querySelector('.item-file-manager') !== null;

                                // ตรวจสอบจาก initial data
                                if (!isFileManager && initialData) {
                                    try {
                                        const data = JSON.parse(initialData);
                                        isFileManager = data.fingerprint &&
                                                       data.fingerprint.name &&
                                                       (data.fingerprint.name.includes('item-file-manager') ||
                                                        data.fingerprint.name.includes('ItemFileManager'));
                                    } catch (e) {
                                        console.error('Error parsing initial data:', e);
                                    }
                                }

                                // ตรวจสอบจาก component properties
                                if (!isFileManager) {
                                    isFileManager = componentInstance.name === 'item-file-manager' ||
                                                   componentInstance.name === 'ItemFileManager' ||
                                                   (componentInstance.snapshot &&
                                                    componentInstance.snapshot.memo &&
                                                    (componentInstance.snapshot.memo.name === 'item-file-manager' ||
                                                     componentInstance.snapshot.memo.name === 'ItemFileManager'));
                                }

                                if (isFileManager) {
                                    console.log('Found ItemFileManager component by properties:', componentInstance);
                                    livewireComponent = componentInstance;
                                    foundFileManager = true;
                                }
                            });
                        }

                        // วิธีที่ 3: ค้นหาจาก Livewire.all()
                        if (!foundFileManager && typeof Livewire.all === 'function') {
                            console.log('Searching through Livewire.all()...');

                            Livewire.all().forEach(component => {
                                if (foundFileManager) return; // ถ้าเจอแล้วให้ข้าม

                                // ตรวจสอบจาก component name
                                const isFileManager = component.name === 'item-file-manager' ||
                                                     component.name === 'ItemFileManager' ||
                                                     (component.snapshot &&
                                                      component.snapshot.memo &&
                                                      (component.snapshot.memo.name === 'item-file-manager' ||
                                                       component.snapshot.memo.name === 'ItemFileManager'));

                                if (isFileManager) {
                                    console.log('Found ItemFileManager component in Livewire.all():', component);
                                    livewireComponent = component;
                                    foundFileManager = true;
                                }
                            });
                        }

                        // ส่งข้อมูลไฟล์ไปยัง Livewire component ที่พบ
                        if (foundFileManager && livewireComponent) {
                            console.log('Sending file data to ItemFileManager component:', fileData);

                            // ส่งข้อมูลไฟล์ไปยัง Livewire component
                            try {
                                // ตรวจสอบว่า component มีเมธอด handleUppyUpload หรือไม่
                                if (typeof livewireComponent.call === 'function') {
                                    console.log('Calling handleUppyUpload on component');
                                    livewireComponent.call('handleUppyUpload', fileData);

                                    // เพิ่ม delay เล็กน้อยเพื่อให้ Livewire มีเวลาประมวลผล
                                    setTimeout(() => {
                                        console.log('Refreshing files after delay');
                                        livewireComponent.call('refreshFiles');

                                        // ตรวจสอบอีกครั้งหลังจาก refresh
                                        setTimeout(() => {
                                            if (typeof livewireComponent.get === 'function') {
                                                const tempFiles = livewireComponent.get('tempFiles');
                                                console.log('Current tempFiles after refresh:', tempFiles);
                                            }
                                        }, 500);
                                    }, 500);
                                } else if (typeof livewireComponent.$wire !== 'undefined' &&
                                          typeof livewireComponent.$wire.call === 'function') {
                                    console.log('Using $wire.call instead');
                                    livewireComponent.$wire.call('handleUppyUpload', fileData);

                                    setTimeout(() => {
                                        livewireComponent.$wire.call('refreshFiles');
                                    }, 500);
                                } else {
                                    console.error('Component does not have call method:', livewireComponent);
                                }
                            } catch (e) {
                                console.error('Error calling Livewire methods:', e);
                            }
                        } else {
                            console.warn('No ItemFileManager component found on the page, trying direct dispatch');

                            // ลองใช้ Livewire.dispatch เพื่อส่งข้อมูลไฟล์โดยตรง
                            try {
                                if (typeof Livewire !== 'undefined' && typeof Livewire.dispatch === 'function') {
                                    console.log('Using Livewire.dispatch to send file data');
                                    Livewire.dispatch('handleFileUpload', { fileData: fileData });

                                    // ลองใช้ handleUppyFileUploaded โดยตรง
                                    Livewire.dispatch('uppyFileUploaded', fileData);

                                    // ลองใช้ event ที่ระบุใน ItemFileManager
                                    Livewire.dispatch('filesUpdated', { tempFiles: [fileData] });
                                }
                            } catch (e) {
                                console.error('Error using Livewire.dispatch:', e);
                            }

                            // ลองใช้ DOM event
                            try {
                                console.log('Dispatching DOM event for file upload');
                                document.dispatchEvent(new CustomEvent('uppyFileUploaded', {
                                    detail: { fileData: fileData }
                                }));
                            } catch (e) {
                                console.error('Error dispatching DOM event:', e);
                            }

                            // เพิ่มไฟล์ลงใน uploadedFiles array เพื่อให้แน่ใจว่าจะถูกส่งไปกับฟอร์ม
                            uploadedFiles.push(fileData);
                            updateHiddenFields();
                        }
                    } catch (e) {
                        console.error('Error sending file data to Livewire component:', e);

                        // เพิ่มไฟล์ลงใน uploadedFiles array เพื่อให้แน่ใจว่าจะถูกส่งไปกับฟอร์ม
                        uploadedFiles.push(fileData);
                        updateHiddenFields();
                    }
                }
            }
        }

        // Dropzone event listeners are now handled in the handleFileProcessed function
        // and set up in the initializeDropzone function

        // Dropzone handles uploads automatically, no need for upload button

        // Handle form submission
        document.getElementById('item-form').addEventListener('submit', function(e) {
            // Prevent the default form submission
            e.preventDefault();

            console.log('Form submission started - updating hidden fields');

            try {
                // Make sure hidden fields are updated with the latest data
                updateHiddenFields();

                // Log the data being submitted
                console.log('Form submission - uploaded_images:', document.getElementById('uploaded_images').value);
                console.log('Form submission - uploaded_files:', document.getElementById('uploaded_files').value);

                // Check if we have any images or files
                const hasImages = uploadedImages && uploadedImages.length > 0;
                const hasFiles = uploadedFiles && uploadedFiles.length > 0;

                console.log('Has images:', hasImages, 'Has files:', hasFiles);

                // Debug the actual content of the arrays
                console.log('uploadedImages array:', JSON.stringify(uploadedImages));
                console.log('uploadedFiles array:', JSON.stringify(uploadedFiles));

                // ตรวจสอบข้อมูลที่จะส่งไปยัง server
                const formData = new FormData(this);
                console.log('Form data entries:');
                for (const pair of formData.entries()) {
                    console.log(`Form data - ${pair[0]}: ${pair[1]}`);
                }
            } catch (error) {
                console.error('Error during form submission preparation:', error);
            }

            // ตรวจสอบว่า uploaded_images และ uploaded_files มีค่าหรือไม่
            const uploadedImagesValue = document.getElementById('uploaded_images').value;
            const uploadedFilesValue = document.getElementById('uploaded_files').value;

            // ตรวจสอบและแก้ไขค่า uploaded_images
            if (!uploadedImagesValue || uploadedImagesValue === '[]' || uploadedImagesValue === 'null') {
                console.warn('Empty uploaded_images value, setting to empty array');
                document.getElementById('uploaded_images').value = JSON.stringify([]);
            } else {
                // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                try {
                    const parsedImages = JSON.parse(uploadedImagesValue);
                    if (!Array.isArray(parsedImages)) {
                        console.warn('uploaded_images is not an array, fixing');
                        document.getElementById('uploaded_images').value = JSON.stringify(Array.isArray(uploadedImages) ? uploadedImages : []);
                    }
                } catch (e) {
                    console.error('Error parsing uploaded_images JSON:', e);
                    document.getElementById('uploaded_images').value = JSON.stringify(Array.isArray(uploadedImages) ? uploadedImages : []);
                }
            }

            // ตรวจสอบและแก้ไขค่า uploaded_files
            if (!uploadedFilesValue || uploadedFilesValue === '[]' || uploadedFilesValue === 'null') {
                console.warn('Empty uploaded_files value, setting to empty array');
                document.getElementById('uploaded_files').value = JSON.stringify([]);
            } else {
                // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                try {
                    const parsedFiles = JSON.parse(uploadedFilesValue);
                    if (!Array.isArray(parsedFiles)) {
                        console.warn('uploaded_files is not an array, fixing');
                        document.getElementById('uploaded_files').value = JSON.stringify(Array.isArray(uploadedFiles) ? uploadedFiles : []);
                    }
                } catch (e) {
                    console.error('Error parsing uploaded_files JSON:', e);
                    document.getElementById('uploaded_files').value = JSON.stringify(Array.isArray(uploadedFiles) ? uploadedFiles : []);
                }
            }

            // ตรวจสอบอีกครั้งว่าค่าที่จะส่งไปเป็น JSON ที่ถูกต้อง
            console.log('Final uploaded_images value:', document.getElementById('uploaded_images').value);
            console.log('Final uploaded_files value:', document.getElementById('uploaded_files').value);

            // If we don't have any images or files, show a warning
            if (!hasImages && !hasFiles) {
                console.warn('No images or files found in the form submission');
            }

            // Check for Livewire component data
            let livewireData = {};
            try {
                if (window.Livewire) {
                    // Try to get all Livewire components on the page
                    const components = document.querySelectorAll('[wire\\:id]');
                    components.forEach(component => {
                        const wireId = component.getAttribute('wire:id');
                        const componentName = component.getAttribute('wire:initial-data') ?
                            JSON.parse(component.getAttribute('wire:initial-data')).fingerprint.name : 'unknown';

                        const livewireComponent = window.Livewire.find(wireId);
                        if (livewireComponent) {
                            livewireData[componentName] = {
                                id: wireId,
                                data: {}
                            };

                            // Try to get tempImages and tempFiles
                            if (componentName.includes('image-manager') && livewireComponent.get('tempImages')) {
                                livewireData[componentName].data.tempImages = livewireComponent.get('tempImages');
                            }

                            if (componentName.includes('file-manager') && livewireComponent.get('tempFiles')) {
                                livewireData[componentName].data.tempFiles = livewireComponent.get('tempFiles');
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Error getting Livewire data:', error);
            }

            // Debug log to server
            fetch('/admin/debug-log', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    message: 'Form submission data',
                    uploadedImages: uploadedImages,
                    uploadedFiles: uploadedFiles,
                    hiddenFieldValues: {
                        uploaded_images: document.getElementById('uploaded_images').value,
                        uploaded_files: document.getElementById('uploaded_files').value
                    },
                    livewireData: livewireData
                })
            }).catch(error => console.error('Error sending debug log:', error));

            // Final check for Livewire component data
            try {
                if (window.Livewire) {
                    // Find all Livewire components
                    const components = document.querySelectorAll('[wire\\:id]');
                    let foundImageData = false;
                    let foundFileData = false;

                    components.forEach(component => {
                        const wireId = component.getAttribute('wire:id');
                        const livewireComponent = window.Livewire.find(wireId);

                        if (livewireComponent) {
                            // Check for image manager
                            if ((component.classList.contains('item-image-manager') ||
                                component.querySelector('.item-image-manager')) &&
                                livewireComponent.get('tempImages') &&
                                livewireComponent.get('tempImages').length > 0) {

                                console.log('Found tempImages in Livewire component before submit:', livewireComponent.get('tempImages'));

                                // If we don't have any images in our local array, use the Livewire ones
                                if (!uploadedImages || uploadedImages.length === 0) {
                                    console.log('Using tempImages from Livewire component for final submission');
                                    uploadedImages = livewireComponent.get('tempImages');
                                    document.getElementById('uploaded_images').value = JSON.stringify(uploadedImages);
                                    foundImageData = true;
                                }
                            }

                            // Check for file manager
                            if ((component.classList.contains('item-file-manager') ||
                                component.querySelector('.item-file-manager')) &&
                                livewireComponent.get('tempFiles') &&
                                livewireComponent.get('tempFiles').length > 0) {

                                console.log('Found tempFiles in Livewire component before submit:', livewireComponent.get('tempFiles'));

                                // If we don't have any files in our local array, use the Livewire ones
                                if (!uploadedFiles || uploadedFiles.length === 0) {
                                    console.log('Using tempFiles from Livewire component for final submission');
                                    uploadedFiles = livewireComponent.get('tempFiles');
                                    document.getElementById('uploaded_files').value = JSON.stringify(uploadedFiles);
                                    foundFileData = true;
                                }
                            }
                        }
                    });

                    console.log('Final check results - Found image data:', foundImageData, 'Found file data:', foundFileData);
                }
            } catch (error) {
                console.error('Error in final Livewire data check:', error);
            }

            // Submit the form
            this.submit();
        });

        // Also handle the submit button click
        document.getElementById('submit-form').addEventListener('click', function(e) {
            // Prevent the default button behavior
            e.preventDefault();

            // Make sure hidden fields are updated with the latest data
            updateHiddenFields();

            console.log('Submit button clicked - checking form data:');
            console.log('uploaded_images:', document.getElementById('uploaded_images').value);
            console.log('uploaded_files:', document.getElementById('uploaded_files').value);

            // ตรวจสอบข้อมูลที่จะส่งไปยัง server
            const formData = new FormData(document.getElementById('item-form'));
            for (const pair of formData.entries()) {
                console.log(`Form data - ${pair[0]}: ${pair[1]}`);
            }

            // ตรวจสอบว่า uploaded_images และ uploaded_files มีค่าหรือไม่
            const uploadedImagesValue = document.getElementById('uploaded_images').value;
            const uploadedFilesValue = document.getElementById('uploaded_files').value;

            // ตรวจสอบและแก้ไขค่า uploaded_images
            if (!uploadedImagesValue || uploadedImagesValue === '[]' || uploadedImagesValue === 'null') {
                console.warn('Empty uploaded_images value, setting to empty array');
                document.getElementById('uploaded_images').value = JSON.stringify([]);
            } else {
                // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                try {
                    const parsedImages = JSON.parse(uploadedImagesValue);
                    if (!Array.isArray(parsedImages)) {
                        console.warn('uploaded_images is not an array, fixing');
                        document.getElementById('uploaded_images').value = JSON.stringify(Array.isArray(uploadedImages) ? uploadedImages : []);
                    }
                } catch (e) {
                    console.error('Error parsing uploaded_images JSON:', e);
                    document.getElementById('uploaded_images').value = JSON.stringify(Array.isArray(uploadedImages) ? uploadedImages : []);
                }
            }

            // ตรวจสอบและแก้ไขค่า uploaded_files
            if (!uploadedFilesValue || uploadedFilesValue === '[]' || uploadedFilesValue === 'null') {
                console.warn('Empty uploaded_files value, setting to empty array');
                document.getElementById('uploaded_files').value = JSON.stringify([]);
            } else {
                // ตรวจสอบว่าเป็น JSON ที่ถูกต้องหรือไม่
                try {
                    const parsedFiles = JSON.parse(uploadedFilesValue);
                    if (!Array.isArray(parsedFiles)) {
                        console.warn('uploaded_files is not an array, fixing');
                        document.getElementById('uploaded_files').value = JSON.stringify(Array.isArray(uploadedFiles) ? uploadedFiles : []);
                    }
                } catch (e) {
                    console.error('Error parsing uploaded_files JSON:', e);
                    document.getElementById('uploaded_files').value = JSON.stringify(Array.isArray(uploadedFiles) ? uploadedFiles : []);
                }
            }

            // ตรวจสอบอีกครั้งว่าค่าที่จะส่งไปเป็น JSON ที่ถูกต้อง
            console.log('Final uploaded_images value:', document.getElementById('uploaded_images').value);
            console.log('Final uploaded_files value:', document.getElementById('uploaded_files').value);

            // ตรวจสอบว่ามีข้อมูลรูปภาพและไฟล์หรือไม่
            const hasImages = uploadedImages && uploadedImages.length > 0;
            const hasFiles = uploadedFiles && uploadedFiles.length > 0;

            console.log('Has images:', hasImages, 'Has files:', hasFiles);

            // ถ้าไม่มีข้อมูลรูปภาพและไฟล์ แสดงข้อความเตือน
            if (!hasImages && !hasFiles) {
                console.warn('No images or files found in the form submission');
            }

            // Submit the form
            document.getElementById('item-form').submit();
        });

        // Handle file type selection
        document.querySelectorAll('.file-type-btn').forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.file-type-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to clicked button
                this.classList.add('active');

                // Get selected file type
                currentFileType = this.getAttribute('data-type');

                // Update file type support text
                document.getElementById('file-type-support-text').textContent =
                    currentFileType === 'image' ? 'รองรับไฟล์ JPG, PNG, GIF (สูงสุด 5MB ต่อไฟล์)' :
                    currentFileType === 'document' ? 'รองรับไฟล์ PDF (สูงสุด 50MB ต่อไฟล์)' :
                    currentFileType === 'audio' ? 'รองรับไฟล์ MP3, WAV, OGG (สูงสุด 20MB ต่อไฟล์)' :
                    'รองรับไฟล์ MP4, WEBM, MOV, AVI (สูงสุด 50MB ต่อไฟล์)';

                // Update Dropzone options if dropzone is initialized
                if (dropzone) {
                    // Get file type configuration from global fileTypes
                    const typeConfig = window.fileTypes[currentFileType];

                    // Remove all files first
                    dropzone.removeAllFiles(true);

                    // Update Dropzone options
                    dropzone.options.maxFiles = typeConfig.maxFiles;
                    dropzone.options.maxFilesize = typeConfig.maxFilesize;
                    dropzone.options.acceptedFiles = typeConfig.acceptedFiles;
                    dropzone.options.dictDefaultMessage = typeConfig.dictDefaultMessage;
                    dropzone.options.headers = {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'X-File-Type': currentFileType
                    };
                }
            });
        });
    });
</script>
@endsection
