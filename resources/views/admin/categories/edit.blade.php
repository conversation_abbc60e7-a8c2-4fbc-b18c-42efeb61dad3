@extends('layouts.admin')

@section('title', 'แก้ไขหมวดหมู่ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="card shadow-sm mb-4">
    <div class="card-header bg-white py-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="card-title mb-0">แก้ไขหมวดหมู่: {{ $category->name }}</h5>
                <p class="card-subtitle text-muted">แก้ไขข้อมูลหมวดหมู่</p>
            </div>
            <div>
                <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการหมวดหมู่
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <form action="{{ route('admin.categories.update', $category->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <div class="row g-3">
                <!-- ชื่อหมวดหมู่ -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="name" value="{{ old('name', $category->name) }}" required class="form-control">
                        @error('name')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- รหัสหมวดหมู่ -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="code" class="form-label">รหัสหมวดหมู่</label>
                        <input type="text" name="code" id="code" value="{{ old('code', $category->code) }}" class="form-control">
                        @error('code')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- หมวดหมู่หลัก -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="parent_id" class="form-label">หมวดหมู่หลัก</label>
                        <select name="parent_id" id="parent_id" class="form-select">
                            <option value="">-- ไม่มีหมวดหมู่หลัก --</option>
                            @foreach($parentCategories as $parentCategory)
                                <option value="{{ $parentCategory->id }}" {{ old('parent_id', $category->parent_id) == $parentCategory->id ? 'selected' : '' }}>
                                    {{ $parentCategory->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('parent_id')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- รูปภาพ -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        <div class="mb-2">
                            @php
                                // ตรวจสอบว่ามีรูปภาพจริงหรือไม่
                                $hasImage = false;
                                if ($category->image_path) {
                                    // ตรวจสอบว่าเป็นรูปภาพเริ่มต้นหรือไม่
                                    $imageUrl = get_image_url($category->image_path);
                                    $isDefaultImage = strpos($imageUrl, 'defaults/') !== false;

                                    // ตรวจสอบว่าไฟล์มีอยู่จริงหรือไม่
                                    $normalizedPath = str_replace('/storage/', '', $category->image_path);
                                    $fileExists = \Illuminate\Support\Facades\Storage::disk('public')->exists($normalizedPath);

                                    $hasImage = !$isDefaultImage && $fileExists;
                                }
                            @endphp

                            @if($hasImage)
                                <div class="d-flex align-items-center">
                                    <img src="{{ get_image_url($category->image_path) }}" alt="{{ $category->name }}" class="img-thumbnail" style="height: 80px; width: auto;">
                                    <button type="button" class="btn btn-sm btn-danger ms-2 delete-image-btn" data-category-id="{{ $category->id }}">
                                        <i class="fas fa-trash-alt"></i> ลบรูปภาพ
                                    </button>
                                </div>
                                <div class="mt-1"><small class="text-muted">รูปภาพปัจจุบัน</small></div>
                            @else
                                <div class="alert alert-info">ไม่มีรูปภาพ</div>
                            @endif
                        </div>
                        <input type="file" name="image" id="image" accept="image/*" class="form-control">
                        <small class="text-muted">อัพโหลดรูปภาพใหม่เพื่อเปลี่ยน (ขนาดไม่เกิน 2MB)</small>
                        @error('image')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- คำอธิบาย -->
                <div class="col-12">
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย</label>
                        <textarea name="description" id="description" rows="4" class="form-control">{{ old('description', $category->description) }}</textarea>
                        @error('description')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="text-end mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // ปุ่มลบรูปภาพ
        const deleteImageButtons = document.querySelectorAll('.delete-image-btn');

        deleteImageButtons.forEach(button => {
            button.addEventListener('click', function() {
                const categoryId = this.getAttribute('data-category-id');

                Swal.fire({
                    title: 'ยืนยันการลบรูปภาพ',
                    text: 'คุณต้องการลบรูปภาพนี้ใช่หรือไม่?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'ใช่, ลบรูปภาพ',
                    cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // สร้าง form สำหรับส่งคำขอ DELETE
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = `/admin/categories/${categoryId}/delete-image`;
                        form.style.display = 'none';

                        // เพิ่ม CSRF token
                        const csrfToken = document.createElement('input');
                        csrfToken.type = 'hidden';
                        csrfToken.name = '_token';
                        csrfToken.value = '{{ csrf_token() }}';
                        form.appendChild(csrfToken);

                        // เพิ่ม method spoofing สำหรับ DELETE
                        const methodField = document.createElement('input');
                        methodField.type = 'hidden';
                        methodField.name = '_method';
                        methodField.value = 'DELETE';
                        form.appendChild(methodField);

                        // เพิ่ม form ไปยัง document และส่ง
                        document.body.appendChild(form);

                        // ส่ง form แบบ AJAX
                        const formData = new FormData(form);
                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                Swal.fire(
                                    'ลบรูปภาพแล้ว!',
                                    data.message,
                                    'success'
                                ).then(() => {
                                    // รีโหลดหน้าเพื่อแสดงการเปลี่ยนแปลง
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire(
                                    'เกิดข้อผิดพลาด!',
                                    data.message,
                                    'error'
                                );
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire(
                                'เกิดข้อผิดพลาด!',
                                'ไม่สามารถลบรูปภาพได้ กรุณาลองใหม่อีกครั้ง',
                                'error'
                            );
                        });
                    }
                });
            });
        });
    });
</script>
@endsection
