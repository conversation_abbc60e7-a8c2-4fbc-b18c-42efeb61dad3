@extends('layouts.admin')

@section('title', 'เพิ่มหมวดหมู่ใหม่ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="card shadow-sm mb-4">
    <div class="card-header bg-white py-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="card-title mb-0">เพิ่มหมวดหมู่ใหม่</h5>
                <p class="card-subtitle text-muted">กรอกข้อมูลเพื่อสร้างหมวดหมู่ใหม่</p>
            </div>
            <div>
                <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการหมวดหมู่
                </a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <form action="{{ route('admin.categories.store') }}" method="POST" enctype="multipart/form-data">
            @csrf

            <div class="row g-3">
                <!-- ชื่อหมวดหมู่ -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required class="form-control">
                        @error('name')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- รหัสหมวดหมู่ -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="code" class="form-label">รหัสหมวดหมู่</label>
                        <input type="text" name="code" id="code" value="{{ old('code') }}" class="form-control">
                        @error('code')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- หมวดหมู่หลัก -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="parent_id" class="form-label">หมวดหมู่หลัก</label>
                        <select name="parent_id" id="parent_id" class="form-select">
                            <option value="">-- ไม่มีหมวดหมู่หลัก --</option>
                            @foreach($parentCategories as $parentCategory)
                                <option value="{{ $parentCategory->id }}" {{ old('parent_id') == $parentCategory->id ? 'selected' : '' }}>
                                    {{ $parentCategory->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('parent_id')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- รูปภาพ -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        <input type="file" name="image" id="image" accept="image/*" class="form-control">
                        <small class="text-muted">รูปภาพควรมีขนาดไม่เกิน 2MB</small>
                        @error('image')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- คำอธิบาย -->
                <div class="col-12">
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย</label>
                        <textarea name="description" id="description" rows="4" class="form-control">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="text-danger mt-1">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="text-end mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>บันทึกหมวดหมู่
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
