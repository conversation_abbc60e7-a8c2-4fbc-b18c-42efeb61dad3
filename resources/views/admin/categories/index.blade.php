@extends('layouts.admin')

@section('title', 'จัดการหมวดหมู่ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="fs-4 fw-bold mb-2">จัดการหมวดหมู่</h3>
                <span class="text-secondary">รายการหมวดหมู่ทั้งหมดในระบบ</span>
            </div>
            <div>
                <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่ใหม่
                </a>
            </div>
        </div>

        <!-- Categories Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ชื่อหมวดหมู่
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            รหัส
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จำนวนรายการ
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            วันที่สร้าง
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จัดการ
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($categories as $category)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0" style="width: 40px; height: 40px;">
                                    <img class="rounded img-fluid" style="width: 40px; height: 40px; object-fit: cover;" src="{{ get_image_url($category->image_path, 'defaults/category-default.svg') }}" alt="{{ $category->name }}">
                                </div>
                                <div class="ms-3">
                                    <div class="fw-medium">
                                        {{ $category->name }}
                                    </div>
                                    @if($category->parent)
                                    <div class="small text-secondary">
                                        หมวดหมู่หลัก: {{ $category->parent->name }}
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="text-secondary">
                            {{ $category->code ?: '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ number_format($category->items_count) }}
                        </td>
                        <td class="text-secondary">
                            {{ $category->created_at ? $category->created_at->format('d/m/Y') : '-' }}
                        </td>
                        <td>
                            <div class="d-flex gap-3">
                                <a href="{{ route('admin.categories.edit', $category->id) }}" class="text-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.categories.destroy', $category->id) }}" method="POST" class="d-inline" onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบหมวดหมู่นี้?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center text-secondary py-3">
                            ไม่พบหมวดหมู่
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4">
            {{ $categories->links() }}
        </div>
    </div>
</div>
@endsection
