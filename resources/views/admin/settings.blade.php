@extends('layouts.admin')

@section('title', 'ตั้งค่าระบบ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="card shadow-sm mb-4">
    <div class="card-header bg-white py-3">
        <h5 class="card-title mb-0">ตั้งค่าระบบ</h5>
        <p class="card-subtitle text-muted">จัดการการตั้งค่าทั่วไปของระบบ</p>
    </div>

    <div class="card-body">
        <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data">
            @csrf

            <div class="mb-4">
                <h5 class="border-bottom pb-2 mb-3">ตั้งค่าทั่วไป</h5>

                <div class="row g-3">
                    <!-- ชื่อเว็บไซต์ -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="site_title" class="form-label">ชื่อเว็บไซต์ <span class="text-danger">*</span></label>
                            <input type="text" name="site_title" id="site_title" value="{{ old('site_title', $settings['site_title']) }}" required class="form-control">
                            @error('site_title')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- อีเมลติดต่อ -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_email" class="form-label">อีเมลติดต่อ</label>
                            <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email', $settings['contact_email']) }}" class="form-control">
                            @error('contact_email')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- เบอร์โทรติดต่อ -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_phone" class="form-label">เบอร์โทรติดต่อ</label>
                            <input type="text" name="contact_phone" id="contact_phone" value="{{ old('contact_phone', \App\Models\Setting::get('contact_phone', '')) }}" class="form-control">
                            @error('contact_phone')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- ที่อยู่ติดต่อ -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_address" class="form-label">ที่อยู่ติดต่อ</label>
                            <input type="text" name="contact_address" id="contact_address" value="{{ old('contact_address', \App\Models\Setting::get('contact_address', '')) }}" class="form-control">
                            @error('contact_address')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- คำอธิบายเว็บไซต์ -->
                    <div class="col-12">
                        <div class="mb-3">
                            <label for="site_description" class="form-label">คำอธิบายเว็บไซต์</label>
                            <textarea name="site_description" id="site_description" rows="3" class="form-control">{{ old('site_description', $settings['site_description']) }}</textarea>
                            @error('site_description')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>



                    <!-- ข้อความส่วนท้าย -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="footer_text" class="form-label">ข้อความส่วนท้าย</label>
                            <input type="text" name="footer_text" id="footer_text" value="{{ old('footer_text', $settings['footer_text']) }}" class="form-control">
                            @error('footer_text')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- ข้อความลิขสิทธิ์ -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="copyright_text" class="form-label">ข้อความลิขสิทธิ์</label>
                            <input type="text" name="copyright_text" id="copyright_text" value="{{ old('copyright_text', \App\Models\Setting::get('copyright_text', 'สงวนลิขสิทธิ์')) }}" class="form-control">
                            @error('copyright_text')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h5 class="border-bottom pb-2 mb-3">การตั้งค่า SEO</h5>

                <div class="row g-3">
                    <!-- คำสำคัญหน้าแรก -->
                    <div class="col-12">
                        <div class="mb-3">
                            <label for="site_keywords" class="form-label">คำสำคัญหน้าแรก (Keywords)</label>
                            <textarea name="site_keywords" id="site_keywords" rows="2" class="form-control" placeholder="คำสำคัญ1, คำสำคัญ2, คำสำคัญ3">{{ old('site_keywords', \App\Models\Setting::get('site_keywords', '')) }}</textarea>
                            <div class="form-text">คำสำคัญสำหรับหน้าแรก แยกด้วยเครื่องหมายจุลภาค (,)</div>
                            @error('site_keywords')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Meta Author -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="site_author" class="form-label">ผู้เขียน (Author)</label>
                            <input type="text" name="site_author" id="site_author" value="{{ old('site_author', \App\Models\Setting::get('site_author', '')) }}" class="form-control" placeholder="ชื่อองค์กรหรือผู้เขียน">
                            @error('site_author')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Twitter Username -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="twitter_username" class="form-label">Twitter Username</label>
                            <div class="input-group">
                                <span class="input-group-text">@</span>
                                <input type="text" name="twitter_username" id="twitter_username" value="{{ old('twitter_username', \App\Models\Setting::get('twitter_username', '')) }}" class="form-control" placeholder="username">
                            </div>
                            <div class="form-text">สำหรับ Twitter Cards (ไม่ต้องใส่ @)</div>
                            @error('twitter_username')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Google Analytics ID -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                            <input type="text" name="google_analytics_id" id="google_analytics_id" value="{{ old('google_analytics_id', \App\Models\Setting::get('google_analytics_id', '')) }}" class="form-control" placeholder="G-XXXXXXXXXX หรือ UA-XXXXXXXXX-X">
                            <div class="form-text">รหัส Google Analytics สำหรับติดตามสถิติเว็บไซต์</div>
                            @error('google_analytics_id')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Google Tag Manager ID -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="google_tag_manager_id" class="form-label">Google Tag Manager ID</label>
                            <input type="text" name="google_tag_manager_id" id="google_tag_manager_id" value="{{ old('google_tag_manager_id', \App\Models\Setting::get('google_tag_manager_id', '')) }}" class="form-control" placeholder="GTM-XXXXXXX">
                            <div class="form-text">รหัส Google Tag Manager (ถ้ามี)</div>
                            @error('google_tag_manager_id')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Facebook App ID -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="facebook_app_id" class="form-label">Facebook App ID</label>
                            <input type="text" name="facebook_app_id" id="facebook_app_id" value="{{ old('facebook_app_id', \App\Models\Setting::get('facebook_app_id', '')) }}" class="form-control" placeholder="123456789012345">
                            <div class="form-text">รหัส Facebook App สำหรับ Open Graph (ถ้ามี)</div>
                            @error('facebook_app_id')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Google Site Verification -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="google_site_verification" class="form-label">Google Site Verification</label>
                            <input type="text" name="google_site_verification" id="google_site_verification" value="{{ old('google_site_verification', \App\Models\Setting::get('google_site_verification', '')) }}" class="form-control" placeholder="abcdefghijklmnopqrstuvwxyz123456">
                            <div class="form-text">รหัสยืนยันเว็บไซต์กับ Google Search Console</div>
                            @error('google_site_verification')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h5 class="border-bottom pb-2 mb-3">รูปภาพ</h5>

                <div class="row g-3">
                    <!-- โลโก้เว็บไซต์ -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="site_logo" class="form-label">โลโก้เว็บไซต์</label>
                            <input type="file" name="site_logo" id="site_logo" class="form-control" accept="image/*">
                            @error('site_logo')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                            @if($settings['site_logo'])
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $settings['site_logo']) }}" alt="Site Logo" class="img-thumbnail" style="max-height: 60px;">
                                </div>
                            @endif
                            <small class="text-muted">แนะนำขนาด 200x50 พิกเซล</small>
                        </div>
                    </div>

                    <!-- รูปภาพหน้าแรก -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="hero_image" class="form-label">รูปภาพหน้าแรก</label>
                            <input type="file" name="hero_image" id="hero_image" class="form-control" accept="image/*">
                            @error('hero_image')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                            @if($settings['hero_image'])
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $settings['hero_image']) }}" alt="Hero Image" class="img-thumbnail" style="max-height: 120px;">
                                </div>
                            @endif
                            <small class="text-muted">แนะนำขนาด 1920x600 พิกเซล</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h5 class="border-bottom pb-2 mb-3">โซเชียลมีเดีย</h5>

                <div class="row g-3">
                    <!-- Facebook URL -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="facebook_url" class="form-label">Facebook URL</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fab fa-facebook-f"></i></span>
                                <input type="url" name="facebook_url" id="facebook_url" value="{{ old('facebook_url', $settings['facebook_url']) }}" class="form-control">
                            </div>
                            @error('facebook_url')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Twitter URL -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="twitter_url" class="form-label">Twitter URL</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                <input type="url" name="twitter_url" id="twitter_url" value="{{ old('twitter_url', $settings['twitter_url']) }}" class="form-control">
                            </div>
                            @error('twitter_url')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Instagram URL -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="instagram_url" class="form-label">Instagram URL</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                <input type="url" name="instagram_url" id="instagram_url" value="{{ old('instagram_url', $settings['instagram_url']) }}" class="form-control">
                            </div>
                            @error('instagram_url')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- YouTube URL -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="youtube_url" class="form-label">YouTube URL</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fab fa-youtube"></i></span>
                                <input type="url" name="youtube_url" id="youtube_url" value="{{ old('youtube_url', $settings['youtube_url']) }}" class="form-control">
                            </div>
                            @error('youtube_url')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <div class="card border-warning">
                    <div class="card-header bg-warning bg-opacity-10">
                        <h5 class="mb-0">โหมดบำรุงรักษา</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>เมื่อเปิดใช้งานโหมดบำรุงรักษา ผู้ใช้ทั่วไปจะไม่สามารถเข้าถึงเว็บไซต์ได้ ยกเว้นหน้าแอดมินและหน้าล็อกอิน
                        </div>

                        <div class="row g-3">
                            <!-- โหมดบำรุงรักษา -->
                            <div class="col-12">
                                <div class="mb-3 form-check form-switch">
                                    <input type="checkbox" name="maintenance_mode" id="maintenance_mode" class="form-check-input" {{ $settings['maintenance_mode'] === 'true' ? 'checked' : '' }}>
                                    <label for="maintenance_mode" class="form-check-label">เปิดใช้งานโหมดบำรุงรักษา</label>
                                </div>
                            </div>

                            <!-- ข้อความบำรุงรักษา -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="maintenance_message" class="form-label">ข้อความบำรุงรักษา</label>
                                    <textarea name="maintenance_message" id="maintenance_message" rows="3" class="form-control">{{ old('maintenance_message', $settings['maintenance_message']) }}</textarea>
                                    <div class="form-text">ข้อความที่จะแสดงให้ผู้ใช้เห็นเมื่อเว็บไซต์อยู่ในโหมดบำรุงรักษา</div>
                                    @error('maintenance_message')
                                        <div class="text-danger mt-1">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>บันทึกการตั้งค่า
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
