@extends('layouts.admin')

@section('title', 'จัดการวัสดุ - ระบบจัดการคลังเอกสารโบราณดิจิทัล')

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="fs-4 fw-bold mb-2">จัดการวัสดุ</h3>
                <span class="text-secondary">รายการวัสดุทั้งหมดในระบบ</span>
            </div>
            <div>
                <a href="{{ route('admin.materials.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มวัสดุใหม่
                </a>
            </div>
        </div>

        <!-- Materials Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ชื่อวัสดุ
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            คำอธิบาย
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จำนวนเอกสาร
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            วันที่สร้าง
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จัดการ
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($materials as $material)
                    <tr>
                        <td class="fw-medium">
                            {{ $material->name }}
                        </td>
                        <td class="text-secondary">
                            {{ Str::limit($material->description, 100) ?: '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ number_format($material->items_count) }}
                        </td>
                        <td class="text-secondary">
                            {{ $material->created_at ? $material->created_at->format('d/m/Y') : '-' }}
                        </td>
                        <td>
                            <div class="d-flex gap-3">
                                <a href="{{ route('admin.materials.edit', $material) }}" class="text-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.materials.destroy', $material) }}" method="POST" class="d-inline" onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบวัสดุนี้?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center text-secondary py-3">
                            ไม่พบวัสดุ
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4">
            {{ $materials->links() }}
        </div>
    </div>
</div>
@endsection
