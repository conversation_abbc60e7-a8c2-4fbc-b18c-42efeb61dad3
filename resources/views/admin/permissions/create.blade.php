@extends('layouts.admin')

@section('title', 'เพิ่มสิทธิ์ใหม่ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="container-fluid px-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-4">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-2">เพิ่มสิทธิ์ใหม่</h5>
                    <p class="card-subtitle text-muted mb-0">กรอกข้อมูลเพื่อเพิ่มสิทธิ์ใหม่</p>
                </div>
                <div>
                    <a href="{{ route('admin.permissions') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                </div>
            </div>
        </div>

        <div class="card-body">
            <form action="{{ route('admin.permissions.store') }}" method="POST">
                @csrf
                <div class="row g-3">
                    <!-- ชื่อสิทธิ์ -->
                    <div class="col-12">
                        <div class="mb-3">
                            <label for="name" class="form-label">ชื่อสิทธิ์ <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" value="{{ old('name') }}">
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text text-muted">ตัวอย่าง: view items, create users, edit settings</div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึก
                    </button>
                    <a href="{{ route('admin.permissions') }}" class="btn btn-secondary ms-2">ยกเลิก</a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
