@extends('layouts.admin')

@section('title', 'จัดการสิทธิ์การใช้งาน - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="container-fluid px-4">
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-4">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-3 mb-md-0">
                    <h5 class="card-title mb-2">จัดการสิทธิ์การใช้งาน</h5>
                    <p class="card-subtitle text-muted mb-0">กำหนดสิทธิ์การใช้งานในระบบ</p>
                </div>
                <div>
                    <a href="{{ route('admin.permissions.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i>เพิ่มสิทธิ์ใหม่
                    </a>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Search -->
            <div class="mb-4">
                <form action="{{ route('admin.permissions') }}" method="GET">
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" name="search" value="{{ request('search') }}" class="form-control py-2" placeholder="ค้นหาสิทธิ์...">
                        <button type="submit" class="btn btn-primary">ค้นหา</button>
                    </div>
                </form>
            </div>

            <!-- Permissions Table -->
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                ชื่อสิทธิ์
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                บทบาทที่ใช้
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                จัดการ
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($permissions as $permission)
                        <tr>
                            <td class="py-3">
                                <div class="fw-medium">
                                    {{ $permission->name }}
                                </div>
                            </td>
                            <td class="py-3">
                                <div class="d-flex flex-wrap gap-1">
                                    @foreach($permission->roles->take(3) as $role)
                                        <span class="badge bg-info rounded-pill px-2 py-1">
                                            {{ $role->name }}
                                        </span>
                                    @endforeach
                                    @if($permission->roles->count() > 3)
                                        <span class="badge bg-secondary rounded-pill px-2 py-1">
                                            +{{ $permission->roles->count() - 3 }} อื่นๆ
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td class="py-3">
                                <div class="d-flex gap-3">
                                    <a href="{{ route('admin.permissions.edit', $permission) }}" class="text-primary border-0 bg-transparent p-0" title="แก้ไข">
                                        <i class="fas fa-edit fa-lg"></i>
                                    </a>
                                    <form action="{{ route('admin.permissions.destroy', $permission) }}" method="POST" class="d-inline" onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบสิทธิ์นี้? การกระทำนี้ไม่สามารถย้อนกลับได้')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                            <i class="fas fa-trash fa-lg"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="3" class="text-center text-secondary py-4">
                                <i class="fas fa-key fa-2x mb-3 text-muted"></i>
                                <p>ไม่พบสิทธิ์การใช้งาน</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
                {{ $permissions->links() }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    @if(session('success'))
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            title: 'สำเร็จ!',
            text: "{{ session('success') }}",
            icon: 'success',
            confirmButtonText: 'ตกลง'
        });
    });
    @endif

    @if(session('error'))
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            title: 'เกิดข้อผิดพลาด!',
            text: "{{ session('error') }}",
            icon: 'error',
            confirmButtonText: 'ตกลง'
        });
    });
    @endif
</script>
@endsection
