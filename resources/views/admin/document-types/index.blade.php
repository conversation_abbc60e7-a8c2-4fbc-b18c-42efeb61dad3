@extends('layouts.admin')

@section('title', 'จัดการประเภทเอกสาร - ระบบจัดการคลังเอกสารโบราณดิจิทัล')

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="fs-4 fw-bold mb-2">จัดการประเภทเอกสาร</h3>
                <span class="text-secondary">รายการประเภทเอกสารทั้งหมดในระบบ</span>
            </div>
            <div>
                <a href="#" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มประเภทเอกสารใหม่
                </a>
            </div>
        </div>

        <!-- Document Types Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ชื่อประเภทเอกสาร
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ไอคอน
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จำนวนเอกสาร
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            วันที่สร้าง
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จัดการ
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($documentTypes as $documentType)
                    <tr>
                        <td class="fw-medium">
                            {{ $documentType->name }}
                        </td>
                        <td class="text-secondary">
                            @if($documentType->icon)
                                <i class="fas fa-{{ $documentType->icon }} fs-5"></i>
                            @else
                                <i class="fas fa-file-alt fs-5"></i>
                            @endif
                        </td>
                        <td class="text-secondary">
                            {{ number_format($documentType->documents_count) }}
                        </td>
                        <td class="text-secondary">
                            {{ $documentType->created_at ? $documentType->created_at->format('d/m/Y') : '-' }}
                        </td>
                        <td>
                            <div class="d-flex gap-3">
                                <a href="#" class="text-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="#" method="POST" class="d-inline" onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบประเภทเอกสารนี้?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-center text-secondary py-3">
                            ไม่พบประเภทเอกสาร
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4">
            {{ $documentTypes->links() }}
        </div>
    </div>
</div>
@endsection
