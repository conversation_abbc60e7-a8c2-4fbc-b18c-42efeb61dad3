@extends('layouts.admin')

@section('title', 'แดชบอร์ด - ระบบจัดการคลังเอกสารโบราณดิจิทัล')

@section('styles')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endsection

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h3 class="fs-4 fw-bold mb-2">แดชบอร์ด</h3>
                <span class="text-secondary">ภาพรวมของระบบคลังเอกสารโบราณดิจิทัล</span>
            </div>
            <div>
                <a href="{{ route('admin.items.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มรายการใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-12 col-sm-6 col-lg-3">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fs-3 fw-bold">{{ number_format($stats['total_documents']) }}</span>
                        <h3 class="text-secondary fs-6 fw-normal mt-1">เอกสารทั้งหมด</h3>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-file-alt fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-lg-3">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fs-3 fw-bold">{{ number_format($stats['total_categories']) }}</span>
                        <h3 class="text-secondary fs-6 fw-normal mt-1">หมวดหมู่</h3>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-folder fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-lg-3">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fs-3 fw-bold">{{ number_format($stats['total_languages']) }}</span>
                        <h3 class="text-secondary fs-6 fw-normal mt-1">ภาษา</h3>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-language fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-lg-3">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fs-3 fw-bold">{{ number_format($stats['total_views']) }}</span>
                        <h3 class="text-secondary fs-6 fw-normal mt-1">จำนวนการเข้าชม</h3>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-eye fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->

<div class="row g-4 mb-4">
    <!-- Documents by Category Chart -->
    <div class="col-12 col-lg-6">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="fs-5 fw-bold">เอกสารตามหมวดหมู่</span>
                    </div>
                    <div>
                        <a href="{{ route('admin.categories.index') }}" class="text-primary text-decoration-none">
                            ดูทั้งหมด
                        </a>
                    </div>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Documents by Type Chart -->
    <div class="col-12 col-lg-6">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="fs-5 fw-bold">เอกสารตามประเภท</span>
                    </div>
                    <div>
                        <a href="{{ route('admin.item-types.index') }}" class="text-primary text-decoration-none">
                            ดูทั้งหมด
                        </a>
                    </div>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="typeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <!-- Documents by Year Chart -->
    <div class="col-12 col-lg-6">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="fs-5 fw-bold">เอกสารตามปี</span>
                    </div>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="yearChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Documents by Language Chart -->
    <div class="col-12 col-lg-6">
        <div class="card shadow h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <span class="fs-5 fw-bold">เอกสารตามภาษา</span>
                    </div>
                    <div>
                        <a href="{{ route('admin.languages') }}" class="text-primary text-decoration-none">
                            ดูทั้งหมด
                        </a>
                    </div>
                </div>
                <div class="chart-container" style="height: 300px;">
                    <canvas id="languageChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Documents -->
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <span class="fs-5 fw-bold">เอกสารล่าสุด</span>
            </div>
            <div>
                <a href="{{ route('admin.items') }}" class="text-primary text-decoration-none">
                    ดูทั้งหมด
                </a>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ชื่อเอกสาร
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            หมวดหมู่
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ประเภท
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            วันที่สร้าง
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            การเข้าชม
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จัดการ
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($recentDocuments as $document)
                    <tr>
                        <td class="fw-medium">
                            {{ Str::limit($document->title, 50) }}
                        </td>
                        <td class="text-secondary">
                            {{ $document->category ? $document->category->name : '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ $document->itemType ? $document->itemType->name : '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ $document->created_at ? $document->created_at->format('d/m/Y') : '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ number_format($document->views) }}
                        </td>
                        <td>
                            <div class="d-flex gap-2">
                                <a href="{{ route('admin.items.edit', $document->id) }}" class="text-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ route('items.show', $document->id) }}" target="_blank" class="text-success" title="ดู">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Chart colors
        const colors = [
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 99, 132, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 205, 86, 0.8)',
            'rgba(201, 203, 207, 0.8)',
            'rgba(255, 99, 132, 0.8)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 206, 86, 0.8)',
        ];

        // Category Chart
        const categoryData = @json($documentsByCategory);
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: categoryData.map(item => item.name),
                datasets: [{
                    label: 'จำนวนเอกสาร',
                    data: categoryData.map(item => item.items_count),
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace('0.8', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // Type Chart
        const typeData = @json($documentsByType);
        const typeCtx = document.getElementById('typeChart').getContext('2d');
        new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: typeData.map(item => item.name),
                datasets: [{
                    data: typeData.map(item => item.items_count),
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace('0.8', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // Year Chart
        const yearData = @json($documentsByYear);
        const yearCtx = document.getElementById('yearChart').getContext('2d');
        new Chart(yearCtx, {
            type: 'line',
            data: {
                labels: yearData.map(item => item.year),
                datasets: [{
                    label: 'จำนวนเอกสาร',
                    data: yearData.map(item => item.count),
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // Language Chart
        const languageData = @json($documentsByLanguage);
        const languageCtx = document.getElementById('languageChart').getContext('2d');
        new Chart(languageCtx, {
            type: 'pie',
            data: {
                labels: languageData.map(item => item.name),
                datasets: [{
                    data: languageData.map(item => item.items_count),
                    backgroundColor: colors,
                    borderColor: colors.map(color => color.replace('0.8', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });


</script>
@endsection
