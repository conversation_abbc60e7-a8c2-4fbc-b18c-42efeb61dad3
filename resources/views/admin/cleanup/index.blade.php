@extends('layouts.admin')

@section('title', 'เครื่องมือลบไฟล์ขยะ')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/cleanup-tool.css') }}">
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm cleanup-card">
                <div class="card-header bg-white">
                    <h3 class="card-title">
                        <i class="fas fa-trash-alt text-danger me-2"></i>
                        เครื่องมือลบไฟล์ขยะ
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info shadow-sm">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">เกี่ยวกับเครื่องมือนี้</h5>
                                <p class="mb-0">เครื่องมือนี้ใช้สำหรับค้นหาและลบไฟล์ที่ไม่มีการอ้างอิงในฐานข้อมูล เช่น รูปภาพหรือไฟล์ที่อัพโหลดแล้วไม่ได้ใช้งาน เพื่อช่วยประหยัดพื้นที่เก็บข้อมูล</p>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4 cleanup-card">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-filter me-2"></i>
                                ตัวเลือกการค้นหา
                            </h5>
                            <div class="form-group mb-3">
                                <label for="file-type" class="form-label">ประเภทไฟล์ที่ต้องการตรวจสอบ</label>
                                <div class="input-group">
                                    <select id="file-type" class="form-select">
                                        <option value="all">ทั้งหมด</option>
                                        <option value="images">รูปภาพเท่านั้น</option>
                                        <option value="files">ไฟล์อื่นๆ เท่านั้น</option>
                                        <option value="temp">ไฟล์ชั่วคราว</option>
                                    </select>
                                    <button id="scan-btn" class="btn btn-primary px-4">
                                        <i class="fas fa-search me-2"></i> ค้นหาไฟล์ขยะ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="loading" class="text-center my-5 py-5 cleanup-loading" style="display: none;">
                        <div class="spinner"></div>
                        <p class="mt-3 fs-5">กำลังค้นหาไฟล์ขยะ กรุณารอสักครู่...</p>
                    </div>

                    <div id="results-container" style="display: none;">
                        <div class="card shadow-sm mb-4 cleanup-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center flex-wrap">
                                    <h4 id="results-summary" class="mb-0 text-primary">
                                        <i class="fas fa-search me-2"></i>
                                        <span id="results-count-display">0</span> ไฟล์ขยะ
                                    </h4>
                                    <div class="mt-3 mt-md-0">
                                        <button id="select-all-btn" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-check-square me-1"></i> เลือกทั้งหมด
                                        </button>
                                        <button id="deselect-all-btn" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-square me-1"></i> ยกเลิกการเลือก
                                        </button>
                                        <button id="delete-selected-btn" class="btn btn-danger" disabled>
                                            <i class="fas fa-trash-alt me-1"></i> ลบไฟล์ที่เลือก
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="no-results" class="alert alert-success shadow-sm empty-state" style="display: none;">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">ไม่พบไฟล์ขยะ</h5>
                                    <p class="mb-0">ระบบไม่พบไฟล์ที่ไม่ได้ใช้งานในระบบ</p>
                                </div>
                            </div>
                        </div>

                        <div id="files-container" class="card shadow-sm cleanup-card" style="display: none;">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 50px;">
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" id="select-all-files">
                                                    </div>
                                                </th>
                                                <th style="width: 100px;">ตัวอย่าง</th>
                                                <th>ชื่อไฟล์</th>
                                                <th>ประเภท</th>
                                                <th>ขนาด</th>
                                                <th>แก้ไขล่าสุด</th>
                                            </tr>
                                        </thead>
                                        <tbody id="files-table-body"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // ค้นหาไฟล์ขยะ
        $('#scan-btn').on('click', function() {
            const fileType = $('#file-type').val();

            // แสดง loading
            $('#loading').show();
            $('#results-container').hide();

            // ส่งคำขอค้นหาไฟล์ขยะ
            $.ajax({
                url: '{{ route("admin.cleanup.scan") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    type: fileType
                },
                success: function(response) {
                    // ซ่อน loading
                    $('#loading').hide();

                    if (response.success) {
                        // แสดงผลลัพธ์
                        displayResults(response);
                    } else {
                        showErrorAlert('เกิดข้อผิดพลาด', response.message || 'ไม่สามารถค้นหาไฟล์ขยะได้');
                    }
                },
                error: function(xhr) {
                    // ซ่อน loading
                    $('#loading').hide();
                    showErrorAlert('เกิดข้อผิดพลาด', 'ไม่สามารถค้นหาไฟล์ขยะได้');
                }
            });
        });

        // แสดงผลลัพธ์การค้นหา
        function displayResults(response) {
            // รวมไฟล์ทั้งหมดเข้าด้วยกัน
            const allFiles = [];
            const files = response.files || {};

            // รวมไฟล์จากทุกประเภท
            if (files.images && files.images.length > 0) {
                files.images.forEach(file => {
                    file.fileType = 'image';
                    allFiles.push(file);
                });
            }

            if (files.documents && files.documents.length > 0) {
                files.documents.forEach(file => {
                    file.fileType = 'file'; // เปลี่ยนจาก document เป็น file
                    allFiles.push(file);
                });
            }

            if (files.others && files.others.length > 0) {
                files.others.forEach(file => {
                    file.fileType = 'other';
                    allFiles.push(file);
                });
            }

            // เพิ่มการรองรับไฟล์ชั่วคราว
            if (response.type === 'temp' || (files.temp && files.temp.length > 0)) {
                // ถ้ามีการระบุประเภทเป็น temp หรือมีไฟล์ temp ส่งมา
                const tempFiles = files.temp || [];
                tempFiles.forEach(file => {
                    file.fileType = 'temp';
                    allFiles.push(file);
                });
            }

            const totalFiles = response.total || 0;
            const totalSize = response.totalSizeFormatted || '0 B';

            // แสดงจำนวนไฟล์ที่พบ
            $('#results-count').text(totalFiles);
            $('#results-count-display').text(totalFiles);
            // ล้างข้อความเดิมและเพิ่มข้อความใหม่
            $('#results-summary').html(`<i class="fas fa-search me-2"></i><span id="results-count-display">${totalFiles}</span> ไฟล์ขยะ (${totalSize})`);

            // แสดงผลลัพธ์
            $('#results-container').show();

            if (totalFiles === 0) {
                // ไม่พบไฟล์ขยะ
                $('#no-results').show();
                $('#files-container').hide();
                $('#delete-selected-btn').prop('disabled', true);
            } else {
                // พบไฟล์ขยะ
                $('#no-results').hide();
                $('#files-container').show();

                // แสดงรายการไฟล์
                displayFileList(allFiles);

                // ตรวจสอบการเลือกไฟล์
                updateDeleteButtonState();
            }
        }

        // แสดงรายการไฟล์
        function displayFileList(files) {
            const tableBody = $('#files-table-body');
            tableBody.empty();

            if (files.length === 0) {
                const emptyRow = `
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <p>ไม่พบไฟล์ขยะในระบบ</p>
                            </div>
                        </td>
                    </tr>
                `;
                tableBody.append(emptyRow);
                return;
            }

            files.forEach(function(file) {
                const fileSize = formatBytes(file.size);
                const lastModified = new Date(file.last_modified * 1000).toLocaleString('th-TH');
                const fileExtension = getFileExtension(file.name);
                const fileTypeIcon = getFileTypeIcon(fileExtension);

                // ตรวจสอบประเภทไฟล์ (ใช้ค่าที่ส่งมาจาก server หรือกำหนดเอง)
                const fileType = file.fileType || getFileTypeFromExtension(fileExtension);
                const fileTypeLabel = getFileTypeLabel(fileType);

                let row = `
                    <tr>
                        <td>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input file-checkbox" data-path="${file.path}" data-size="${file.size}">
                            </div>
                        </td>
                `;

                // แสดงตัวอย่างตามประเภทไฟล์
                if (fileType === 'image') {
                    row += `
                        <td>
                            <img src="{{ asset('storage') }}/${file.path}" alt="${file.name}" class="img-thumbnail" style="height: 50px; width: auto; max-width: 100px;">
                        </td>
                    `;
                } else {
                    row += `
                        <td>
                            <div class="file-icon">
                                <i class="${fileTypeIcon} fa-2x text-secondary"></i>
                            </div>
                        </td>
                    `;
                }

                row += `
                        <td>
                            <div class="d-flex flex-column">
                                <span class="fw-medium">${file.name}</span>
                                <small class="text-muted">${file.path}</small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">${fileTypeLabel}</span>
                        </td>
                        <td>${fileSize}</td>
                        <td>${lastModified}</td>
                    </tr>
                `;

                tableBody.append(row);
            });
        }

        // เลือกไฟล์ทั้งหมด
        $('#select-all-btn, #select-all-files').on('click', function() {
            $('.file-checkbox').prop('checked', true);
            $('#select-all-files').prop('checked', true);
            updateDeleteButtonState();
        });

        // ยกเลิกการเลือกไฟล์ทั้งหมด
        $('#deselect-all-btn').on('click', function() {
            $('.file-checkbox').prop('checked', false);
            $('#select-all-files').prop('checked', false);
            updateDeleteButtonState();
        });

        // ตรวจสอบการเลือกไฟล์
        $(document).on('change', '.file-checkbox', function() {
            updateDeleteButtonState();
            updateSelectAllCheckbox();
        });

        // อัปเดตสถานะปุ่มลบ
        function updateDeleteButtonState() {
            const selectedCount = $('.file-checkbox:checked').length;
            $('#delete-selected-btn').prop('disabled', selectedCount === 0);
        }

        // อัปเดตสถานะ checkbox เลือกทั้งหมด
        function updateSelectAllCheckbox() {
            const totalFiles = $('.file-checkbox').length;
            const selectedFiles = $('.file-checkbox:checked').length;

            $('#select-all-files').prop('checked', totalFiles > 0 && totalFiles === selectedFiles);
        }

        // ลบไฟล์ที่เลือก
        $('#delete-selected-btn').on('click', function() {
            const selectedFiles = [];

            $('.file-checkbox:checked').each(function() {
                selectedFiles.push({
                    path: $(this).data('path'),
                    size: $(this).data('size')
                });
            });

            if (selectedFiles.length === 0) {
                return;
            }

            // ยืนยันการลบไฟล์
            Swal.fire({
                title: 'ยืนยันการลบไฟล์',
                html: `
                    <div class="text-start">
                        <p>คุณต้องการลบไฟล์ที่เลือกทั้งหมด <strong>${selectedFiles.length}</strong> ไฟล์ใช่หรือไม่?</p>
                        <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>การลบไฟล์ไม่สามารถเรียกคืนได้</p>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-trash-alt me-2"></i>ใช่, ลบไฟล์',
                cancelButtonText: '<i class="fas fa-times me-2"></i>ยกเลิก',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // แสดง loading
                    Swal.fire({
                        title: 'กำลังลบไฟล์',
                        html: '<div class="text-center"><i class="fas fa-spinner fa-spin fa-3x mb-3"></i><p>กรุณารอสักครู่...</p></div>',
                        allowOutsideClick: false,
                        showConfirmButton: false
                    });

                    // ส่งคำขอลบไฟล์
                    $.ajax({
                        url: '{{ route("admin.cleanup.delete") }}',
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            files: selectedFiles
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'ลบไฟล์สำเร็จ',
                                    html: `
                                        <div class="text-start">
                                            <p>ลบไฟล์สำเร็จ <strong>${response.deleted}</strong> ไฟล์ (${response.totalSizeDeletedFormatted})</p>
                                            ${response.failed > 0 ? `<p class="text-warning">ล้มเหลว ${response.failed} ไฟล์</p>` : ''}
                                        </div>
                                    `
                                }).then(() => {
                                    // ค้นหาไฟล์ขยะอีกครั้ง
                                    $('#scan-btn').click();
                                });
                            } else {
                                showErrorAlert('เกิดข้อผิดพลาด', response.message || 'ไม่สามารถลบไฟล์ได้');
                            }
                        },
                        error: function(xhr) {
                            showErrorAlert('เกิดข้อผิดพลาด', 'ไม่สามารถลบไฟล์ได้');
                        }
                    });
                }
            });
        });

        // แสดงข้อความแจ้งเตือนข้อผิดพลาด
        function showErrorAlert(title, message) {
            Swal.fire({
                icon: 'error',
                title: title,
                text: message
            });
        }

        // แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 B';

            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

        // ดึงนามสกุลไฟล์
        function getFileExtension(filename) {
            return filename.split('.').pop().toLowerCase();
        }

        // ดึงประเภทไฟล์จากนามสกุล
        function getFileTypeFromExtension(extension) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
            const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];

            if (imageExtensions.includes(extension)) {
                return 'image';
            } else if (documentExtensions.includes(extension)) {
                return 'document';
            } else {
                return 'other';
            }
        }

        // ดึงไอคอนตามประเภทไฟล์
        function getFileTypeIcon(extension) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
            const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
            const archiveExtensions = ['zip', 'rar', 'tar', 'gz', '7z'];

            if (imageExtensions.includes(extension)) {
                return 'fas fa-file-image';
            } else if (extension === 'pdf') {
                return 'fas fa-file-pdf';
            } else if (['doc', 'docx'].includes(extension)) {
                return 'fas fa-file-word';
            } else if (['xls', 'xlsx'].includes(extension)) {
                return 'fas fa-file-excel';
            } else if (['ppt', 'pptx'].includes(extension)) {
                return 'fas fa-file-powerpoint';
            } else if (documentExtensions.includes(extension)) {
                return 'fas fa-file-alt';
            } else if (archiveExtensions.includes(extension)) {
                return 'fas fa-file-archive';
            } else {
                return 'fas fa-file';
            }
        }

        // ดึงป้ายกำกับประเภทไฟล์
        function getFileTypeLabel(fileType) {
            switch (fileType) {
                case 'image':
                    return 'รูปภาพ';
                case 'file':
                    return 'ไฟล์อื่นๆ'; // เปลี่ยนจาก "เอกสาร" เป็น "ไฟล์อื่นๆ"
                case 'document':
                    return 'ไฟล์อื่นๆ'; // สำหรับความเข้ากันได้กับโค้ดเก่า
                case 'other':
                    return 'ไฟล์อื่นๆ';
                case 'temp':
                    return 'ไฟล์ชั่วคราว';
                default:
                    return 'ไม่ทราบประเภท';
            }
        }
    });
</script>
@endsection
