@extends('layouts.admin')

@section('title', 'เครื่องมือลบไฟล์ขยะ')

@section('styles')
<link rel="stylesheet" href="{{ asset('css/cleanup-tool.css') }}">
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm cleanup-card">
                <div class="card-header bg-white">
                    <h3 class="card-title">
                        <i class="fas fa-trash-alt text-danger me-2"></i>
                        เครื่องมือลบไฟล์ขยะ
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info shadow-sm">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">เกี่ยวกับเครื่องมือนี้</h5>
                                <p class="mb-0">เครื่องมือนี้ใช้สำหรับค้นหาและลบไฟล์ที่ไม่มีการอ้างอิงในฐานข้อมูล เช่น รูปภาพ, ไฟล์ Settings, Categories, Profiles ที่อัพโหลดแล้วไม่ได้ใช้งาน เพื่อช่วยประหยัดพื้นที่เก็บข้อมูล</p>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4 cleanup-card">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-filter me-2"></i>
                                ตัวเลือกการค้นหา
                            </h5>
                            <div class="form-group mb-3">
                                <label for="file-type" class="form-label">ประเภทไฟล์ที่ต้องการตรวจสอบ</label>
                                <div class="input-group">
                                    <select id="file-type" class="form-select">
                                        <option value="all">ทั้งหมด</option>
                                        <option value="images">รูปภาพเท่านั้น</option>
                                        <option value="files">ไฟล์อื่นๆ เท่านั้น</option>
                                        <option value="settings">ไฟล์ Settings</option>
                                        <option value="categories">ไฟล์ Categories</option>
                                        <option value="profiles">ไฟล์ Profiles</option>
                                    </select>
                                    <button id="scan-btn" class="btn btn-primary px-4">
                                        <i class="fas fa-search me-2"></i> ค้นหาไฟล์ขยะ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="loading" class="text-center my-5 py-5 cleanup-loading" style="display: none;">
                        <div class="spinner"></div>
                        <p class="mt-3 fs-5">กำลังค้นหาไฟล์ขยะ กรุณารอสักครู่...</p>
                    </div>

                    <div id="results-container" style="display: none;">
                        <div class="card shadow-sm mb-4 cleanup-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center flex-wrap">
                                    <h4 id="results-summary" class="mb-0 text-primary">
                                        <i class="fas fa-search me-2"></i>
                                        <span id="results-count-display">0</span> ไฟล์ขยะ
                                    </h4>
                                    <div class="mt-3 mt-md-0">
                                        <button id="select-all-btn" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-check-square me-1"></i> เลือกทั้งหมด
                                        </button>
                                        <button id="deselect-all-btn" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-square me-1"></i> ยกเลิกการเลือก
                                        </button>
                                        <button id="delete-selected-btn" class="btn btn-danger" disabled>
                                            <i class="fas fa-trash-alt me-1"></i> ลบไฟล์ที่เลือก
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="no-results" class="alert alert-success shadow-sm empty-state" style="display: none;">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">ไม่พบไฟล์ขยะ</h5>
                                    <p class="mb-0">ระบบไม่พบไฟล์ที่ไม่ได้ใช้งานในระบบ</p>
                                </div>
                            </div>
                        </div>

                        <div id="files-container" class="card shadow-sm cleanup-card" style="display: none;">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 50px;">
                                                    <div class="form-check">
                                                        <input type="checkbox" class="form-check-input" id="select-all-files">
                                                    </div>
                                                </th>
                                                <th style="width: 100px;">ตัวอย่าง</th>
                                                <th>ชื่อไฟล์</th>
                                                <th>ประเภท</th>
                                                <th>ขนาด</th>
                                                <th>แก้ไขล่าสุด</th>
                                            </tr>
                                        </thead>
                                        <tbody id="files-table-body"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Global variables for cleanup.js
    window.cleanupRoutes = {
        scan: '{{ route("admin.cleanup.scan") }}',
        delete: '{{ route("admin.cleanup.delete") }}'
    };
    window.csrfToken = '{{ csrf_token() }}';
    window.storageUrl = '{{ asset("storage") }}';
</script>
<script src="{{ asset('js/cleanup.js') }}"></script>
@endsection
