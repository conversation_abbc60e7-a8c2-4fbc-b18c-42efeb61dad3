@extends('layouts.admin')

@section('title', 'เพิ่มเอกสารใหม่ - ระบบจัดการคลังเอกสารโบราณดิจิทัล')

@section('styles')
<!-- Uppy CSS -->
<link href="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.css" rel="stylesheet">

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

<style>
    /* General styling */
    body {
        font-family: 'Sarabun', sans-serif;
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0,0,0,.125);
        border-radius: 0.25rem;
    }

    .card-header {
        padding: 0.5rem 1rem;
        margin-bottom: 0;
        background-color: rgba(0,0,0,.03);
        border-bottom: 1px solid rgba(0,0,0,.125);
    }

    .card-body {
        flex: 1 1 auto;
        padding: 1rem 1rem;
    }

    /* Form controls */
    .form-control, .form-select {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    }

    .form-control:focus, .form-select:focus {
        color: #212529;
        background-color: #fff;
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
    }

    .form-label {
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    /* Buttons */
    .btn {
        display: inline-block;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        text-align: center;
        text-decoration: none;
        vertical-align: middle;
        cursor: pointer;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        border-radius: 0.25rem;
        transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    }

    .btn-primary {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn-primary:hover {
        color: #fff;
        background-color: #0b5ed7;
        border-color: #0a58ca;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        color: #fff;
        background-color: #5c636a;
        border-color: #565e64;
    }

    /* Image and file previews */
    .preview-image {
        max-width: 150px;
        max-height: 150px;
        margin: 10px;
        border-radius: 8px;
        padding: 5px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }

    .preview-image:hover {
        transform: scale(1.05);
    }

    .image-preview-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 15px;
        gap: 15px;
    }

    /* Progress bar */
    .progress {
        height: 20px;
        margin-bottom: 20px;
        overflow: hidden;
        background-color: #f5f5f5;
        border-radius: 10px;
        box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
        display: none;
    }

    .progress-bar {
        float: left;
        width: 0%;
        height: 100%;
        font-size: 12px;
        line-height: 20px;
        color: #fff;
        text-align: center;
        background: linear-gradient(135deg, #4a6bff 0%, #2541b8 100%);
        box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
        transition: width .6s ease;
    }

    /* File preview styling */
    .file-preview-container {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 15px;
    }

    .file-preview-card {
        border-radius: 8px;
        overflow: hidden;
        width: 150px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        background-color: #f8f9fa;
    }

    .file-preview-card:hover {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-5px);
    }

    .file-preview-card .card-body {
        padding: 10px;
    }

    .file-preview-card .card-title {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .file-icon {
        font-size: 24px;
        margin-right: 10px;
        color: #4a6bff;
    }

    .delete-file-btn {
        border-radius: 50%;
        width: 30px;
        height: 30px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
    }

    .delete-file-btn:hover {
        background-color: #dc3545;
        color: white;
        transform: scale(1.1);
    }

    /* Uppy customization */
    .uppy-Dashboard-inner {
        width: 100% !important;
        height: 350px !important;
        border-radius: 8px !important;
        border: 2px dashed #4a6bff !important;
    }

    .uppy-Dashboard-AddFiles-title {
        font-size: 16px !important;
        font-weight: 500 !important;
        color: #4a6bff !important;
    }

    .uppy-Dashboard-browse {
        color: #2541b8 !important;
    }

    .uppy-Dashboard-dropFilesHereHint {
        border-radius: 8px !important;
    }

    .uppy-Dashboard-Item-previewInnerWrap {
        border-radius: 8px !important;
    }

    .uppy-Dashboard-Item {
        border-radius: 8px !important;
    }

    /* File type buttons */
    .file-type-btn {
        transition: all 0.3s;
        border-radius: 8px;
    }

    .file-type-btn:hover {
        transform: translateY(-2px);
    }

    .file-type-btn.active {
        background-color: #4a6bff;
        color: white;
        border-color: #4a6bff;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .card-header {
            padding: 12px 15px;
        }

        .card-body {
            padding: 15px;
        }

        .btn {
            padding: 8px 16px;
        }

        .uppy-Dashboard-inner {
            height: 250px !important;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">เพิ่มเอกสารใหม่</h1>
        <a href="{{ route('admin.documents') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการเอกสาร
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">ข้อมูลเอกสาร</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.documents.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <!-- ข้อมูลพื้นฐาน -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ข้อมูลพื้นฐาน</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">ชื่อเอกสาร <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="identifier_no" class="form-label">รหัสเอกสาร</label>
                                    <input type="text" class="form-control @error('identifier_no') is-invalid @enderror" id="identifier_no" name="identifier_no" value="{{ old('identifier_no') }}">
                                    <small class="text-muted">รหัสเอกสารต้องไม่ซ้ำกับเอกสารอื่นในระบบ</small>
                                    @error('identifier_no')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="other_title1" class="form-label">ชื่อเรื่องอื่น 1</label>
                                    <input type="text" class="form-control @error('other_title1') is-invalid @enderror" id="other_title1" name="other_title1" value="{{ old('other_title1') }}">
                                    @error('other_title1')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="other_title2" class="form-label">ชื่อเรื่องอื่น 2</label>
                                    <input type="text" class="form-control @error('other_title2') is-invalid @enderror" id="other_title2" name="other_title2" value="{{ old('other_title2') }}">
                                    @error('other_title2')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="other_title3" class="form-label">ชื่อเรื่องอื่น 3</label>
                                    <input type="text" class="form-control @error('other_title3') is-invalid @enderror" id="other_title3" name="other_title3" value="{{ old('other_title3') }}">
                                    @error('other_title3')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                        <option value="">เลือกหมวดหมู่</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="year" class="form-label">ปี</label>
                                    <input type="text" class="form-control @error('year') is-invalid @enderror" id="year" name="year" value="{{ old('year') }}">
                                    @error('year')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="document_type_id" class="form-label">ประเภทเอกสาร</label>
                                    <select class="form-select @error('document_type_id') is-invalid @enderror" id="document_type_id" name="document_type_id">
                                        <option value="">เลือกประเภทเอกสาร</option>
                                        @foreach($documentTypes as $documentType)
                                            <option value="{{ $documentType->id }}" {{ old('document_type_id') == $documentType->id ? 'selected' : '' }}>
                                                {{ $documentType->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('document_type_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="material_id" class="form-label">วัสดุ</label>
                                    <select class="form-select @error('material_id') is-invalid @enderror" id="material_id" name="material_id">
                                        <option value="">เลือกวัสดุ</option>
                                        @foreach($materials as $material)
                                            <option value="{{ $material->id }}" {{ old('material_id') == $material->id ? 'selected' : '' }}>
                                                {{ $material->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('material_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="quantity" class="form-label">จำนวน</label>
                                    <input type="text" class="form-control @error('quantity') is-invalid @enderror" id="quantity" name="quantity" value="{{ old('quantity') }}">
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลเพิ่มเติม -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ข้อมูลเพิ่มเติม</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="language_id" class="form-label">ภาษา</label>
                                    <select class="form-select @error('language_id') is-invalid @enderror" id="language_id" name="language_id">
                                        <option value="">เลือกภาษา</option>
                                        @foreach($languages as $language)
                                            <option value="{{ $language->id }}" {{ old('language_id') == $language->id ? 'selected' : '' }}>
                                                {{ $language->name }} {{ $language->code ? '(' . $language->code . ')' : '' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('language_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="script_id" class="form-label">ตัวอักษร</label>
                                    <select class="form-select @error('script_id') is-invalid @enderror" id="script_id" name="script_id">
                                        <option value="">เลือกตัวอักษร</option>
                                        @foreach($scripts as $script)
                                            <option value="{{ $script->id }}" {{ old('script_id') == $script->id ? 'selected' : '' }}>
                                                {{ $script->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('script_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="description" class="form-label">คำอธิบาย</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="brief" class="form-label">สรุปย่อ</label>
                                    <textarea class="form-control @error('brief') is-invalid @enderror" id="brief" name="brief" rows="3">{{ old('brief') }}</textarea>
                                    @error('brief')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="remark" class="form-label">หมายเหตุ</label>
                                    <textarea class="form-control @error('remark') is-invalid @enderror" id="remark" name="remark" rows="3">{{ old('remark') }}</textarea>
                                    @error('remark')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="manuscript_condition" class="form-label">สภาพเอกสาร</label>
                                    <input type="text" class="form-control @error('manuscript_condition') is-invalid @enderror" id="manuscript_condition" name="manuscript_condition" value="{{ old('manuscript_condition') }}">
                                    @error('manuscript_condition')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลสถานที่ -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ข้อมูลสถานที่</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="location" class="form-label">สถานที่</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" id="location" name="location" value="{{ old('location') }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="country" class="form-label">ประเทศ</label>
                                    <select class="form-select @error('country') is-invalid @enderror" id="country" name="country">
                                        <option value="">-- เลือกประเทศ --</option>
                                        @foreach($countries as $country)
                                            <option value="{{ $country->code }}" {{ old('country') == $country->code ? 'selected' : '' }}>{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="latitude" class="form-label">ละติจูด</label>
                                        <input type="text" class="form-control @error('latitude') is-invalid @enderror" id="latitude" name="latitude" value="{{ old('latitude') }}">
                                        @error('latitude')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="longitude" class="form-label">ลองจิจูด</label>
                                        <input type="text" class="form-control @error('longitude') is-invalid @enderror" id="longitude" name="longitude" value="{{ old('longitude') }}">
                                        @error('longitude')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ไฟล์และรูปภาพ -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ไฟล์และรูปภาพ</h6>
                            </div>
                            <div class="card-body">
                                <!-- Unified Uppy Uploader -->
                                <div class="mb-3">
                                    <label class="form-label">อัพโหลดไฟล์เอกสารและรูปภาพ</label>

                                    <!-- Uppy Dashboard Container -->
                                    <div id="uppy-dashboard"
                                         data-upload-url="{{ route('admin.documents.upload-files') }}"
                                         data-is-create-page="true"
                                         data-allowed-file-types="image/*,.pdf,.mp3,.wav,.ogg,.mp4,.webm,.mov"
                                         data-max-file-size="50"
                                         data-max-number-of-files="10">
                                    </div>

                                    <div class="form-text mt-2">
                                        <i class="fas fa-info-circle me-1"></i>
                                        รองรับไฟล์ PDF (สูงสุด 50MB), รูปภาพ (JPG, PNG, GIF สูงสุด 5MB), เสียง (MP3, WAV, OGG สูงสุด 20MB), วิดีโอ (MP4, WEBM, MOV สูงสุด 50MB)
                                    </div>
                                </div>

                                <!-- Container to display uploaded files -->
                                <div class="mt-3">
                                    <h6>ไฟล์ที่อัพโหลดแล้ว</h6>
                                    <div id="file-preview-container" class="d-flex flex-wrap gap-2 mt-2 mb-3">
                                        <!-- Uploaded files will be displayed here -->
                                        <div class="text-muted small" id="no-files-message">ยังไม่มีไฟล์ที่อัพโหลด</div>
                                    </div>
                                </div>

                                <!-- Hidden inputs to store uploaded files -->
                                <input type="hidden" name="uploaded_files" id="uploaded-files" value="">
                                <input type="hidden" name="temp_file_path" id="temp-document-file-path">
                                <input type="hidden" name="temp_file_type" id="temp-document-file-type" value="document">

                                @error('images.*')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror

                                @error('file')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="reset" class="btn btn-secondary me-2">
                        <i class="fas fa-undo me-2"></i>รีเซ็ต
                    </button>
                    <button type="submit" class="btn btn-primary" id="save-document-btn">
                        <i class="fas fa-save me-2"></i>บันทึกเอกสาร
                    </button>
                    <div id="submit-status" class="mt-2"></div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<!-- Uppy JS -->
<script src="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.js"></script>
<script src="https://releases.transloadit.com/uppy/locales/v3.0.1/th_TH.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Font Awesome -->
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>

<!-- Custom Document Create JS -->
<script src="{{ asset('js/document-create.js') }}?v={{ time() }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check for duplicate identifier
        const identifierInput = document.getElementById('identifier_no');
        const identifierFeedback = document.createElement('div');
        identifierFeedback.className = 'mt-1';
        identifierInput.parentNode.insertBefore(identifierFeedback, identifierInput.nextSibling.nextSibling);

        let checkTimeout;

        identifierInput.addEventListener('input', function() {
            const identifier = this.value.trim();
            clearTimeout(checkTimeout);

            // Clear feedback if empty
            if (!identifier) {
                identifierFeedback.innerHTML = '';
                return;
            }

            // Set a timeout to avoid too many requests
            checkTimeout = setTimeout(function() {
                // Make AJAX request to check identifier
                fetch('{{ route("admin.documents.check-identifier") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ identifier: identifier })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.exists) {
                        identifierFeedback.innerHTML = `<div class="alert alert-danger py-1 px-2 mt-1 mb-0"><i class="fas fa-exclamation-triangle me-1"></i> ${data.message}</div>`;
                        identifierInput.classList.add('is-invalid');
                    } else {
                        identifierFeedback.innerHTML = `<div class="alert alert-success py-1 px-2 mt-1 mb-0"><i class="fas fa-check-circle me-1"></i> ${data.message}</div>`;
                        identifierInput.classList.remove('is-invalid');
                    }
                })
                .catch(error => {
                    console.error('Error checking identifier:', error);
                });
            }, 500); // Wait 500ms after user stops typing
        });

        // Add event listener for page unload to clean up temporary files
        window.addEventListener('beforeunload', function() {
            // Send a cleanup request to the server
            // Using navigator.sendBeacon to ensure the request is sent even if the page is unloading
            if (navigator.sendBeacon) {
                const formData = new FormData();
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                navigator.sendBeacon('{{ route("admin.documents.temp-cleanup") }}', formData);
            } else {
                // Fallback for browsers that don't support sendBeacon
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '{{ route("admin.documents.temp-cleanup") }}', false); // Synchronous request
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.send('_token=' + document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            }
        });

        // File type selection buttons
        const fileTypeButtons = document.querySelectorAll('.file-type-btn');
        const uploadSections = document.querySelectorAll('.upload-section');
        const fileUploadTypeInput = document.getElementById('file-upload-type');
        const uploadedImagesInput = document.getElementById('uploaded-images');

        // Check if there are any existing uploaded files and lock the interface if needed
        checkExistingFiles();

        // File inputs
        const documentInput = document.getElementById('document-file');
        const audioInput = document.getElementById('audio-file');
        const videoInput = document.getElementById('video-file');
        const youtubeInput = document.getElementById('youtube-link');

        // Handle file type selection
        fileTypeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const fileType = this.getAttribute('data-type');

                // Update hidden input with selected file type
                fileUploadTypeInput.value = fileType;

                // Reset active state on all buttons
                fileTypeButtons.forEach(btn => btn.classList.remove('active', 'btn-primary'));
                fileTypeButtons.forEach(btn => btn.classList.add('btn-outline-primary'));

                // Set active state on clicked button
                this.classList.remove('btn-outline-primary');
                this.classList.add('active', 'btn-primary');

                // Hide all upload sections
                uploadSections.forEach(section => section.style.display = 'none');

                // Show the selected upload section
                const selectedSection = document.getElementById(`${fileType}-upload-section`);
                if (selectedSection) {
                    selectedSection.style.display = 'block';
                }
            });
        });

        // Handle file uploads for document, audio, and video
        if (documentInput) {
            documentInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFileUpload(this.files[0], 'document');
                }
            });
        }

        if (audioInput) {
            audioInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFileUpload(this.files[0], 'audio');
                }
            });
        }

        if (videoInput) {
            videoInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFileUpload(this.files[0], 'video');
                }
            });
        }

        // Function to handle file uploads
        function handleFileUpload(file, fileType) {
            // Check if a file is already uploaded
            if ($(`#temp-${fileType}-file-path`).val() || $(`#${fileType}-file-info`).length > 0) {
                // Show error message
                const statusDiv = $(`#${fileType}-upload-status`);
                statusDiv.html('<div class="alert alert-warning">A file is already uploaded. Please delete it first to upload a new file.</div>');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('file_type', fileType);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

            // Show progress bar
            const progressBar = $(`#${fileType}UploadProgress`);
            const progressBarInner = progressBar.find('.progress-bar');
            progressBar.show();
            progressBarInner.css('width', '0%').attr('aria-valuenow', 0);

            // Update status
            const statusDiv = $(`#${fileType}-upload-status`);
            statusDiv.html('<div class="alert alert-info">Uploading file...</div>');

            // Create a variable to track if the form is being submitted
            window.isSubmittingForm = false;

            $.ajax({
                url: '/admin/documents/temp/upload-file',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            var percent = Math.round((e.loaded / e.total) * 100);
                            progressBarInner.css('width', percent + '%').attr('aria-valuenow', percent);
                            progressBarInner.text(percent + '%');
                        }
                    });
                    return xhr;
                },
                success: function(response) {
                    // Show success message
                    statusDiv.html('<div class="alert alert-success">File uploaded successfully!</div>');

                    // Store the file path in hidden input
                    $(`#temp-${fileType}-file-path`).val(response.file_path);
                    $(`#temp-${fileType}-file-type`).val(response.file_type);

                    // Display the uploaded file with delete button
                    displayUploadedFile(fileType, response.file_path, response.file_name || file.name);

                    // Add a hidden input to store the file information for form submission
                    if (!$(`#${fileType}-file-info`).length) {
                        const fileInfo = $('<input>').attr({
                            type: 'hidden',
                            id: `${fileType}-file-info`,
                            name: 'temp_file_path',
                            value: response.file_path
                        });
                        const fileTypeInfo = $('<input>').attr({
                            type: 'hidden',
                            id: `${fileType}-file-type-info`,
                            name: 'temp_file_type',
                            value: fileType
                        });
                        $(form).append(fileInfo).append(fileTypeInfo);
                    } else {
                        $(`#${fileType}-file-info`).val(response.file_path);
                        $(`#${fileType}-file-type-info`).val(fileType);
                    }
                },
                error: function(xhr, status, error) {
                    var errorMsg = 'Failed to upload file';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    statusDiv.html('<div class="alert alert-danger">' + errorMsg + '</div>');
                    console.error('Upload error:', status, error, xhr.responseText);
                },
                complete: function() {
                    // Hide progress bar after a short delay
                    setTimeout(function() {
                        progressBar.hide();
                    }, 2000);
                }
            });
        }

        // YouTube link preview
        if (youtubeInput) {
            youtubeInput.addEventListener('input', function() {
                const youtubeUrl = this.value.trim();
                const youtubePreview = document.getElementById('youtube-preview');

                if (youtubeUrl && isValidYouTubeUrl(youtubeUrl)) {
                    const videoId = extractYouTubeVideoId(youtubeUrl);
                    if (videoId) {
                        youtubePreview.innerHTML = `
                            <div class="ratio ratio-16x9">
                                <iframe src="https://www.youtube.com/embed/${videoId}" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                            </div>
                        `;
                    }
                } else {
                    youtubePreview.innerHTML = '';
                }
            });
        }

        // Function to validate YouTube URL
        function isValidYouTubeUrl(url) {
            const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
            return youtubeRegex.test(url);
        }

        // Function to extract YouTube video ID
        function extractYouTubeVideoId(url) {
            const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
            const match = url.match(regExp);
            return (match && match[2].length === 11) ? match[2] : null;
        }

        // Function to check for existing uploaded files and lock the interface if needed
        function checkExistingFiles() {
            console.log('Checking for existing files in create page...');

            // Check if there are any file previews or hidden inputs with values in the DOM
            const fileTypes = ['document', 'audio', 'video'];
            let fileFound = false;

            // First check for any delete buttons which indicate an existing file
            const existingFiles = $('.delete-file-btn');
            console.log('Existing delete buttons found:', existingFiles.length);

            if (existingFiles.length > 0) {
                console.log('Existing file found via delete button, locking interface');
                fileFound = true;
            }

            // If no file found yet, check each file type
            if (!fileFound) {
                fileTypes.forEach(fileType => {
                    const previewDiv = $(`#${fileType}-file-preview`);
                    const hiddenInput = $(`#temp-${fileType}-file-path`);
                    const fileInfo = $(`#${fileType}-file-info`);

                    console.log(`${fileType} preview children:`, previewDiv.children().length);
                    console.log(`${fileType} hidden input exists:`, hiddenInput.length > 0);
                    console.log(`${fileType} hidden input value:`, hiddenInput.val());
                    console.log(`${fileType} file info exists:`, fileInfo.length > 0);

                    // If there are any children in the preview div or a value in the hidden input, a file is already uploaded
                    if (previewDiv.children().length > 0 ||
                        (hiddenInput.length > 0 && hiddenInput.val()) ||
                        fileInfo.length > 0) {

                        console.log(`Existing ${fileType} file found, locking interface`);
                        fileFound = true;
                        return false; // Break out of forEach
                    }
                });
            }

            // If a file was found, lock the interface
            if (fileFound) {
                console.log('Locking interface due to existing file');

                // Disable all file type buttons
                $('.file-type-btn').each(function() {
                    $(this).prop('disabled', true);
                    $(this).addClass('disabled');
                    $(this).css('cursor', 'not-allowed');
                    $(this).attr('title', 'Please delete the current file first to upload a different type of file');
                });

                // Add a message above the file type buttons if it doesn't exist
                if ($('#file-type-lock-message').length === 0) {
                    $('.file-type-btn').first().parent().parent().before(
                        `<div id="file-type-lock-message" class="alert alert-warning mb-3">
                            <i class="fas fa-lock me-2"></i>
                            <strong>File upload locked:</strong> You have already uploaded a file. Please delete it first to upload a different type of file.
                        </div>`
                    );
                }
            } else {
                console.log('No existing files found, interface remains unlocked');
            }
        }

        // Function to display uploaded file with delete button
        function displayUploadedFile(fileType, filePath, fileName) {
            const previewDiv = $(`#${fileType}-file-preview`);
            const fileExtension = fileName.split('.').pop().toLowerCase();

            // Clear previous preview
            previewDiv.empty();

            // Create file display card
            const card = $('<div class="card mb-3"></div>');
            const cardBody = $('<div class="card-body p-3"></div>');

            // Add file icon based on type
            let fileIcon = 'fa-file';
            if (fileType === 'document') {
                if (['pdf'].includes(fileExtension)) {
                    fileIcon = 'fa-file-pdf';
                } else if (['doc', 'docx'].includes(fileExtension)) {
                    fileIcon = 'fa-file-word';
                } else if (['xls', 'xlsx'].includes(fileExtension)) {
                    fileIcon = 'fa-file-excel';
                } else if (['ppt', 'pptx'].includes(fileExtension)) {
                    fileIcon = 'fa-file-powerpoint';
                } else {
                    fileIcon = 'fa-file-alt';
                }
            } else if (fileType === 'audio') {
                fileIcon = 'fa-file-audio';
            } else if (fileType === 'video') {
                fileIcon = 'fa-file-video';
            }

            // Create file info display
            const fileInfo = $(
                `<div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas ${fileIcon} fa-2x text-primary"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${fileName}</h6>
                        <small class="text-muted">Uploaded successfully</small>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger delete-file-btn" data-file-type="${fileType}" data-file-path="${filePath}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>`
            );

            cardBody.append(fileInfo);
            card.append(cardBody);
            previewDiv.append(card);

            // Add event listener for delete button
            previewDiv.find('.delete-file-btn').on('click', function() {
                const fileType = $(this).data('file-type');
                const filePath = $(this).data('file-path');
                deleteUploadedFile(fileType, filePath);
            });

            // Hide only the file input element, not the entire container
            $(`#${fileType}-file`).hide();

            // Add a disabled browse button that replaces the file input
            const browseBtn = $(`<button type="button" class="btn btn-secondary form-control" disabled>
                <i class="fas fa-file-upload me-2"></i>File already uploaded
            </button>`);
            $(`#${fileType}-file`).after(browseBtn);

            // Add a message indicating that the file is already uploaded
            const messageHtml = `<div class="alert alert-info mt-2">
                <i class="fas fa-info-circle me-2"></i>
                A file has been uploaded. To upload a different file, please delete the current file first.
            </div>`;

            // Add the message after the preview
            if ($(`#${fileType}-upload-message`).length === 0) {
                previewDiv.after(`<div id="${fileType}-upload-message">${messageHtml}</div>`);
            }

            // Disable all file type buttons
            $('.file-type-btn').each(function() {
                $(this).prop('disabled', true);
                $(this).addClass('disabled');
                $(this).css('cursor', 'not-allowed');
                $(this).attr('title', 'Please delete the current file first to upload a different type of file');
            });

            // Add a message above the file type buttons
            if ($('#file-type-lock-message').length === 0) {
                $('.file-type-btn').first().parent().parent().before(
                    `<div id="file-type-lock-message" class="alert alert-warning mb-3">
                        <i class="fas fa-lock me-2"></i>
                        <strong>File upload locked:</strong> You have already uploaded a file. Please delete it first to upload a different type of file.
                    </div>`
                );
            }

            // Hide all upload sections except the current one
            $('.upload-section').each(function() {
                if ($(this).attr('id') !== `${fileType}-upload-section`) {
                    $(this).hide();
                }
            });

            // Keep the current upload section visible but hide the file input
            $(`#${fileType}-upload-section`).find('input[type="file"]').hide();
            $(`#${fileType}-upload-section`).find('label[for="${fileType}-file"]').hide();
            $(`#${fileType}-upload-section`).find('small.text-muted').hide();
        }

        // Function to delete uploaded file
        function deleteUploadedFile(fileType, filePath) {
            // Clear the file input
            $(`#${fileType}-file`).val('');

            // Clear the hidden inputs
            $(`#temp-${fileType}-file-path`).val('');
            $(`#${fileType}-file-info`).remove();
            $(`#${fileType}-file-type-info`).remove();

            // Clear the preview
            $(`#${fileType}-file-preview`).empty();

            // Remove the upload message
            $(`#${fileType}-upload-message`).remove();

            // Show the file input again and remove the disabled browse button
            $(`#${fileType}-file`).show();
            $(`#${fileType}-file`).next('button').remove();

            // Show message
            $(`#${fileType}-upload-status`).html('<div class="alert alert-info">File removed. You can upload a new file.</div>');

            // Re-enable all file type buttons
            $('.file-type-btn').each(function() {
                $(this).prop('disabled', false);
                $(this).removeClass('disabled');
                $(this).css('cursor', 'pointer');
                $(this).removeAttr('title');
            });

            // Remove the lock message
            $('#file-type-lock-message').remove();

            // Show all upload sections
            $('#upload-sections').show();

            // Send AJAX request to delete the file from server
            if (filePath) {
                $.ajax({
                    url: '/admin/documents/temp/delete-file',
                    type: 'POST',
                    data: {
                        file_path: filePath,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        console.log('File deleted from server:', response);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error deleting file:', error);
                    }
                });
            }

            // The AJAX request to delete the file is now implemented above
        }

        // Check for uploaded images before form submission
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Prevent multiple submissions
                if (window.isSubmittingForm) {
                    e.preventDefault();
                    return false;
                }

                window.isSubmittingForm = true;
                e.preventDefault(); // Prevent default form submission

                // Show loading indicator
                const submitBtn = document.getElementById('save-document-btn');
                const submitStatus = document.getElementById('submit-status');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';
                submitStatus.innerHTML = '<div class="alert alert-info mt-2">กำลังบันทึกเอกสาร กรุณารอสักครู่...</div>';

                // Log the current value of uploaded images
                console.log('Current uploaded images:', uploadedImagesInput.value);

                // If no value, check if there are any images in the Dropzone
                if (!uploadedImagesInput.value && window.documentDropzone) {
                    const processedFiles = window.documentDropzone.files.filter(file => file.status === 'success');

                    if (processedFiles.length > 0) {
                        // There are successfully uploaded files but the input is empty
                        // This can happen if the user uploaded files but the response wasn't properly stored
                        console.warn('Files were uploaded but not stored in the form input');

                        // You can show a warning to the user
                        Swal.fire({
                            title: 'คำเตือน',
                            text: 'พบรูปภาพที่อัพโหลดแล้วแต่ยังไม่ได้บันทึกในฟอร์ม กรุณาลองอัพโหลดอีกครั้ง',
                            icon: 'warning',
                            confirmButtonText: 'ตกลง'
                        });

                        // Re-enable the submit button
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>บันทึกเอกสาร';
                        submitStatus.innerHTML = '<div class="alert alert-warning mt-2">การบันทึกถูกยกเลิก โปรดตรวจสอบข้อมูลและลองอีกครั้ง</div>';
                        window.isSubmittingForm = false;
                        return false;
                    }
                }

                // Check if any file has been uploaded via AJAX
                const documentFilePath = document.getElementById('temp-document-file-path');
                const audioFilePath = document.getElementById('temp-audio-file-path');
                const videoFilePath = document.getElementById('temp-video-file-path');
                const documentFileInfo = document.getElementById('document-file-info');
                const audioFileInfo = document.getElementById('audio-file-info');
                const videoFileInfo = document.getElementById('video-file-info');

                // If a file was uploaded via AJAX, make sure it's included in the form submission
                if ((documentFilePath && documentFilePath.value) || (documentFileInfo && documentFileInfo.value)) {
                    console.log('Document file uploaded via AJAX:', documentFilePath ? documentFilePath.value : documentFileInfo.value);
                } else if ((audioFilePath && audioFilePath.value) || (audioFileInfo && audioFileInfo.value)) {
                    console.log('Audio file uploaded via AJAX:', audioFilePath ? audioFilePath.value : audioFileInfo.value);
                } else if ((videoFilePath && videoFilePath.value) || (videoFileInfo && videoFileInfo.value)) {
                    console.log('Video file uploaded via AJAX:', videoFilePath ? videoFilePath.value : videoFileInfo.value);
                }

                // Submit the form
                console.log('Submitting form...');

                // Preserve document files by adding a hidden input with current file path
                const documentFilePath = document.getElementById('temp-document-file-path');
                const audioFilePath = document.getElementById('temp-audio-file-path');
                const videoFilePath = document.getElementById('temp-video-file-path');

                if (documentFilePath && documentFilePath.value) {
                    console.log('Preserving document file path:', documentFilePath.value);
                    const preserveFileInput = document.createElement('input');
                    preserveFileInput.type = 'hidden';
                    preserveFileInput.name = 'preserve_file_path';
                    preserveFileInput.value = 'true';
                    form.appendChild(preserveFileInput);
                } else if (audioFilePath && audioFilePath.value) {
                    console.log('Preserving audio file path:', audioFilePath.value);
                    const preserveFileInput = document.createElement('input');
                    preserveFileInput.type = 'hidden';
                    preserveFileInput.name = 'preserve_file_path';
                    preserveFileInput.value = 'true';
                    form.appendChild(preserveFileInput);
                } else if (videoFilePath && videoFilePath.value) {
                    console.log('Preserving video file path:', videoFilePath.value);
                    const preserveFileInput = document.createElement('input');
                    preserveFileInput.type = 'hidden';
                    preserveFileInput.name = 'preserve_file_path';
                    preserveFileInput.value = 'true';
                    form.appendChild(preserveFileInput);
                }

                // Check identifier one last time before submitting
                const identifierInput = document.getElementById('identifier_no');
                const identifier = identifierInput.value.trim();

                if (identifier) {
                    fetch('{{ route("admin.documents.check-identifier") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({ identifier: identifier })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.exists) {
                            // Show error and prevent form submission
                            Swal.fire({
                                title: 'รหัสเอกสารซ้ำ',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'ตกลง'
                            });
                            // Scroll to identifier field
                            identifierInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            identifierInput.focus();
                        } else {
                            // Submit the form if identifier is valid
                            form.submit();
                        }
                    })
                    .catch(error => {
                        console.error('Error checking identifier:', error);
                        // Submit anyway if there's an error checking
                        form.submit();
                    });
                } else {
                    // No identifier, just submit the form
                    form.submit();
                }
            });
        }
    });
</script>
@endsection
