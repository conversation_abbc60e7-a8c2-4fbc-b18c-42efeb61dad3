@extends('layouts.admin')

@section('title', 'แก้ไขเอกสาร')

@section('styles')
<style>
    /* General styling */
    body {
        font-family: 'Sarabun', sans-serif;
    }

    .form-label {
        font-weight: 500;
    }

    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }

    .btn-primary {
        background-color: #4e73df;
        border-color: #4e73df;
    }

    .btn-primary:hover {
        background-color: #2e59d9;
        border-color: #2653d4;
    }

    .btn-secondary {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        color: #fff;
        background-color: #5c636a;
        border-color: #565e64;
    }

    .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 24px;
        border: none;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .card:hover {
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        padding: 0.5rem 1rem;
        margin-bottom: 0;
        background-color: rgba(0,0,0,.03);
        border-bottom: 1px solid rgba(0,0,0,.125);
    }

    .card-body {
        flex: 1 1 auto;
        padding: 1rem 1rem;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        vertical-align: top;
        border-color: #dee2e6;
    }

    .table-bordered {
        border: 1px solid #dee2e6;
    }

    .table-hover tbody tr:hover {
        color: #212529;
        background-color: rgba(0,0,0,.075);
    }

    .badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .bg-info {
        background-color: #0dcaf0 !important;
    }

    /* File gallery styling */
    .file-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 10px;
        margin-bottom: 20px;
    }

    .file-card {
        display: flex;
        flex-direction: column;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        background-color: white;
        border: 1px solid #e9ecef;
    }

    .file-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .file-card-main {
        border: 2px solid #ffc107;
        box-shadow: 0 4px 10px rgba(255, 193, 7, 0.3);
    }

    .file-icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 120px;
        color: white;
        position: relative;
    }

    .main-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #ffc107;
        color: #212529;
        padding: 3px 8px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .file-info {
        padding: 15px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .file-name {
        font-weight: 600;
        margin-bottom: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .file-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .file-size {
        color: #6c757d;
        font-size: 0.85rem;
    }

    .file-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: auto;
    }

    /* Uploaded files styling */
    .uploaded-files-container {
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .uploaded-files-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">แก้ไขเอกสาร: {{ $document->title }}</h1>
        <div>
            <a href="{{ route('documents.show', $document->id) }}" class="btn btn-info me-2" target="_blank">
                <i class="fas fa-eye me-2"></i>ดูเอกสาร
            </a>
            <a href="{{ route('admin.documents') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการเอกสาร
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">ข้อมูลเอกสาร</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.documents.update', $document->id) }}" method="POST" enctype="multipart/form-data" id="document-edit-form">
                @csrf
                @method('PUT')
                <!-- Hidden input to preserve file path - ALWAYS set to true to prevent file loss -->
                <input type="hidden" name="preserve_file_path" id="preserve_file_path" value="true">

                <div class="row">
                    <!-- ข้อมูลพื้นฐาน -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ข้อมูลพื้นฐาน</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">ชื่อเอกสาร <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $document->title) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="identifier_no" class="form-label">รหัสเอกสาร</label>
                                    <input type="text" class="form-control @error('identifier_no') is-invalid @enderror" id="identifier_no" name="identifier_no" value="{{ old('identifier_no', $document->identifier_no) }}">
                                    <small class="text-muted">รหัสเอกสารต้องไม่ซ้ำกับเอกสารอื่นในระบบ</small>
                                    @error('identifier_no')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="other_title1" class="form-label">ชื่อเรื่องอื่น 1</label>
                                    <input type="text" class="form-control @error('other_title1') is-invalid @enderror" id="other_title1" name="other_title1" value="{{ old('other_title1', $document->other_title1) }}">
                                    @error('other_title1')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="other_title2" class="form-label">ชื่อเรื่องอื่น 2</label>
                                    <input type="text" class="form-control @error('other_title2') is-invalid @enderror" id="other_title2" name="other_title2" value="{{ old('other_title2', $document->other_title2) }}">
                                    @error('other_title2')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="other_title3" class="form-label">ชื่อเรื่องอื่น 3</label>
                                    <input type="text" class="form-control @error('other_title3') is-invalid @enderror" id="other_title3" name="other_title3" value="{{ old('other_title3', $document->other_title3) }}">
                                    @error('other_title3')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                        <option value="">เลือกหมวดหมู่</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id', $document->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="year" class="form-label">ปี</label>
                                    <input type="text" class="form-control @error('year') is-invalid @enderror" id="year" name="year" value="{{ old('year', $document->year) }}">
                                    @error('year')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="document_type_id" class="form-label">ประเภทเอกสาร</label>
                                    <select class="form-select @error('document_type_id') is-invalid @enderror" id="document_type_id" name="document_type_id">
                                        <option value="">เลือกประเภทเอกสาร</option>
                                        @foreach($documentTypes as $documentType)
                                            <option value="{{ $documentType->id }}" {{ old('document_type_id', $document->document_type_id) == $documentType->id ? 'selected' : '' }}>
                                                {{ $documentType->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('document_type_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="material_id" class="form-label">วัสดุ</label>
                                    <select class="form-select @error('material_id') is-invalid @enderror" id="material_id" name="material_id">
                                        <option value="">เลือกวัสดุ</option>
                                        @foreach($materials as $material)
                                            <option value="{{ $material->id }}" {{ old('material_id', $document->material_id) == $material->id ? 'selected' : '' }}>
                                                {{ $material->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('material_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="quantity" class="form-label">จำนวน</label>
                                    <input type="text" class="form-control @error('quantity') is-invalid @enderror" id="quantity" name="quantity" value="{{ old('quantity', $document->quantity) }}">
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลเพิ่มเติม -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ข้อมูลเพิ่มเติม</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="language_id" class="form-label">ภาษา</label>
                                    <select class="form-select @error('language_id') is-invalid @enderror" id="language_id" name="language_id">
                                        <option value="">เลือกภาษา</option>
                                        @foreach($languages as $language)
                                            <option value="{{ $language->id }}" {{ old('language_id', $document->language_id) == $language->id ? 'selected' : '' }}>
                                                {{ $language->name }} {{ $language->code ? '(' . $language->code . ')' : '' }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('language_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="script_id" class="form-label">ตัวอักษร</label>
                                    <select class="form-select @error('script_id') is-invalid @enderror" id="script_id" name="script_id">
                                        <option value="">เลือกตัวอักษร</option>
                                        @foreach($scripts as $script)
                                            <option value="{{ $script->id }}" {{ old('script_id', $document->script_id) == $script->id ? 'selected' : '' }}>
                                                {{ $script->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('script_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label for="description" class="form-label">คำอธิบาย</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $document->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="brief" class="form-label">สรุปย่อ</label>
                                    <textarea class="form-control @error('brief') is-invalid @enderror" id="brief" name="brief" rows="3">{{ old('brief', $document->brief) }}</textarea>
                                    @error('brief')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="remark" class="form-label">หมายเหตุ</label>
                                    <textarea class="form-control @error('remark') is-invalid @enderror" id="remark" name="remark" rows="3">{{ old('remark', $document->remark) }}</textarea>
                                    @error('remark')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="manuscript_condition" class="form-label">สภาพเอกสาร</label>
                                    <input type="text" class="form-control @error('manuscript_condition') is-invalid @enderror" id="manuscript_condition" name="manuscript_condition" value="{{ old('manuscript_condition', $document->manuscript_condition) }}">
                                    @error('manuscript_condition')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลสถานที่ -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ข้อมูลสถานที่</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="location" class="form-label">สถานที่</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" id="location" name="location" value="{{ old('location', $document->location) }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="country" class="form-label">ประเทศ</label>
                                    <select class="form-select @error('country') is-invalid @enderror" id="country" name="country">
                                        <option value="">-- เลือกประเทศ --</option>
                                        @foreach($countries as $country)
                                            <option value="{{ $country->code }}" {{ old('country', $document->country) == $country->code ? 'selected' : '' }}>{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="latitude" class="form-label">ละติจูด</label>
                                        <input type="text" class="form-control @error('latitude') is-invalid @enderror" id="latitude" name="latitude" value="{{ old('latitude', $document->latitude) }}">
                                        @error('latitude')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="longitude" class="form-label">ลองจิจูด</label>
                                        <input type="text" class="form-control @error('longitude') is-invalid @enderror" id="longitude" name="longitude" value="{{ old('longitude', $document->longitude) }}">
                                        @error('longitude')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ไฟล์และรูปภาพ -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">ไฟล์และรูปภาพ</h6>
                            </div>
                            <div class="card-body">
                                <!-- Unified Uppy Uploader -->
                                @include('partials.unified-uppy-uploader', ['documentId' => $document->id])

                                <div class="row">
                                    <!-- Livewire Image Manager Component -->
                                    <div class="col-12 mb-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">รูปภาพเอกสาร</h6>
                                            </div>
                                            <div class="card-body">
                                                @livewire('document-image-manager', ['document' => $document])
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Livewire Document File Manager Component -->
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">ไฟล์เอกสาร</h6>
                                            </div>
                                            <div class="card-body">
                                                @livewire('document-file-manager', ['document' => $document])
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="reset" class="btn btn-secondary me-2">
                        <i class="fas fa-undo me-2"></i>รีเซ็ต
                    </button>
                    <button type="submit" class="btn btn-primary" id="submit-btn">
                        <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- jQuery (required for AJAX operations) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Uppy JS - Using CDN with all plugins included -->
<script src="https://releases.transloadit.com/uppy/v3.0.1/uppy.min.js"></script>

<!-- Custom Uppy Configuration -->
<script src="{{ asset('js/uppy-config.js') }}?v={{ time() }}"></script>

<script>
    // Initialize show upload form button
    const showUploadFormBtn = document.getElementById('show-upload-form-btn');
    if (showUploadFormBtn) {
        showUploadFormBtn.addEventListener('click', function() {
            const uploadForm = document.getElementById('additional-upload-form');
            if (uploadForm) {
                if (uploadForm.style.display === 'none') {
                    uploadForm.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-minus me-1"></i> ซ่อนฟอร์มอัพโหลด';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-secondary');
                } else {
                    uploadForm.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-plus me-1"></i> อัพโหลดไฟล์เพิ่มเติม';
                    this.classList.remove('btn-secondary');
                    this.classList.add('btn-primary');
                }
            }
        });
    }

    // Check for duplicate identifier
    document.addEventListener('DOMContentLoaded', function() {
        const identifierInput = document.getElementById('identifier_no');
        if (identifierInput) {
            const identifierFeedback = document.createElement('div');
            identifierFeedback.className = 'mt-1';
            identifierInput.parentNode.insertBefore(identifierFeedback, identifierInput.nextSibling.nextSibling);

            let checkTimeout;

            identifierInput.addEventListener('input', function() {
                const identifier = this.value.trim();
                clearTimeout(checkTimeout);

                // Clear feedback if empty
                if (!identifier) {
                    identifierFeedback.innerHTML = '';
                    return;
                }

                // Set a timeout to avoid too many requests
                checkTimeout = setTimeout(function() {
                    // Make AJAX request to check identifier
                    fetch('{{ route("admin.documents.check-identifier") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({
                            identifier: identifier,
                            document_id: {{ $document->id }}
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.exists) {
                            identifierFeedback.innerHTML = `<div class="alert alert-danger py-1 px-2 mt-1 mb-0"><i class="fas fa-exclamation-triangle me-1"></i> ${data.message}</div>`;
                            identifierInput.classList.add('is-invalid');
                        } else {
                            identifierFeedback.innerHTML = `<div class="alert alert-success py-1 px-2 mt-1 mb-0"><i class="fas fa-check-circle me-1"></i> ${data.message}</div>`;
                            identifierInput.classList.remove('is-invalid');
                        }
                    })
                    .catch(error => {
                        console.error('Error checking identifier:', error);
                    });
                }, 500); // Wait 500ms after user stops typing
            });

            // Trigger check for initial value if not empty
            if (identifierInput.value.trim()) {
                identifierInput.dispatchEvent(new Event('input'));
            }
        }
    });
</script>
@endsection
