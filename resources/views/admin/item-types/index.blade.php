@extends('layouts.admin')

@section('title', 'จัดการประเภทรายการ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="fs-4 fw-bold mb-2">จัดการประเภทรายการ</h3>
                <span class="text-secondary">รายการประเภทรายการทั้งหมดในระบบ</span>
            </div>
            <div>
                <a href="{{ route('admin.item-types.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มประเภทรายการใหม่
                </a>
            </div>
        </div>

        <!-- Item Types Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="text-uppercase small fw-bold">
                            ชื่อประเภท
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            คำอธิบาย
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จำนวนรายการ
                        </th>
                        <th scope="col" class="text-uppercase small fw-bold">
                            จัดการ
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($itemTypes as $type)
                    <tr>
                        <td class="fw-medium">
                            {{ $type->name }}
                        </td>
                        <td class="text-secondary">
                            {{ Str::limit($type->description, 100) ?: '-' }}
                        </td>
                        <td class="text-secondary">
                            {{ number_format($type->items_count) }}
                        </td>
                        <td>
                            <div class="d-flex gap-3">
                                <a href="{{ route('admin.item-types.edit', $type->id) }}" class="text-primary" title="แก้ไข">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.item-types.destroy', $type->id) }}" method="POST" class="d-inline" onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบประเภทรายการนี้?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4" class="text-center text-secondary py-3">
                            ไม่พบประเภทรายการ
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-4">
            {{ $itemTypes->links() }}
        </div>
    </div>
</div>
@endsection
