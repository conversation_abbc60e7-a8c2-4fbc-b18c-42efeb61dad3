@extends('layouts.admin')

@section('title', 'แก้ไขประเภทรายการ - ระบบจัดการคลังข้อมูลดิจิทัล')

@section('content')
<div class="card shadow mb-4">
    <div class="card-body p-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="fs-4 fw-bold mb-2">แก้ไขประเภทรายการ</h3>
                <span class="text-secondary">แก้ไขข้อมูลประเภทรายการ "{{ $itemType->name }}"</span>
            </div>
            <div>
                <a href="{{ route('admin.item-types.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับไปยังรายการประเภทรายการ
                </a>
            </div>
        </div>

        <form action="{{ route('admin.item-types.update', $itemType->id) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อประเภทรายการ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $itemType->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="icon_class" class="form-label">ไอคอน</label>
                        <div class="input-group">
                            <span class="input-group-text"><i id="selected-icon" class="fas {{ $itemType->icon_class ?: 'fa-icons' }}"></i></span>
                            <input type="text" class="form-control @error('icon_class') is-invalid @enderror" id="icon_class" name="icon_class" value="{{ old('icon_class', $itemType->icon_class) }}" placeholder="เช่น fa-book, fa-scroll" readonly>
                            <button class="btn btn-outline-secondary" type="button" id="select-icon-btn">เลือกไอคอน</button>
                        </div>
                        <small class="text-muted">คลิกที่ปุ่ม "เลือกไอคอน" เพื่อเลือกไอคอนที่ต้องการ</small>
                        @error('icon_class')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Modal สำหรับเลือกไอคอน -->
                    <div class="modal fade" id="iconModal" tabindex="-1" aria-labelledby="iconModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="iconModalLabel">เลือกไอคอน</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <input type="text" class="form-control" id="icon-search" placeholder="ค้นหาไอคอน...">
                                    </div>
                                    <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-3" id="icon-grid">
                                        <!-- ไอคอนจะถูกเพิ่มที่นี่ด้วย JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">คำอธิบาย</label>
                <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="4">{{ old('description', $itemType->description) }}</textarea>
                @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="d-flex justify-content-end mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>บันทึกการเปลี่ยนแปลง
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // ไอคอนที่เกี่ยวข้องกับประเภทรายการ
        const icons = [
            'fa-book', 'fa-scroll', 'fa-file-alt', 'fa-file-pdf', 'fa-file-word', 'fa-file-image',
            'fa-file-audio', 'fa-file-video', 'fa-file-archive', 'fa-file-code', 'fa-file-excel',
            'fa-file-powerpoint', 'fa-file-medical', 'fa-file-contract', 'fa-file-signature',
            'fa-folder', 'fa-folder-open', 'fa-archive', 'fa-box', 'fa-boxes',
            'fa-book-open', 'fa-book-reader', 'fa-bookmark', 'fa-atlas', 'fa-newspaper',
            'fa-journal-whills', 'fa-map', 'fa-map-marked', 'fa-map-marked-alt', 'fa-globe',
            'fa-globe-asia', 'fa-monument', 'fa-landmark', 'fa-university', 'fa-school',
            'fa-church', 'fa-mosque', 'fa-synagogue', 'fa-place-of-worship', 'fa-torii-gate',
            'fa-vihara', 'fa-gopuram', 'fa-hamsa', 'fa-khanda', 'fa-om', 'fa-pastafarianism',
            'fa-peace', 'fa-yin-yang', 'fa-ankh', 'fa-cross', 'fa-star-of-david',
            'fa-star-and-crescent', 'fa-dharmachakra', 'fa-mandalorian', 'fa-jedi',
            'fa-journal-whills', 'fa-torah', 'fa-quran', 'fa-bible', 'fa-pray',
            'fa-praying-hands', 'fa-hands', 'fa-handshake', 'fa-heart', 'fa-heartbeat',
            'fa-history', 'fa-clock', 'fa-hourglass', 'fa-calendar', 'fa-calendar-alt',
            'fa-calendar-day', 'fa-calendar-week', 'fa-calendar-times', 'fa-calendar-check',
            'fa-calendar-minus', 'fa-calendar-plus', 'fa-bell', 'fa-bell-slash',
            'fa-language', 'fa-comment', 'fa-comments', 'fa-microphone', 'fa-headphones',
            'fa-volume-up', 'fa-music', 'fa-guitar', 'fa-drum', 'fa-drum-steelpan',
            'fa-trumpet', 'fa-piano', 'fa-photo-video', 'fa-camera', 'fa-camera-retro',
            'fa-video', 'fa-film', 'fa-tv', 'fa-broadcast-tower', 'fa-satellite-dish',
            'fa-tablet', 'fa-mobile', 'fa-laptop', 'fa-desktop', 'fa-server',
            'fa-database', 'fa-hdd', 'fa-memory', 'fa-microchip', 'fa-sim-card',
            'fa-sd-card', 'fa-compact-disc', 'fa-record-vinyl', 'fa-tape', 'fa-floppy-disk',
            'fa-save', 'fa-cloud', 'fa-cloud-download-alt', 'fa-cloud-upload-alt',
            'fa-pen', 'fa-pencil-alt', 'fa-marker', 'fa-highlighter', 'fa-paint-brush',
            'fa-palette', 'fa-fill', 'fa-fill-drip', 'fa-stamp', 'fa-icons',
            'fa-feather', 'fa-feather-alt', 'fa-pen-fancy', 'fa-pen-nib', 'fa-signature'
        ];

        // เลือก elements
        const iconGrid = document.getElementById('icon-grid');
        const iconSearch = document.getElementById('icon-search');
        const iconModal = new bootstrap.Modal(document.getElementById('iconModal'));
        const selectIconBtn = document.getElementById('select-icon-btn');
        const iconClassInput = document.getElementById('icon_class');
        const selectedIcon = document.getElementById('selected-icon');

        // สร้างกริดไอคอน
        function createIconGrid(iconsToShow) {
            iconGrid.innerHTML = '';
            iconsToShow.forEach(icon => {
                const iconDiv = document.createElement('div');
                iconDiv.className = 'col text-center';
                iconDiv.innerHTML = `
                    <div class="icon-item p-3 border rounded mb-2 cursor-pointer" data-icon="${icon}">
                        <i class="fas ${icon} fa-2x mb-2"></i>
                        <div class="small text-muted">${icon}</div>
                    </div>
                `;
                iconGrid.appendChild(iconDiv);
            });

            // เพิ่ม event listener สำหรับการคลิกที่ไอคอน
            document.querySelectorAll('.icon-item').forEach(item => {
                item.addEventListener('click', function() {
                    const icon = this.getAttribute('data-icon');
                    iconClassInput.value = icon;
                    selectedIcon.className = 'fas ' + icon;
                    iconModal.hide();
                });
            });
        }

        // ค้นหาไอคอน
        iconSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filteredIcons = icons.filter(icon =>
                icon.toLowerCase().includes(searchTerm)
            );
            createIconGrid(filteredIcons);
        });

        // แสดง modal เมื่อคลิกปุ่ม
        selectIconBtn.addEventListener('click', function() {
            createIconGrid(icons);
            iconModal.show();
        });

        // เพิ่ม CSS สำหรับ cursor pointer
        const style = document.createElement('style');
        style.textContent = `
            .cursor-pointer {
                cursor: pointer;
            }
            .icon-item:hover {
                background-color: #f8f9fa;
                transform: scale(1.05);
                transition: all 0.2s;
            }
        `;
        document.head.appendChild(style);
    });
</script>
@endsection
