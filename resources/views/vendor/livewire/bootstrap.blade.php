<div wire:key="{{ now() }}">
    @if ($paginator->hasPages())
        <div class="pagination-container">
            <!-- ส่วนแสดงข้อมูลจำนวนรายการ -->
            <div class="pagination-info mb-3 text-center">
                <div class="card bg-light border-0 shadow-sm py-2 px-3 d-inline-block">
                    <div class="d-none d-sm-block">
                        <span class="fw-medium">
                            <i class="fas fa-file-alt me-1 text-primary"></i>
                            แสดงรายการที่ {{ $paginator->firstItem() ?? 0 }} - {{ $paginator->lastItem() ?? 0 }}
                            จากทั้งหมด {{ $paginator->total() }} รายการ
                        </span>
                    </div>
                    <div class="d-sm-none">
                        <span class="fw-medium">
                            <i class="fas fa-file-alt me-1 text-primary"></i>
                            {{ $paginator->firstItem() ?? 0 }}-{{ $paginator->lastItem() ?? 0 }} จาก {{ $paginator->total() }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- ส่วนแสดงปุ่มเลขหน้า -->
            <nav aria-label="Page navigation">
                <ul class="pagination pagination-lg justify-content-center flex-wrap">
                    {{-- Previous Page Link --}}
                    @if ($paginator->onFirstPage())
                        <li class="page-item disabled" aria-disabled="true" aria-label="@lang('pagination.previous')">
                            <span class="page-link rounded-start" aria-hidden="true">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                    @else
                        <li class="page-item">
                            <button type="button"
                                dusk="previousPage{{ $paginator->getPageName() == 'page' ? '' : '.' . $paginator->getPageName() }}"
                                class="page-link rounded-start"
                                wire:click="previousPage('{{ $paginator->getPageName() }}')"
                                wire:loading.attr="disabled"
                                rel="prev"
                                aria-label="@lang('pagination.previous')">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                        </li>
                    @endif

                    {{-- Pagination Elements --}}
                    @foreach ($elements as $element)
                        {{-- "Three Dots" Separator --}}
                        @if (is_string($element))
                            <li class="page-item disabled" aria-disabled="true">
                                <span class="page-link">{{ $element }}</span>
                            </li>
                        @endif

                        {{-- Array Of Links --}}
                        @if (is_array($element))
                            @foreach ($element as $page => $url)
                                @if ($page == $paginator->currentPage())
                                    <li class="page-item active"
                                        wire:key="paginator-{{ $paginator->getPageName() }}-{{ $page }}"
                                        aria-current="page">
                                        <span class="page-link">{{ $page }}</span>
                                    </li>
                                @else
                                    <li class="page-item"
                                        wire:key="paginator-{{ $paginator->getPageName() }}-{{ $page }}">
                                        <button type="button"
                                            class="page-link"
                                            wire:click="gotoPage({{ $page }}, '{{ $paginator->getPageName() }}')">
                                            {{ $page }}
                                        </button>
                                    </li>
                                @endif
                            @endforeach
                        @endif
                    @endforeach

                    {{-- Next Page Link --}}
                    @if ($paginator->hasMorePages())
                        <li class="page-item">
                            <button type="button"
                                dusk="nextPage{{ $paginator->getPageName() == 'page' ? '' : '.' . $paginator->getPageName() }}"
                                class="page-link rounded-end"
                                wire:click="nextPage('{{ $paginator->getPageName() }}')"
                                wire:loading.attr="disabled"
                                rel="next"
                                aria-label="@lang('pagination.next')">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </li>
                    @else
                        <li class="page-item disabled" aria-disabled="true" aria-label="@lang('pagination.next')">
                            <span class="page-link rounded-end" aria-hidden="true">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    @endif
                </ul>
            </nav>
        </div>
    @endif
</div>

<style>
    .pagination-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .pagination {
        margin-bottom: 0;
    }

    .pagination .page-link {
        border-radius: 0;
        color: #333;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-color: #dee2e6;
        transition: all 0.2s ease;
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #4a6bff, #2541b8);
        border-color: #2541b8;
        color: white;
        box-shadow: 0 2px 5px rgba(37, 65, 184, 0.2);
    }

    .pagination .page-link:hover:not(.active) {
        background-color: #f8f9fa;
        color: #4a6bff;
        z-index: 1;
    }

    .pagination .page-link:focus {
        box-shadow: 0 0 0 0.2rem rgba(74, 107, 255, 0.25);
    }

    .pagination-info .card {
        border-radius: 50px;
        display: inline-block;
    }

    @media (max-width: 576px) {
        .pagination .page-link {
            padding: 0.4rem 0.8rem;
            font-size: 0.9rem;
        }
    }
</style>
