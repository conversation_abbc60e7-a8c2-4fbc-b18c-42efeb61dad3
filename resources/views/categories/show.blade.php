@extends('layouts.app')

@section('title', $category->name . ' - คลังข้อมูลดิจิทัล')

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>{{ $category->name }}</h1>
        @if ($category->description)
            <p>{{ $category->description }}</p>
        @endif
    </div>
</div>

<section class="items py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>รายการในหมวดหมู่</h2>
            <a href="{{ route('items.index') }}" class="btn btn-outline-primary">
                <i class="fas fa-th-list me-2"></i>ดูรายการทั้งหมด
            </a>
        </div>

        @if ($items->isEmpty())
            <div class="alert alert-info">ไม่พบรายการในหมวดหมู่นี้</div>
        @else
            <div class="row g-4">
                @foreach ($items as $item)
                    <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                        <div class="item-card">
                            <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none">
                                <img src="{{ get_image_url($item->first_image, 'defaults/item-default.svg') }}" alt="{{ $item->title }}" class="item-img">
                                <div class="item-info">
                                    <h3>{{ $item->title }}</h3>
                                    <p>{{ $item->year }}</p>
                                </div>
                                <div class="item-meta">
                                    @if($item->itemType)
                                        <span class="item-type">{{ $item->itemType->name }}</span>
                                    @endif
                                    <span class="item-date">{{ $item->created_at->format('d/m/Y') }}</span>
                                </div>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</section>
@endsection
