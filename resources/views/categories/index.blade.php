@extends('layouts.app')

@section('title', 'หมวดหมู่ทั้งหมด - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))

@section('description', 'ค้นพบรายการตามหมวดหมู่ที่คุณสนใจ เรียกดูข้อมูลจากคลังข้อมูลดิจิทัลที่จัดหมวดหมู่อย่างเป็นระบบ')

@section('keywords', 'หมวดหมู่, คลังข้อมูลดิจิทัล, จัดหมวดหมู่, ประเภทข้อมูล, categories, digital collection, classification')

@section('canonical', route('categories.index'))

@section('og_type', 'website')
@section('og_title', 'หมวดหมู่ทั้งหมด - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('og_description', 'ค้นพบรายการตามหมวดหมู่ที่คุณสนใจ เรียกดูข้อมูลจากคลังข้อมูลดิจิทัลที่จัดหมวดหมู่อย่างเป็นระบบ')
@section('og_url', route('categories.index'))

@section('twitter_title', 'หมวดหมู่ทั้งหมด - ' . \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล'))
@section('twitter_description', 'ค้นพบรายการตามหมวดหมู่ที่คุณสนใจ เรียกดูข้อมูลจากคลังข้อมูลดิจิทัลที่จัดหมวดหมู่อย่างเป็นระบบ')

@section('structured_data')
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "หมวดหมู่ทั้งหมด",
    "description": "ค้นพบรายการตามหมวดหมู่ที่คุณสนใจ เรียกดูข้อมูลจากคลังข้อมูลดิจิทัลที่จัดหมวดหมู่อย่างเป็นระบบ",
    "url": "{{ route('categories.index') }}",
    "isPartOf": {
        "@type": "WebSite",
        "name": "{{ \App\Models\Setting::get('site_title', 'คลังข้อมูลดิจิทัล') }}",
        "url": "{{ config('app.url') }}"
    }
}
</script>
@endsection

@section('content')
<div class="hero" style="{{ get_hero_style() }}">
    <div class="container">
        <h1>หมวดหมู่ทั้งหมด</h1>
        <p>ค้นพบรายการตามหมวดหมู่ที่คุณสนใจ</p>
    </div>
</div>

<section class="categories py-5">
    <div class="container">
        @if ($categories->isEmpty())
            <div class="alert alert-info">ไม่พบหมวดหมู่</div>
        @else
            <div class="row g-4">
                @foreach ($categories as $category)
                    <div class="col-6 col-md-3">
                        <a href="{{ route('categories.show', $category->id) }}" class="text-decoration-none">
                            <div class="category-card">
                                <div class="category-icon">
                                    <img src="{{ get_image_url($category->image_path, 'defaults/category-default.svg') }}" alt="{{ $category->name }}">
                                </div>
                                <h3>{{ $category->name }}</h3>
                                <p>{{ $category->items->count() ?? 0 }} รายการ</p>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</section>
@endsection
