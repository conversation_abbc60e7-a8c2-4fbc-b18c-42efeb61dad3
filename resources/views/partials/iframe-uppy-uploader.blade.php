<!-- Iframe Uppy Uploader -->
<div class="mb-4">
    <label class="form-label fw-bold mb-2">
        <i class="fas fa-cloud-upload-alt text-primary me-1"></i>
        @if($type == 'document')
            อัพโหลดไฟล์เอกสาร:
        @elseif($type == 'audio')
            อัพโหลดไฟล์เสียง:
        @elseif($type == 'video')
            อัพโหลดไฟล์วิดีโอ:
        @else
            อัพโหลดรูปภาพ:
        @endif
    </label>

    <!-- Uppy Iframe Container -->
    <div class="uppy-iframe-container mb-3">
        <iframe src="{{ route('admin.documents.uppy-iframe', ['document_id' => $documentId, 'type' => $type ?? 'image']) }}"
                id="uppy-iframe-{{ $type ?? 'image' }}"
                frameborder="0"
                width="100%"
                height="450px"
                style="border-radius: 8px; border: 1px solid #dee2e6;"></iframe>
    </div>

    <div class="form-text">
        <i class="fas fa-info-circle me-1"></i>
        @if($type == 'document')
            รองรับไฟล์ PDF (สูงสุด 50MB ต่อไฟล์ สามารถอัพโหลดได้หลายไฟล์)
        @elseif($type == 'audio')
            รองรับไฟล์ MP3, WAV, OGG (สูงสุด 20MB)
        @elseif($type == 'video')
            รองรับไฟล์ MP4, WEBM, MOV (สูงสุด 50MB)
        @else
            รองรับไฟล์ JPG, PNG, GIF (สูงสุด 5MB ต่อไฟล์)
        @endif
    </div>
</div>

<!-- Uploaded Files Container -->
<div id="uploaded-files-container-{{ $type ?? 'image' }}" class="uploaded-files-container mt-4" style="display: none;">
    <h6 class="mb-3">ไฟล์ที่อัพโหลดล่าสุด</h6>
    <div id="uploaded-files-list-{{ $type ?? 'image' }}" class="uploaded-files-list"></div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Helper function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Helper function to get file icon based on file type
        function getFileIcon(fileType) {
            if (!fileType) return 'alt';

            if (fileType === 'document' || fileType.includes('pdf')) {
                return 'pdf';
            } else if (fileType === 'audio' || fileType.includes('audio')) {
                return 'audio';
            } else if (fileType === 'video' || fileType.includes('video')) {
                return 'video';
            } else if (fileType === 'image' || fileType.includes('image')) {
                return 'image';
            } else {
                return 'alt';
            }
        }

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            // Check if the message is from our iframe
            if (event.data && event.data.type === 'uppy-upload-success') {
                console.log('Received upload success message from iframe:', event.data);

                // Handle the uploaded file data
                const fileData = event.data.fileData;
                const filesData = event.data.filesData || [];
                const uploadType = event.data.uploadType;

                if (filesData.length > 0) {
                    // Show the uploaded files container
                    const filesContainer = document.getElementById('uploaded-files-container-' + uploadType);
                    const filesList = document.getElementById('uploaded-files-list-' + uploadType);

                    if (filesContainer && filesList) {
                        // Clear the list
                        filesList.innerHTML = '';

                        // Create file cards for each uploaded file
                        filesData.forEach(file => {
                            // Create file card
                            const fileCard = document.createElement('div');
                            fileCard.className = 'file-card';

                            // Get file details
                            const fileName = file.name || file.file_name || 'ไฟล์';
                            const fileSize = formatFileSize(file.size || 0);
                            const fileType = file.type || file.file_type || '';
                            const fileIcon = getFileIcon(fileType);

                            // Set file card content
                            fileCard.innerHTML = `
                                <div class="file-icon-container bg-primary">
                                    <i class="fas fa-file-${fileIcon} fa-3x"></i>
                                </div>
                                <div class="file-info">
                                    <h6 class="file-name" title="${fileName}">${fileName}</h6>
                                    <div class="file-meta">
                                        <span class="badge bg-primary text-white">
                                            <i class="fas fa-file-${fileIcon} me-1"></i> อัพโหลดใหม่
                                        </span>
                                        <span class="file-size">${fileSize}</span>
                                    </div>
                                </div>
                            `;

                            // Add file card to the list
                            filesList.appendChild(fileCard);
                        });

                        filesContainer.style.display = 'block';
                    }
                }

                // Reload the page after a delay to show all uploaded files
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            }
        });
    });
</script>
