<div>
    <!-- Loading Indicator -->
    <div wire:loading.delay class="wire-loading-indicator">
        <div class="spinner-border text-primary mb-2" role="status">
            <span class="visually-hidden">กำลังโหลด...</span>
        </div>
        <div>กำลังโหลดข้อมูล...</div>
    </div>

    <div class="row" wire:loading.class="opacity-50">
        <div class="col-lg-9 item-main">
            <div class="card mb-4">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-6 mb-2 mb-md-0">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary rounded-pill me-2">{{ $totalResults }}</span>
                                <span class="text-muted">รายการที่พบ</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center justify-content-md-end">
                                <div class="d-flex align-items-center me-3">
                                    <label for="per-page" class="form-label me-2 mb-0">แสดง:</label>
                                    <select id="per-page" wire:model.live="perPage" class="form-select form-select-sm" style="width: 70px;">
                                        <option value="12">12</option>
                                        <option value="24">24</option>
                                        <option value="48">48</option>
                                        <option value="96">96</option>
                                    </select>
                                </div>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm {{ $viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary' }}" wire:click="setViewMode('grid')">
                                        <i class="fas fa-th-large"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm {{ $viewMode === 'table' ? 'btn-primary' : 'btn-outline-primary' }}" wire:click="setViewMode('table')">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grid View -->
            @if($viewMode === 'grid')
                <div class="item-grid">
                    @if ($items->isEmpty())
                        <div class="alert alert-info">ไม่พบรายการ</div>
                    @else
                        <div class="row g-4">
                            @foreach ($items as $item)
                                <div class="col-12 col-sm-6 col-md-4">
                                    <div class="item-card">
                                        <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none">
                                            <img src="{{ get_image_url($item->first_image, 'defaults/item-default.svg') }}" alt="{{ $item->title }}" class="item-img">
                                            <div class="item-info">
                                                <h3>{{ $item->title }}</h3>
                                                @if($item->year)
                                                    <p>{{ $item->year }}</p>
                                                @endif
                                            </div>
                                            <div class="item-meta">
                                                @if($item->category)
                                                    <span class="badge bg-success"><i class="fas fa-folder me-1"></i> {{ $item->category->name }}</span>
                                                @endif
                                                @if($item->language)
                                                    <span class="badge bg-warning"><i class="fas fa-language me-1"></i> {{ $item->language->name }}</span>
                                                @endif
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endif

            <!-- Table View -->
            @if($viewMode === 'table')
                <div class="card item-table">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 80px;">รูปภาพ</th>
                                        <th>ชื่อรายการ</th>
                                        <th style="width: 120px;">หมวดหมู่</th>
                                        <th style="width: 120px;">ภาษา</th>
                                        <th style="width: 80px;">ปี</th>
                                        <th style="width: 120px;">วัสดุ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if ($items->isEmpty())
                                        <tr>
                                            <td colspan="6" class="text-center py-4">ไม่พบรายการ</td>
                                        </tr>
                                    @else
                                        @foreach ($items as $item)
                                            <tr>
                                                <td class="text-center">
                                                    <a href="{{ route('items.show', $item->id) }}">
                                                        <img src="{{ get_image_url($item->first_image, 'defaults/item-default.svg') }}" alt="{{ $item->title }}" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                    </a>
                                                </td>
                                                <td>
                                                    <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none fw-medium">
                                                        {{ $item->title }}
                                                    </a>
                                                </td>
                                                <td>
                                                    @if($item->category)
                                                        <span class="badge bg-success"><i class="fas fa-folder me-1"></i> {{ $item->category->name }}</span>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($item->language)
                                                        <span class="badge bg-warning"><i class="fas fa-language me-1"></i> {{ $item->language->name }}</span>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                                <td>{{ $item->year ?? '-' }}</td>
                                                <td>{{ $item->material ? $item->material->name : '-' }}</td>
                                            </tr>
                                        @endforeach
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <div class="pagination-wrapper">
                {{ $items->links() }}
            </div>
        </div>

        <div class="col-lg-3 filter-sidebar">
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">ค้นหา</h5>
                        <button type="button" class="btn btn-sm btn-outline-secondary" wire:click="resetFilters">
                            <i class="fas fa-redo-alt me-1"></i>รีเซ็ต
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="search" class="form-label">คำค้นหา</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" wire:model.live.debounce.300ms="searchTerm" placeholder="ค้นหารายการ...">
                            <button class="btn btn-outline-secondary" type="button" wire:click="$set('searchTerm', '')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="search-type" class="form-label">ค้นหาใน</label>
                        <select id="search-type" wire:model.live="searchType" class="form-select">
                            <option value="all">ทั้งหมด</option>
                            <option value="title">ชื่อรายการ</option>
                            <option value="description">คำอธิบาย</option>
                            <option value="identifier">รหัสรายการ</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">หมวดหมู่</label>
                        <div class="filter-scroll">
                            @foreach($categories as $category)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="category-{{ $category->id }}"
                                        wire:model.live="categoryIds" value="{{ $category->id }}">
                                    <label class="form-check-label" for="category-{{ $category->id }}">
                                        {{ $category->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ประเภทรายการ</label>
                        <div class="filter-scroll">
                            @foreach($itemTypes as $type)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="type-{{ $type->id }}"
                                        wire:model.live="itemTypeIds" value="{{ $type->id }}">
                                    <label class="form-check-label" for="type-{{ $type->id }}">
                                        {{ $type->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">วัสดุ</label>
                        <div class="filter-scroll">
                            @foreach($materials as $material)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="material-{{ $material->id }}"
                                        wire:model.live="materialIds" value="{{ $material->id }}">
                                    <label class="form-check-label" for="material-{{ $material->id }}">
                                        {{ $material->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ภาษา</label>
                        <div class="filter-scroll">
                            @foreach($languages as $language)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="language-{{ $language->id }}"
                                        wire:model.live="languageIds" value="{{ $language->id }}">
                                    <label class="form-check-label" for="language-{{ $language->id }}">
                                        {{ $language->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ตัวอักษร</label>
                        <div class="filter-scroll">
                            @foreach($scripts as $script)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="script-{{ $script->id }}"
                                        wire:model.live="scriptIds" value="{{ $script->id }}">
                                    <label class="form-check-label" for="script-{{ $script->id }}">
                                        {{ $script->name }}
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ช่วงปี</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="number" class="form-control" placeholder="จาก"
                                    wire:model.live.debounce.500ms="yearFrom">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" placeholder="ถึง"
                                    wire:model.live.debounce.500ms="yearTo">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">เรียงตาม</label>
                        <select class="form-select" wire:model.live="sortBy">
                            <option value="newest">ล่าสุด</option>
                            <option value="oldest">เก่าสุด</option>
                            <option value="a-z">ชื่อ A-Z</option>
                            <option value="z-a">ชื่อ Z-A</option>
                            <option value="year-asc">ปี (น้อยไปมาก)</option>
                            <option value="year-desc">ปี (มากไปน้อย)</option>
                            <option value="views">ยอดเข้าชม</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
