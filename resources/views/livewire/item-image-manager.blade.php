<div class="item-image-manager">
    <style>
        /* Card styling */
        .sortable-image .card {
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.125);
            border-radius: 8px;
            overflow: hidden;
        }
        .sortable-image .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
            border-color: rgba(13, 110, 253, 0.3);
        }

        /* Image container styling */
        .image-container {
            position: relative;
            overflow: hidden;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            background-color: #f8f9fa;
        }
        .image-container img {
            transition: transform 0.3s ease;
        }
        .sortable-image .card:hover .image-container img {
            transform: scale(1.05);
        }

        /* Card body styling */
        .sortable-image .card-body {
            background-color: #ffffff;
        }
        .sortable-image .card-title {
            font-weight: 600;
            color: #333;
        }

        /* Badge styling */
        .sortable-image .badge {
            font-weight: normal;
            padding: 0.4em 0.6em;
        }
        .sortable-image .badge.bg-light {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
        }
        /* Main image badge styling */
        .sortable-image .badge.bg-primary {
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 10;
        }

        /* Button styling */
        .sortable-image .btn-group {
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .sortable-image .btn-outline-primary:hover {
            box-shadow: 0 0 0 0.15rem rgba(13, 110, 253, 0.25);
        }
        .sortable-image .btn-outline-danger:hover {
            box-shadow: 0 0 0 0.15rem rgba(220, 53, 69, 0.25);
        }

        /* Sortable styling */
        .sortable-ghost {
            opacity: 0.5;
            background: #f8f9fa;
        }
        .handle {
            cursor: grab;
        }
        .handle:active {
            cursor: grabbing;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .sortable-image .card-body {
                padding: 0.75rem !important;
            }
            .sortable-image .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
        }

        /* Basic Uppy customizations - most styles come from npm packages */
        .uppy-ImageEditor-container {
            background-color: rgba(0,0,0,0.8) !important;
        }
    </style>
    <!-- Success and Error Messages -->
    @if($successMessage)
        <div class="alert alert-success alert-dismissible fade show" role="alert" id="success-alert">
            {{ $successMessage }}
            <button type="button" class="btn-close" onclick="document.getElementById('success-alert').style.display='none'; @this.set('successMessage', '');"></button>
        </div>
    @endif

    @if($errorMessage)
        <div class="alert alert-danger alert-dismissible fade show" role="alert" id="error-alert">
            {{ $errorMessage }}
            <button type="button" class="btn-close" onclick="document.getElementById('error-alert').style.display='none'; @this.set('errorMessage', '');"></button>
        </div>
    @endif

    <!-- Image Upload Section -->
    <div class="card mb-4">
        <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
            <h5 class="mb-0">
                <i class="fas fa-images text-primary me-2"></i> จัดการรูปภาพ
            </h5>
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle text-info me-2"></i>
                <span class="text-muted small">รองรับไฟล์ JPG, PNG, GIF (สูงสุด 5MB ต่อไฟล์)</span>
            </div>
        </div>
        <div class="card-body">
            <!-- Note: Uppy uploader is now unified and moved outside this component -->

            <!-- Image Preview and Management -->
            @if(count($images) > 0)
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-photo-video text-success me-1"></i> รูปภาพที่อัพโหลดแล้ว ({{ count($images) }} รูป):
                        </h6>
                        <span class="badge bg-primary rounded-pill">{{ count($images) }} รูป</span>
                    </div>
                    <div class="alert alert-light border mb-3">
                        <div class="d-flex">
                            <div class="me-2">
                                <i class="fas fa-lightbulb text-warning"></i>
                            </div>
                            <div>
                                <strong>คำแนะนำ:</strong> คุณสามารถลากเพื่อจัดเรียงรูปภาพ และคลิกที่ <i class="fas fa-star text-primary"></i> เพื่อตั้งเป็นรูปหลัก
                            </div>
                        </div>
                    </div>
                    <div class="row image-gallery" id="sortable-images">
                        @foreach($images as $index => $image)
                            @php
                                // Handle both DB images and temporary images
                                $isTemp = is_array($image) || (is_object($image) && !($image instanceof \App\Models\ItemImage));

                                if ($isTemp) {
                                    // Temporary image (array format)
                                    $imageId = $image['id'] ?? 'temp_' . uniqid();
                                    $filename = $image['image_name'] ?? '';
                                    $isMain = $image['is_main'] ?? false;
                                    $createdAt = $image['created_at'] ?? now()->toDateTimeString();
                                    $imageUrl = $image['url'] ?? '';
                                    $displayFilename = strlen($filename) > 25 ? substr($filename, 0, 22) . '...' : $filename;
                                } else {
                                    // Database image (ItemImage model)
                                    $imageId = $image->id;

                                    // Standardize image path handling
                                    $imagePath = $image->image_path;

                                    // Extract the actual path without /storage/ prefix
                                    $pathWithoutStorage = preg_replace('#^/?(storage/)?#', '', $imagePath);

                                    // Ensure the path starts with 'images/'
                                    if (!str_starts_with($pathWithoutStorage, 'images/')) {
                                        $pathWithoutStorage = 'images/' . $pathWithoutStorage;
                                    }

                                    // Create the asset URL
                                    $imageUrl = asset('storage/' . $pathWithoutStorage);

                                    // Extract filename from path
                                    $filename = basename($pathWithoutStorage);

                                    // Truncate filename if too long
                                    $displayFilename = strlen($filename) > 25 ? substr($filename, 0, 22) . '...' : $filename;

                                    $isMain = $image->is_main;
                                    $createdAt = $image->created_at;

                                    // Debug information
                                    $debugPath = 'storage/' . $pathWithoutStorage;
                                    $fullPath = public_path($debugPath);
                                    $exists = file_exists($fullPath) ? 'exists' : 'missing';
                                    $size = file_exists($fullPath) ? filesize($fullPath) . ' bytes' : 'N/A';

                                    // If the file doesn't exist in public storage but exists in app storage, copy it
                                    if (!file_exists($fullPath)) {
                                        $correctPath = storage_path('app/public/' . $pathWithoutStorage);
                                        $incorrectPath = storage_path('app/public/public/' . $pathWithoutStorage);

                                        // Try to copy from the correct storage path
                                        if (file_exists($correctPath)) {
                                            $storageDir = dirname($pathWithoutStorage);
                                            if (!file_exists(public_path('storage/' . $storageDir))) {
                                                mkdir(public_path('storage/' . $storageDir), 0755, true);
                                            }
                                            copy($correctPath, $fullPath);
                                            $exists = 'copied from correct path';
                                            $size = filesize($fullPath) . ' bytes';
                                        }
                                        // If not in correct path, try the incorrect path
                                        else if (file_exists($incorrectPath)) {
                                            // First copy to correct storage location
                                            $storageDir = dirname($pathWithoutStorage);
                                            $correctStorageDir = storage_path('app/public/' . $storageDir);
                                            if (!file_exists($correctStorageDir)) {
                                                mkdir($correctStorageDir, 0755, true);
                                            }

                                            // Copy to correct storage path
                                            copy($incorrectPath, $correctPath);

                                            // Then copy to public path
                                            if (!file_exists(public_path('storage/' . $storageDir))) {
                                                mkdir(public_path('storage/' . $storageDir), 0755, true);
                                            }
                                            copy($correctPath, $fullPath);
                                            $exists = 'copied from incorrect path';
                                            $size = filesize($fullPath) . ' bytes';
                                        }
                                    }
                                }
                            @endphp
                            <div class="col-md-2 col-sm-4 mb-3 sortable-image" data-id="{{ $imageId }}">
                                <div class="card h-100 shadow-sm">
                                    <div class="position-relative">
                                        @if($isMain)
                                            <span class="badge bg-primary position-absolute top-0 end-0 m-2 px-2 py-1 small">หลัก</span>
                                        @endif
                                        <div class="image-container" style="height: 150px; overflow: hidden;">
                                            <!-- Image number badge -->
                                            <div class="position-absolute top-0 start-0 m-2 image-number">
                                                <span class="badge rounded-pill bg-dark bg-opacity-50">
                                                    {{ $index + 1 }}/{{ count($images) }}
                                                </span>
                                            </div>

                                            <img src="{{ $imageUrl }}"
                                                class="card-img-top" alt="{{ $filename }}"
                                                style="height: 100%; width: 100%; object-fit: cover;">
                                        </div>
                                        <!-- Debug info removed -->
                                    </div>
                                    <div class="card-body p-2">
                                        <!-- Filename with icon -->
                                        <h6 class="card-title text-truncate mb-1 small" title="{{ $filename }}">
                                            <i class="fas fa-image text-primary me-1"></i> {{ $displayFilename }}
                                        </h6>

                                        <!-- File size removed -->

                                        <!-- Action buttons -->
                                        <div class="d-flex justify-content-center mt-2">
                                            <div class="btn-group btn-group-sm">
                                                @if(!$isMain && !$isTemp)
                                                    <button type="button" class="btn btn-sm btn-outline-primary set-main-btn"
                                                            data-image-id="{{ $imageId }}"
                                                            title="ตั้งเป็นรูปหลัก">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-outline-success" disabled
                                                            title="รูปหลัก">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                @endif
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-image-btn"
                                                        data-image-id="{{ $imageId }}"
                                                        title="ลบรูปภาพ">
                                                        <i class="fas fa-trash"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary handle" title="จัดเรียง">
                                                    <i class="fas fa-arrows-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="alert alert-info border-info">
                    <div class="d-flex align-items-center">
                        <div class="me-3 fs-3">
                            <i class="fas fa-info-circle text-info"></i>
                        </div>
                        <div>
                            <h6 class="alert-heading mb-1">ยังไม่มีรูปภาพ</h6>
                            <p class="mb-0">ยังไม่มีรูปภาพที่อัพโหลด กรุณาอัพโหลดรูปภาพโดยใช้เครื่องมือด้านบน</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- JavaScript for Sortable and iframe communication -->
    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', function () {
            initSortable();
            setupIframeListeners();
            setupUploadButton();
            setupDeleteButtons();
            setupSetMainButtons();
        });

        function initSortable() {
            const sortableImages = document.getElementById('sortable-images');
            if (sortableImages) {
                new Sortable(sortableImages, {
                    animation: 150,
                    handle: '.handle',
                    ghostClass: 'sortable-ghost',
                    onEnd: function(evt) {
                        const itemIds = Array.from(sortableImages.querySelectorAll('.sortable-image'))
                            .map(item => parseInt(item.dataset.id));

                        @this.updateImageOrder(itemIds);
                    }
                });
            }
        }

        function setupIframeListeners() {
            // Listen for messages from the iframe
            window.addEventListener('message', function(event) {
                console.log('Received message from iframe:', event.data);

                if (event.data && event.data.type === 'uppy:upload-success') {
                    // Handle successful upload
                    const fileData = event.data.fileData;
                    console.log('Upload success from iframe:', fileData);

                    // Call the Livewire method to handle the upload
                    @this.handleUppyUpload(fileData);

                    // Show success notification
                    Swal.fire({
                        title: 'อัพโหลดสำเร็จ!',
                        text: 'รูปภาพถูกอัพโหลดเรียบร้อยแล้ว',
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    });
                }

                if (event.data && event.data.type === 'uppy:upload-error') {
                    // Handle upload error
                    const errorMessage = event.data.error;
                    console.error('Upload error from iframe:', errorMessage);

                    // Show error notification
                    Swal.fire({
                        title: 'อัพโหลดไม่สำเร็จ!',
                        text: 'เกิดข้อผิดพลาดในการอัพโหลดรูปภาพ: ' + errorMessage,
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                }
            });
        }

        function setupUploadButton() {
            // Upload button is now handled by the unified uploader
            console.log('Upload button is now handled by the unified uploader');
        }

        // Setup set main image buttons with SweetAlert confirmation
        function setupSetMainButtons() {
            document.querySelectorAll('.set-main-btn').forEach(button => {
                // Remove existing event listeners by cloning and replacing
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // Add new event listener
                newButton.addEventListener('click', function() {
                    const imageId = this.getAttribute('data-image-id');

                    Swal.fire({
                        title: 'ยืนยันการตั้งเป็นรูปหลัก',
                        text: 'คุณต้องการตั้งรูปนี้เป็นรูปหลักใช่หรือไม่?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#4a6bff',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'ใช่, ตั้งเป็นรูปหลัก',
                        cancelButtonText: 'ยกเลิก'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Call Livewire method to set main image
                            @this.setMainImage(imageId);

                            // Show success message without close button
                            Swal.fire({
                                title: 'สำเร็จ!',
                                text: 'ตั้งเป็นรูปหลักเรียบร้อยแล้ว',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false,
                                showCloseButton: false
                            });
                        }
                    });
                });
            });
        }

        // Setup delete buttons with SweetAlert confirmation
        function setupDeleteButtons() {
            document.querySelectorAll('.delete-image-btn').forEach(button => {
                // Remove existing event listeners by cloning and replacing
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // Add new event listener
                newButton.addEventListener('click', function() {
                    const imageId = this.getAttribute('data-image-id');

                    Swal.fire({
                        title: 'ยืนยันการลบ',
                        text: 'คุณแน่ใจหรือไม่ว่าต้องการลบรูปภาพนี้?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'ใช่, ลบเลย',
                        cancelButtonText: 'ยกเลิก'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Call Livewire method to delete the image
                            @this.deleteImage(imageId);

                            // Show success message without close button
                            Swal.fire({
                                title: 'ลบเรียบร้อย!',
                                text: 'รูปภาพถูกลบเรียบร้อยแล้ว',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false,
                                showCloseButton: false
                            });
                        }
                    });
                });
            });
        }

        // Re-initialize sortable when Livewire updates
        document.addEventListener('livewire:update', function() {
            setTimeout(() => {
                // Reinitialize sortable and buttons
                initSortable();
                setupDeleteButtons();
                setupSetMainButtons();
                console.log('Livewire updated, reinitialized components');
            }, 100);
        });

        // Add a listener for when images are updated
        Livewire.on('imagesUpdated', () => {
            console.log('Images updated, reinitializing components');
            setTimeout(() => {
                initSortable();
                setupDeleteButtons();
                setupSetMainButtons();
            }, 100);
        });

        // Add a special listener for successful uploads
        Livewire.on('imageUploadSuccess', () => {
            console.log('Image upload success event received, refreshing components');
            setTimeout(() => {
                initSortable();
                setupDeleteButtons();
                setupSetMainButtons();
            }, 100);
        });
    </script>
    @endpush
</div>
