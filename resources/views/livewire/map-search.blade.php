<div>
    <div class="card shadow-sm mb-3">
        <div class="card-body">
            <h5 class="card-title mb-3">ค้นหารายการบนแผนที่</h5>

            <div class="mb-3">
                <label for="searchTerm" class="form-label">คำค้นหา</label>
                <div class="input-group">
                    <input type="text" id="searchTerm" class="form-control" placeholder="ชื่อรายการ, สถานที่...">
                    <button class="btn btn-primary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">หมวดหมู่</label>
                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="category_all" checked>
                        <label class="form-check-label fw-semibold" for="category_all">
                            ทั้งหมด
                        </label>
                    </div>
                    <hr class="my-2">
                    @foreach($categories as $category)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="{{ $category->id }}"
                                id="category_{{ $category->id }}">
                            <label class="form-check-label" for="category_{{ $category->id }}">
                                {{ $category->name }}
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">ประเภทรายการ</label>
                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="doctype_all" checked>
                        <label class="form-check-label fw-semibold" for="doctype_all">
                            ทั้งหมด
                        </label>
                    </div>
                    <hr class="my-2">
                    @foreach($itemTypes as $type)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="{{ $type->id }}"
                                id="type_{{ $type->id }}">
                            <label class="form-check-label" for="type_{{ $type->id }}">
                                {{ $type->name }}
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">ประเทศ</label>
                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="country_all" checked>
                        <label class="form-check-label fw-semibold" for="country_all">
                            ทั้งหมด
                        </label>
                    </div>
                    <hr class="my-2">
                    @if(isset($countries) && count($countries) > 0)
                        @foreach($countries as $country)
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                    value="{{ $country->code }}"
                                    id="country_{{ $country->code }}">
                                <label class="form-check-label" for="country_{{ $country->code }}">
                                    {{ $country->name }}
                                </label>
                            </div>
                        @endforeach
                    @else
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="TH"
                                id="country_TH">
                            <label class="form-check-label" for="country_TH">
                                ประเทศไทย
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="MM"
                                id="country_MM">
                            <label class="form-check-label" for="country_MM">
                                ประเทศเมียนมาร์
                            </label>
                        </div>
                    @endif
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">จังหวัด</label>
                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="province_all" checked>
                        <label class="form-check-label fw-semibold" for="province_all">
                            ทั้งหมด
                        </label>
                    </div>
                    <hr class="my-2">
                    @foreach($provinces as $province)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="{{ $province->code }}"
                                id="province_{{ $province->code }}">
                            <label class="form-check-label" for="province_{{ $province->code }}">
                                {{ $province->name_th }}
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">ภาษา</label>
                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="language_all" checked>
                        <label class="form-check-label fw-semibold" for="language_all">
                            ทั้งหมด
                        </label>
                    </div>
                    <hr class="my-2">
                    @foreach($languages as $language)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="{{ $language->id }}"
                                id="language_{{ $language->id }}">
                            <label class="form-check-label" for="language_{{ $language->id }}">
                                {{ $language->name }}
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">อักษร</label>
                <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="script_all" checked>
                        <label class="form-check-label fw-semibold" for="script_all">
                            ทั้งหมด
                        </label>
                    </div>
                    <hr class="my-2">
                    @foreach($scripts as $script)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                value="{{ $script->id }}"
                                id="script_{{ $script->id }}">
                            <label class="form-check-label" for="script_{{ $script->id }}">
                                {{ $script->name }}
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="badge bg-primary" id="filteredCount">0</span> จาก <span class="badge bg-secondary" id="totalCount">0</span> รายการ
                </div>
                <button class="btn btn-outline-secondary" id="resetFiltersBtn">
                    <i class="fas fa-undo me-1"></i>ล้างตัวกรอง
                </button>
            </div>
        </div>
    </div>

    <div class="item-list">
        <!-- รายการจะถูกสร้างโดย JavaScript -->
    </div>
</div>
