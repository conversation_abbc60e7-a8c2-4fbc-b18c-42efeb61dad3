<div wire:poll.10s>
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-4">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-3 mb-md-0">
                    <h5 class="card-title mb-2">จัดการบทบาท</h5>
                    <p class="card-subtitle text-muted mb-0">กำหนดบทบาทและสิทธิ์การใช้งานในระบบ</p>
                </div>
                <div>
                    <button wire:click="create" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i>เพิ่มบทบาทใหม่
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Search -->
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" wire:model.live.debounce.300ms="searchTerm" class="form-control py-2" placeholder="ค้นหาบทบาท...">
                </div>
            </div>

            <!-- Roles Table -->
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                ชื่อบทบาท
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                สิทธิ์การใช้งาน
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                จำนวนผู้ใช้งาน
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                จัดการ
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($roles as $role)
                        <tr>
                            <td class="py-3">
                                <div class="fw-medium">
                                    {{ $role->name }}
                                </div>
                            </td>
                            <td class="py-3">
                                <div class="d-flex flex-wrap gap-1">
                                    @foreach($role->permissions->take(5) as $permission)
                                        <span class="badge bg-info rounded-pill px-2 py-1">
                                            {{ $permission->name }}
                                        </span>
                                    @endforeach
                                    @if($role->permissions->count() > 5)
                                        <span class="badge bg-secondary rounded-pill px-2 py-1">
                                            +{{ $role->permissions->count() - 5 }} อื่นๆ
                                        </span>
                                    @endif
                                </div>
                            </td>
                            <td class="py-3">
                                <span class="badge bg-secondary rounded-pill px-3 py-2">
                                    {{ $role->users->count() }} คน
                                </span>
                            </td>
                            <td class="py-3">
                                <div class="d-flex gap-3">
                                    <button wire:click="edit({{ $role->id }})" class="text-primary border-0 bg-transparent p-0" title="แก้ไข">
                                        <i class="fas fa-edit fa-lg"></i>
                                    </button>
                                    @if($role->name !== 'admin' && $role->name !== 'editor' && $role->name !== 'user')
                                        <button wire:click="confirmDelete({{ $role->id }})" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                            <i class="fas fa-trash fa-lg"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="text-center text-secondary py-4">
                                <i class="fas fa-user-tag fa-2x mb-3 text-muted"></i>
                                <p>ไม่พบบทบาท</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4" wire:key="roles-pagination">
                {{ $roles->links() }}
            </div>
        </div>
    </div>

    <!-- Create Role Modal -->
    @if($isCreating)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มบทบาทใหม่</h5>
                    <button type="button" class="btn-close" wire:click="cancel"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row g-3">
                            <!-- ชื่อบทบาท -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อบทบาท <span class="text-danger">*</span></label>
                                    <input type="text" wire:model.live="name" id="name" class="form-control @error('name') is-invalid @enderror">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- สิทธิ์การใช้งาน -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">สิทธิ์การใช้งาน <span class="text-danger">*</span></label>
                                    @error('selectedPermissions')
                                        <div class="text-danger small mb-2">{{ $message }}</div>
                                    @enderror

                                    <div class="row">
                                        @foreach($allPermissions->groupBy(function($permission) {
                                            return explode(' ', $permission->name)[0];
                                        }) as $group => $permissions)
                                            <div class="col-md-6 mb-3">
                                                <div class="card">
                                                    <div class="card-header bg-light py-2">
                                                        <strong>{{ ucfirst($group) }}</strong>
                                                    </div>
                                                    <div class="card-body">
                                                        @foreach($permissions as $permission)
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox"
                                                                    wire:model.live="selectedPermissions"
                                                                    value="{{ $permission->name }}"
                                                                    id="perm_{{ $permission->id }}">
                                                                <label class="form-check-label" for="perm_{{ $permission->id }}">
                                                                    {{ $permission->name }}
                                                                </label>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="cancel">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" wire:click="store">บันทึก</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Edit Role Modal -->
    @if($isEditing)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">แก้ไขบทบาท</h5>
                    <button type="button" class="btn-close" wire:click="cancel"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row g-3">
                            <!-- ชื่อบทบาท -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อบทบาท <span class="text-danger">*</span></label>
                                    <input type="text" wire:model.live="name" id="name" class="form-control @error('name') is-invalid @enderror"
                                        @if(in_array($name, ['admin', 'editor', 'user'])) readonly @endif>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if(in_array($name, ['admin', 'editor', 'user']))
                                        <div class="form-text text-muted">บทบาทพื้นฐานไม่สามารถเปลี่ยนชื่อได้</div>
                                    @endif
                                </div>
                            </div>

                            <!-- สิทธิ์การใช้งาน -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">สิทธิ์การใช้งาน <span class="text-danger">*</span></label>
                                    @error('selectedPermissions')
                                        <div class="text-danger small mb-2">{{ $message }}</div>
                                    @enderror

                                    <div class="row">
                                        @foreach($allPermissions->groupBy(function($permission) {
                                            return explode(' ', $permission->name)[0];
                                        }) as $group => $permissions)
                                            <div class="col-md-6 mb-3">
                                                <div class="card">
                                                    <div class="card-header bg-light py-2">
                                                        <strong>{{ ucfirst($group) }}</strong>
                                                    </div>
                                                    <div class="card-body">
                                                        @foreach($permissions as $permission)
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox"
                                                                    wire:model.live="selectedPermissions"
                                                                    value="{{ $permission->name }}"
                                                                    id="perm_{{ $permission->id }}">
                                                                <label class="form-check-label" for="perm_{{ $permission->id }}">
                                                                    {{ $permission->name }}
                                                                </label>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="cancel">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" wire:click="update">บันทึกการเปลี่ยนแปลง</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Delete Confirmation Modal -->
    @if($confirmingRoleDeletion)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบบทบาท</h5>
                    <button type="button" class="btn-close" wire:click="$set('confirmingRoleDeletion', false)"></button>
                </div>
                <div class="modal-body">
                    <p>คุณแน่ใจหรือไม่ที่จะลบบทบาทนี้? การกระทำนี้ไม่สามารถย้อนกลับได้</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="$set('confirmingRoleDeletion', false)">ยกเลิก</button>
                    <button type="button" class="btn btn-danger" wire:click="delete">ลบบทบาท</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- SweetAlert Scripts -->
    <script>
        document.addEventListener('livewire:initialized', () => {
            @this.on('role-saved', (event) => {
                const action = event.action === 'created' ? 'เพิ่ม' : 'แก้ไข';
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: `${action}บทบาทเรียบร้อยแล้ว`,
                    icon: 'success',
                    confirmButtonText: 'ตกลง'
                });
            });

            @this.on('role-deleted', () => {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: 'ลบบทบาทเรียบร้อยแล้ว',
                    icon: 'success',
                    confirmButtonText: 'ตกลง'
                });
            });

            @this.on('role-error', (event) => {
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: event.message,
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            });
        });
    </script>
</div>
