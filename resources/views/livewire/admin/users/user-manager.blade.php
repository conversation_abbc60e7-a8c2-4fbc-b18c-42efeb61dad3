<div wire:poll.10s>
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-4">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div class="mb-3 mb-md-0">
                    <h5 class="card-title mb-2">จัดการผู้ใช้งาน</h5>
                    <p class="card-subtitle text-muted mb-0">รายการผู้ใช้งานทั้งหมดในระบบ</p>
                </div>
                <div>
                    <button wire:click="create" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>เพิ่มผู้ใช้งานใหม่
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Search -->
            <div class="mb-4">
                <div class="input-group">
                    <span class="input-group-text bg-light">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" wire:model.live.debounce.300ms="searchTerm" class="form-control py-2" placeholder="ค้นหาผู้ใช้งาน...">
                </div>
            </div>

            <!-- Users Table -->
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                ชื่อผู้ใช้งาน
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                อีเมล
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                บทบาท
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                สถานะ
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                วันที่สร้าง
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold py-3">
                                จัดการ
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td class="py-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <img class="rounded-circle" src="{{ $user->getProfileImageUrl() }}" alt="{{ $user->name }}" width="48" height="48">
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-medium">
                                            {{ $user->name }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 text-secondary">
                                {{ $user->email }}
                            </td>
                            <td class="py-3">
                                @if($user->hasRole('admin'))
                                    <span class="badge bg-danger rounded-pill px-3 py-2">
                                        ผู้ดูแลระบบ
                                    </span>
                                @elseif($user->hasRole('editor'))
                                    <span class="badge bg-primary rounded-pill px-3 py-2">
                                        ผู้แก้ไข
                                    </span>
                                @else
                                    <span class="badge bg-secondary rounded-pill px-3 py-2">
                                        ผู้ใช้งานทั่วไป
                                    </span>
                                @endif
                            </td>
                            <td class="py-3">
                                @if($user->is_active == 1 || $user->is_active === true)
                                    <span class="badge bg-success rounded-pill px-3 py-2">
                                        ใช้งาน
                                    </span>
                                @else
                                    <span class="badge bg-secondary rounded-pill px-3 py-2">
                                        ไม่ใช้งาน
                                    </span>
                                @endif
                            </td>
                            <td class="py-3 text-secondary">
                                {{ $user->created_at ? $user->created_at->format('d/m/Y') : '-' }}
                            </td>
                            <td class="py-3">
                                <div class="d-flex gap-3">
                                    <button wire:click="edit({{ $user->id }})" class="text-primary border-0 bg-transparent p-0" title="แก้ไข">
                                        <i class="fas fa-edit fa-lg"></i>
                                    </button>
                                    @if($user->id !== auth()->id())
                                        <button wire:click="confirmDelete({{ $user->id }})" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                            <i class="fas fa-trash fa-lg"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center text-secondary py-4">
                                <i class="fas fa-user-slash fa-2x mb-3 text-muted"></i>
                                <p>ไม่พบผู้ใช้งาน</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 d-flex justify-content-center" wire:key="users-pagination">
                {{ $users->links() }}
            </div>
        </div>
    </div>

    <!-- Create User Modal -->
    @if($isCreating)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มผู้ใช้งานใหม่</h5>
                    <button type="button" class="btn-close" wire:click="cancel"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row g-3">
                            <!-- ชื่อผู้ใช้งาน -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อผู้ใช้งาน <span class="text-danger">*</span></label>
                                    <input type="text" wire:model.live="name" id="name" class="form-control @error('name') is-invalid @enderror">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- อีเมล -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                                    <input type="email" wire:model.live="email" id="email" class="form-control @error('email') is-invalid @enderror">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- รหัสผ่าน -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">รหัสผ่าน <span class="text-danger">*</span></label>
                                    <input type="password" wire:model.live="password" id="password" class="form-control @error('password') is-invalid @enderror">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- ยืนยันรหัสผ่าน -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่าน <span class="text-danger">*</span></label>
                                    <input type="password" wire:model.live="password_confirmation" id="password_confirmation" class="form-control">
                                </div>
                            </div>

                            <!-- บทบาท -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">บทบาท <span class="text-danger">*</span></label>
                                    <select wire:model.live="role" id="role" class="form-select @error('role') is-invalid @enderror">
                                        @foreach($availableRoles as $availableRole)
                                            <option value="{{ $availableRole->name }}">
                                                @if($availableRole->name === 'admin')
                                                    ผู้ดูแลระบบ
                                                @elseif($availableRole->name === 'editor')
                                                    ผู้แก้ไข
                                                @elseif($availableRole->name === 'user')
                                                    ผู้ใช้งานทั่วไป
                                                @else
                                                    {{ $availableRole->name }}
                                                @endif
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('role')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- สถานะ -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label d-block">สถานะ</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" wire:model.live="is_active" id="is_active">
                                        <label class="form-check-label" for="is_active">
                                            {{ $is_active ? 'ใช้งาน' : 'ไม่ใช้งาน' }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- รูปโปรไฟล์ -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="temp_profile_image" class="form-label">รูปโปรไฟล์</label>
                                    <input type="file" wire:model.live="temp_profile_image" id="temp_profile_image" class="form-control @error('temp_profile_image') is-invalid @enderror" accept="image/*">
                                    <div wire:loading wire:target="temp_profile_image" class="text-primary mt-2">
                                        <i class="fas fa-spinner fa-spin me-1"></i> กำลังอัปโหลด...
                                    </div>
                                    @error('temp_profile_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror

                                    @if ($temp_profile_image)
                                        <div class="mt-2">
                                            <img src="{{ $temp_profile_image->temporaryUrl() }}" class="img-thumbnail" width="100" height="100">
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- ประวัติ -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="bio" class="form-label">ประวัติ</label>
                                    <textarea wire:model.live="bio" id="bio" rows="3" class="form-control @error('bio') is-invalid @enderror"></textarea>
                                    @error('bio')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="cancel">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" wire:click="store">บันทึก</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Edit User Modal -->
    @if($isEditing)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">แก้ไขผู้ใช้งาน</h5>
                    <button type="button" class="btn-close" wire:click="cancel"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row g-3">
                            <!-- ชื่อผู้ใช้งาน -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อผู้ใช้งาน <span class="text-danger">*</span></label>
                                    <input type="text" wire:model.live="name" id="name" class="form-control @error('name') is-invalid @enderror">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- อีเมล -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                                    <input type="email" wire:model.live="email" id="email" class="form-control @error('email') is-invalid @enderror">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- รหัสผ่าน (ตัวเลือก) -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">รหัสผ่าน <small class="text-muted">(เว้นว่างไว้หากไม่ต้องการเปลี่ยน)</small></label>
                                    <input type="password" wire:model.live="password" id="password" class="form-control @error('password') is-invalid @enderror">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- ยืนยันรหัสผ่าน -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่าน</label>
                                    <input type="password" wire:model.live="password_confirmation" id="password_confirmation" class="form-control">
                                </div>
                            </div>

                            <!-- บทบาท -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">บทบาท <span class="text-danger">*</span></label>
                                    <select wire:model.live="role" id="role" class="form-select @error('role') is-invalid @enderror">
                                        @foreach($availableRoles as $availableRole)
                                            <option value="{{ $availableRole->name }}">
                                                @if($availableRole->name === 'admin')
                                                    ผู้ดูแลระบบ
                                                @elseif($availableRole->name === 'editor')
                                                    ผู้แก้ไข
                                                @elseif($availableRole->name === 'user')
                                                    ผู้ใช้งานทั่วไป
                                                @else
                                                    {{ $availableRole->name }}
                                                @endif
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('role')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- สถานะ -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label d-block">สถานะ</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" wire:model.live="is_active" id="is_active">
                                        <label class="form-check-label" for="is_active">
                                            {{ $is_active ? 'ใช้งาน' : 'ไม่ใช้งาน' }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- ประวัติ -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="bio" class="form-label">ประวัติ</label>
                                    <textarea wire:model.live="bio" id="bio" rows="3" class="form-control @error('bio') is-invalid @enderror"></textarea>
                                    @error('bio')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="cancel">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" wire:click="update">บันทึกการเปลี่ยนแปลง</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Delete Confirmation Modal -->
    @if($confirmingUserDeletion)
    <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบผู้ใช้งาน</h5>
                    <button type="button" class="btn-close" wire:click="$set('confirmingUserDeletion', false)"></button>
                </div>
                <div class="modal-body">
                    <p>คุณแน่ใจหรือไม่ที่จะลบผู้ใช้งานนี้? การกระทำนี้ไม่สามารถย้อนกลับได้</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="$set('confirmingUserDeletion', false)">ยกเลิก</button>
                    <button type="button" class="btn btn-danger" wire:click="delete">ลบผู้ใช้งาน</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- SweetAlert Scripts -->
    <script>
        document.addEventListener('livewire:initialized', () => {
            @this.on('user-saved', (event) => {
                const action = event.action === 'created' ? 'เพิ่ม' : 'แก้ไข';
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: `${action}ผู้ใช้งานเรียบร้อยแล้ว`,
                    icon: 'success',
                    confirmButtonText: 'ตกลง'
                });
            });

            @this.on('user-deleted', () => {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: 'ลบผู้ใช้งานเรียบร้อยแล้ว',
                    icon: 'success',
                    confirmButtonText: 'ตกลง'
                });
            });

            @this.on('user-cannot-delete-self', () => {
                Swal.fire({
                    title: 'ไม่สามารถดำเนินการได้',
                    text: 'คุณไม่สามารถลบบัญชีของตัวเองได้',
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            });

            @this.on('user-error', (event) => {
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: event.message,
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            });
        });
    </script>
</div>
