<div>
    <div class="card shadow mb-4">
        <div class="card-body p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h3 class="fs-4 fw-bold mb-2">รายการ</h3>
                    <span class="text-secondary">รายการ: หมดในระบบ</span>
                </div>
                <div>
                    <a href="{{ route('admin.items.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> สร้างรายการใหม่
                    </a>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="mb-4">
                <div class="row g-3">
                    <div class="col-12 col-md-4">
                        <div class="input-group">
                            <span class="input-group-text bg-light">
                                <i class="fas fa-search text-secondary"></i>
                            </span>
                            <input type="text" wire:model.live.debounce.300ms="search" class="form-control" placeholder="ค้นหารายการ...">
                        </div>
                    </div>
                    <div class="col-12 col-md-2">
                        <select wire:model.live="categoryFilter" class="form-select">
                            <option value="">หมวด: หมด</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-12 col-md-2">
                        <select wire:model.live="typeFilter" class="form-select">
                            <option value="">ประเภท: หมด</option>
                            @foreach($itemTypes as $type)
                                <option value="{{ $type->id }}">{{ $type->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-12 col-md-2">
                        <select wire:model.live="languageFilter" class="form-select">
                            <option value="">ภาษา: หมด</option>
                            @foreach($languages as $language)
                                <option value="{{ $language->id }}">{{ $language->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-12 col-md-2">
                        <select wire:model.live="perPage" class="form-select">
                            <option value="10">10 รายการ</option>
                            <option value="15">15 รายการ</option>
                            <option value="25">25 รายการ</option>
                            <option value="50">50 รายการ</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div wire:loading class="text-center my-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">โหลด...</span>
                </div>
                <span class="ms-2">โหลดข้อมูล...</span>
            </div>

            <!-- Items Table -->
            <div class="table-responsive" wire:loading.class="opacity-50">
                <div class="mb-3 d-flex justify-content-between align-items-center">
                    <div>
                        @if(count($selectedItems) > 0)
                            <button type="button" class="btn btn-danger btn-sm" wire:click="confirmMultipleItemsDeletion">
                                <i class="fas fa-trash me-1"></i>ลบรายการ ({{ count($selectedItems) }})
                            </button>
                        @endif
                    </div>
                </div>
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" class="text-center" style="width: 40px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" wire:model.live="selectAll" id="selectAllCheckbox">
                                    <label class="form-check-label" for="selectAllCheckbox"></label>
                                </div>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('identifier_no')" class="text-decoration-none text-dark d-flex align-items-center">
                                    รหัสรายการ
                                    @if($sortField === 'identifier_no')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('title')" class="text-decoration-none text-dark d-flex align-items-center">
                                    ชื่อรายการ
                                    @if($sortField === 'title')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('category_id')" class="text-decoration-none text-dark d-flex align-items-center">
                                    หมวด
                                    @if($sortField === 'category_id')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('item_type_id')" class="text-decoration-none text-dark d-flex align-items-center">
                                    ประเภท
                                    @if($sortField === 'item_type_id')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('language_id')" class="text-decoration-none text-dark d-flex align-items-center">
                                    ภาษา
                                    @if($sortField === 'language_id')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('created_at')" class="text-decoration-none text-dark d-flex align-items-center">
                                    สร้าง
                                    @if($sortField === 'created_at')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                <a href="#" wire:click.prevent="sortBy('views')" class="text-decoration-none text-dark d-flex align-items-center">
                                    การเข้าชม
                                    @if($sortField === 'views')
                                        <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @else
                                        <i class="fas fa-sort ms-1 text-muted"></i>
                                    @endif
                                </a>
                            </th>
                            <th scope="col" class="text-uppercase small fw-bold">
                                ลบ

                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($items as $item)
                        <tr wire:key="item-{{ $item->id }}">
                            <td class="text-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="{{ $item->id }}" 
                                           wire:model.live="selectedItems" id="checkbox-{{ $item->id }}">
                                    <label class="form-check-label" for="checkbox-{{ $item->id }}"></label>
                                </div>
                            </td>
                            <td class="fw-medium">
                                {{ $item->identifier_no ?: '-' }}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0" style="width: 40px; height: 40px;">
                                        <img class="rounded img-fluid" style="width: 40px; height: 40px; object-fit: cover;" src="{{ get_image_url($item->first_image, 'defaults/item-default.svg') }}" alt="{{ $item->title }}">
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-medium">
                                            {{ Str::limit($item->title, 50) }}
                                        </div>
                                        @if($item->year)
                                        <div class="small text-secondary">
                                            {{ $item->year }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="text-secondary">
                                {{ $item->category ? $item->category->name : '-' }}
                            </td>
                            <td class="text-secondary">
                                {{ $item->itemType ? $item->itemType->name : '-' }}
                            </td>
                            <td class="text-secondary">
                                {{ $item->language ? $item->language->name : '-' }}
                            </td>
                            <td class="text-secondary">
                                {{ $item->created_at ? $item->created_at->format('d/m/Y') : '-' }}
                            </td>
                            <td class="text-secondary">
                                {{ number_format($item->views) }}
                            </td>
                            <td>
                                <div class="d-flex gap-3">
                                    <a href="{{ route('admin.items.edit', $item->id) }}" class="text-primary" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('items.show', $item->id) }}" target="_blank" class="text-success" title="ดู">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button wire:click="confirmItemDeletion({{ $item->id }})" class="text-danger border-0 bg-transparent p-0" title="ลบ">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="9" class="text-center text-secondary py-3">
                                <i class="fas fa-file-alt fa-2x mb-3 text-muted"></i>
                                <p>ไม่พบรายการ</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4" wire:key="items-pagination">
                {{ $items->links() }}
            </div>
        </div>
    </div>

    <!-- SweetAlert Scripts -->
    <script>
        document.addEventListener('livewire:initialized', () => {
            @this.on('show-delete-confirmation', () => {
                Swal.fire({
                    title: 'การลบรายการ',
                    text: 'แน่ใจไม่จะลบรายการ? การกระทำไม่สามารถย้อนกลับได้',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'ลบรายการ',
                    cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                    if (result.isConfirmed) {
                        @this.call('deleteItem');
                    } else {
                        @this.call('cancelItemDeletion');
                    }
                });
            });
            
            @this.on('show-multiple-delete-confirmation', () => {
                Swal.fire({
                    title: 'การลบรายการ',
                    text: 'แน่ใจไม่จะลบรายการทั้งหมด? การกระทำไม่สามารถย้อนกลับได้',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'ลบรายการ',
                    cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                    if (result.isConfirmed) {
                        @this.call('deleteMultipleItems');
                    }
                });
            });

            @this.on('item-deleted', (event) => {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: event.message,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
            
            @this.on('items-deleted', (event) => {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: event.message,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        });
    </script>
</div>
