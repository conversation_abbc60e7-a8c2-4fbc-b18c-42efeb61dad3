<div>
    <!-- Loading Indicator -->
    <div wire:loading.delay class="wire-loading-indicator">
        <div class="loading-spinner" role="status">
            <span class="visually-hidden">กำลังโหลด...</span>
        </div>
        <div class="loading-text">กำลังโหลดข้อมูล</div>
        <div class="loading-dots">
            <span class="dot dot-1">.</span>
            <span class="dot dot-2">.</span>
            <span class="dot dot-3">.</span>
        </div>
    </div>

    <div class="row" wire:loading.class="opacity-50 content-loading">
        <div class="col-lg-12">
            <div class="card mb-4 shadow-sm">
                <div class="card-body search-card-body">
                    <div class="mb-4">
                        <div class="input-group input-group-lg mb-2">
                            <input type="text" wire:model.live.debounce.300ms="searchTerm" placeholder="ค้นหารายการ..." class="form-control">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>ค้นหา
                            </button>
                        </div>
                        <div class="mt-2">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" wire:model.live="searchType" id="search_type_all" value="all">
                                <label class="form-check-label" for="search_type_all">ทั้งหมด</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" wire:model.live="searchType" id="search_type_title" value="title">
                                <label class="form-check-label" for="search_type_title">ชื่อรายการ</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" wire:model.live="searchType" id="search_type_identifier" value="identifier">
                                <label class="form-check-label" for="search_type_identifier">รหัสรายการ</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- หมวดหมู่ (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">หมวดหมู่</label>
                            <div class="filter-box">
                                @foreach($categories as $category)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $category->id }}"
                                        wire:model.live="categoryIds"
                                        id="category_{{ $category->id }}">
                                    <label class="form-check-label" for="category_{{ $category->id }}">
                                        {{ $category->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ประเภทรายการ (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ประเภทรายการ</label>
                            <div class="filter-box">
                                @foreach($itemTypes as $type)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $type->id }}"
                                        wire:model.live="itemTypeIds"
                                        id="type_{{ $type->id }}">
                                    <label class="form-check-label" for="type_{{ $type->id }}">
                                        {{ $type->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- วัสดุ (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">วัสดุ</label>
                            <div class="filter-box">
                                @foreach($materials as $material)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $material->id }}"
                                        wire:model.live="materialIds"
                                        id="material_{{ $material->id }}">
                                    <label class="form-check-label" for="material_{{ $material->id }}">
                                        {{ $material->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ภาษา (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ภาษา</label>
                            <div class="filter-box">
                                @foreach($languages as $language)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $language->id }}"
                                        wire:model.live="languageIds"
                                        id="language_{{ $language->id }}">
                                    <label class="form-check-label" for="language_{{ $language->id }}">
                                        {{ $language->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ตัวอักษร (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ตัวอักษร</label>
                            <div class="filter-box">
                                @foreach($scripts as $script)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $script->id }}"
                                        wire:model.live="scriptIds"
                                        id="script_{{ $script->id }}">
                                    <label class="form-check-label" for="script_{{ $script->id }}">
                                        {{ $script->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ประเทศ (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ประเทศ</label>
                            <div class="filter-box">
                                @foreach($countries as $country)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $country->code }}"
                                        wire:model.live="countryIds"
                                        id="country_{{ $country->code }}">
                                    <label class="form-check-label" for="country_{{ $country->code }}">
                                        {{ $country->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- จังหวัด (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">จังหวัด</label>
                            <div class="filter-box">
                                @foreach($provinces as $province)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $province->code }}"
                                        wire:model.live="provinceCodes"
                                        id="province_{{ $province->code }}">
                                    <label class="form-check-label" for="province_{{ $province->code }}">
                                        {{ $province->name_th }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ช่วงปี -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ช่วงปี</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" class="form-control" placeholder="ปีเริ่มต้น"
                                        wire:model.live.debounce.500ms="yearFrom">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control" placeholder="ปีสิ้นสุด"
                                        wire:model.live.debounce.500ms="yearTo">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" wire:click="resetFilters" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-2"></i>ล้างตัวกรอง
                        </button>
                    </div>
                </div>
            </div>

            <style>
                /* Search badge styles */
                .search-badge {
                    display: inline-flex;
                    align-items: center;
                    background-color: #6c757d;
                    color: white;
                    padding: 0.5rem 0.75rem;
                    border-radius: 0.375rem;
                    font-size: 0.875rem;
                    font-weight: 500;
                    gap: 0.5rem;
                    margin-bottom: 0.5rem;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    transition: all 0.2s ease;
                }

                .search-badge:hover {
                    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
                    transform: translateY(-1px);
                }

                /* Different colors for different badge types */
                .search-term-badge {
                    background-color: #0d6efd;
                }

                .category-badge {
                    background-color: #6f42c1;
                }

                .item-type-badge {
                    background-color: #fd7e14;
                }

                .year-badge {
                    background-color: #198754;
                }

                .material-badge {
                    background-color: #dc3545;
                }

                .language-badge {
                    background-color: #0dcaf0;
                }

                .script-badge {
                    background-color: #6c757d;
                }

                .country-badge {
                    background-color: #20c997;
                }

                .province-badge {
                    background-color: #6610f2;
                }

                .badge-close-btn {
                    background: none;
                    border: none;
                    color: white;
                    opacity: 0.8;
                    padding: 0;
                    margin-left: 0.5rem;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    transition: all 0.2s;
                    background-color: rgba(255, 255, 255, 0.1);
                }

                .badge-close-btn:hover {
                    opacity: 1;
                    background-color: rgba(255, 255, 255, 0.2);
                }

                /* Item badge styles */
                .item-badge {
                    padding: 0.4rem 0.6rem;
                    font-size: 0.75rem;
                    font-weight: 500;
                    border-radius: 0.25rem;
                }

                /* Card styles */
                .card {
                    transition: all 0.3s ease;
                    border: none;
                }

                .card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
                }

                .card-img-top {
                    transition: all 0.3s ease;
                }

                .card:hover .card-img-top {
                    opacity: 0.9;
                }

                /* Spacing improvements */
                .mb-4 {
                    margin-bottom: 1.8rem !important;
                }

                .card-body {
                    padding: 1.25rem !important;
                }

                /* Pagination styles */
                .pagination {
                    gap: 0.25rem;
                }

                .page-item .page-link {
                    border-radius: 0.25rem;
                    padding: 0.5rem 0.75rem;
                    color: #6c757d;
                    border: 1px solid #dee2e6;
                    transition: all 0.2s;
                }

                .page-item.active .page-link {
                    background-color: #0d6efd;
                    border-color: #0d6efd;
                    color: white;
                    font-weight: 500;
                }

                .page-item .page-link:hover {
                    background-color: #e9ecef;
                    color: #0d6efd;
                    border-color: #dee2e6;
                    z-index: 2;
                }

                /* Filter box styles */
                .filter-box {
                    border: 1px solid #dee2e6;
                    border-radius: 0.375rem;
                    padding: 0.75rem;
                    max-height: 150px;
                    overflow-y: auto;
                    background-color: #fff;
                    scrollbar-width: thin;
                    scrollbar-color: #adb5bd #f8f9fa;
                }

                /* Custom scrollbar for WebKit browsers */
                .filter-box::-webkit-scrollbar {
                    width: 8px;
                }

                .filter-box::-webkit-scrollbar-track {
                    background: #f8f9fa;
                    border-radius: 4px;
                }

                .filter-box::-webkit-scrollbar-thumb {
                    background-color: #adb5bd;
                    border-radius: 4px;
                    border: 2px solid #f8f9fa;
                }

                .filter-box .form-check {
                    margin-bottom: 0.5rem;
                    padding-left: 1.75rem;
                }

                .filter-box .form-check:last-child {
                    margin-bottom: 0;
                }

                .filter-box .form-check-input {
                    margin-left: -1.75rem;
                }

                .form-label {
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                /* Form control styles */
                .form-control {
                    padding: 0.5rem 0.75rem;
                    border-radius: 0.375rem;
                    border: 1px solid #dee2e6;
                    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                }

                .form-control:focus {
                    border-color: #86b7fe;
                    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
                }

                /* Form check styles */
                .form-check-input {
                    width: 1.1em;
                    height: 1.1em;
                    margin-top: 0.2em;
                }

                .form-check-input:checked {
                    background-color: #0d6efd;
                    border-color: #0d6efd;
                }

                .form-check-label {
                    cursor: pointer;
                }

                /* Search card body styles */
                .search-card-body {
                    padding: 1.75rem !important;
                }

                /* Form check inline styles */
                .form-check-inline {
                    margin-right: 1rem;
                }

                .form-check-inline .form-check-input {
                    margin-right: 0.5rem;
                }

                /* Input group styles */
                .input-group {
                    margin-bottom: 1.5rem;
                }

                .input-group-lg > .form-control {
                    padding: 0.75rem 1rem;
                    font-size: 1.1rem;
                }

                .input-group .btn {
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                }

                /* Search badges container */
                .search-badges-container {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0.5rem;
                    margin-bottom: 1rem;
                    padding: 0.5rem;
                    border-radius: 0.375rem;
                    background-color: #f8f9fa;
                    min-height: 3rem;
                }

                /* Empty state for search badges container */
                .search-badges-container:empty {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #6c757d;
                    font-style: italic;
                }

                .search-badges-container:empty::before {
                    content: "ยังไม่มีตัวกรองที่เลือก";
                    font-size: 0.875rem;
                }
            </style>

            @if($isSearching)
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h5 class="mb-0">ผลการค้นหา</h5>
                                <p class="text-muted mb-0">พบ {{ $totalResults }} รายการ</p>
                            </div>
                            <div>
                                <select wire:model.live="sortBy" class="form-select form-select-sm">
                                    <option value="newest">เรียงตาม: ล่าสุด</option>
                                    <option value="oldest">เรียงตาม: เก่าสุด</option>
                                    <option value="a-z">เรียงตาม: A-Z</option>
                                    <option value="z-a">เรียงตาม: Z-A</option>
                                    <option value="year-asc">เรียงตาม: ปี (น้อยไปมาก)</option>
                                    <option value="year-desc">เรียงตาม: ปี (มากไปน้อย)</option>
                                    <option value="views">เรียงตาม: ยอดเข้าชม</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">ตัวกรองที่เลือก</h6>
                                <button wire:click="resetFilters" class="btn btn-sm btn-outline-secondary" title="ล้างตัวกรองทั้งหมด">
                                    <i class="fas fa-times me-1"></i> ล้างทั้งหมด
                                </button>
                            </div>
                            <div class="search-badges-container">
                                @if($searchTerm)
                                    <div class="search-badge search-term-badge">
                                        <span><i class="fas fa-search me-1"></i> คำค้นหา: <strong>{{ $searchTerm }}</strong></span>
                                        <button wire:click="$set('searchTerm', '')" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($categoryIds) > 0)
                                    <div class="search-badge category-badge">
                                        <span><i class="fas fa-folder me-1"></i> หมวดหมู่:
                                        <strong>
                                            @php
                                                $categoryNames = [];
                                                foreach($categoryIds as $id) {
                                                    $category = $categories->where('id', $id)->first();
                                                    if ($category) {
                                                        $categoryNames[] = $category->name;
                                                    }
                                                }
                                                echo implode(', ', $categoryNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('categoryIds', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($itemTypeIds) > 0)
                                    <div class="search-badge item-type-badge">
                                        <span><i class="fas fa-file-alt me-1"></i> ประเภทรายการ:
                                        <strong>
                                            @php
                                                $typeNames = [];
                                                foreach($itemTypeIds as $id) {
                                                    $type = $itemTypes->where('id', $id)->first();
                                                    if ($type) {
                                                        $typeNames[] = $type->name;
                                                    }
                                                }
                                                echo implode(', ', $typeNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('itemTypeIds', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if($yearFrom || $yearTo)
                                    <div class="search-badge year-badge">
                                        <span><i class="fas fa-calendar-alt me-1"></i> ปี: <strong>{{ $yearFrom ?: 'ทั้งหมด' }} - {{ $yearTo ?: 'ทั้งหมด' }}</strong></span>
                                        <button wire:click="$set('yearFrom', ''); $set('yearTo', '');" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($materialIds) > 0)
                                    <div class="search-badge material-badge">
                                        <span><i class="fas fa-cube me-1"></i> วัสดุ:
                                        <strong>
                                            @php
                                                $materialNames = [];
                                                foreach($materialIds as $id) {
                                                    $material = $materials->where('id', $id)->first();
                                                    if ($material) {
                                                        $materialNames[] = $material->name;
                                                    }
                                                }
                                                echo implode(', ', $materialNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('materialIds', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($languageIds) > 0)
                                    <div class="search-badge language-badge">
                                        <span><i class="fas fa-language me-1"></i> ภาษา:
                                        <strong>
                                            @php
                                                $languageNames = [];
                                                foreach($languageIds as $id) {
                                                    $language = $languages->where('id', $id)->first();
                                                    if ($language) {
                                                        $languageNames[] = $language->name;
                                                    }
                                                }
                                                echo implode(', ', $languageNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('languageIds', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($scriptIds) > 0)
                                    <div class="search-badge script-badge">
                                        <span><i class="fas fa-font me-1"></i> ตัวอักษร:
                                        <strong>
                                            @php
                                                $scriptNames = [];
                                                foreach($scriptIds as $id) {
                                                    $script = $scripts->where('id', $id)->first();
                                                    if ($script) {
                                                        $scriptNames[] = $script->name;
                                                    }
                                                }
                                                echo implode(', ', $scriptNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('scriptIds', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($countryIds) > 0)
                                    <div class="search-badge country-badge">
                                        <span><i class="fas fa-globe me-1"></i> ประเทศ:
                                        <strong>
                                            @php
                                                $countryNames = [];
                                                foreach($countryIds as $code) {
                                                    $country = $countries->where('code', $code)->first();
                                                    if ($country) {
                                                        $countryNames[] = $country->name;
                                                    }
                                                }
                                                echo implode(', ', $countryNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('countryIds', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif

                                @if(count($provinceCodes) > 0)
                                    <div class="search-badge province-badge">
                                        <span><i class="fas fa-map-marker-alt me-1"></i> จังหวัด:
                                        <strong>
                                            @php
                                                $provinceNames = [];
                                                foreach($provinceCodes as $code) {
                                                    $province = $provinces->where('code', $code)->first();
                                                    if ($province) {
                                                        $provinceNames[] = $province->name_th;
                                                    }
                                                }
                                                echo implode(', ', $provinceNames);
                                            @endphp
                                        </strong></span>
                                        <button wire:click="$set('provinceCodes', [])" class="badge-close-btn" aria-label="Close">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                @if($items->isEmpty())
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4>ไม่พบรายการที่ตรงกับเงื่อนไขการค้นหา</h4>
                            <p class="text-muted">ลองเปลี่ยนคำค้นหาหรือตัวกรองแล้วลองใหม่อีกครั้ง</p>
                            <button wire:click="resetFilters" class="btn btn-primary mt-2">ล้างการค้นหา</button>
                        </div>
                    </div>
                @else
                    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4 mb-4">
                        @foreach($items as $item)
                            <div class="col">
                                <div class="card h-100 shadow-sm">
                                    <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none">
                                        <img src="{{ get_image_url($item->first_image, 'defaults/item-default.svg') }}" class="card-img-top" alt="{{ $item->title }}" style="height: 200px; object-fit: cover;">
                                    </a>
                                    <div class="card-body">
                                        <a href="{{ route('items.show', $item->id) }}" class="text-decoration-none text-dark">
                                            <h5 class="card-title">{{ $item->title }}</h5>
                                        </a>
                                        <p class="card-text text-muted small">
                                            @if($item->year)
                                                <i class="far fa-calendar-alt me-1"></i> {{ $item->year }}
                                            @endif
                                            @if($item->views)
                                                <span class="ms-2"><i class="far fa-eye me-1"></i> {{ $item->views }}</span>
                                            @endif
                                        </p>
                                        <div class="d-flex flex-wrap gap-1 mt-2 justify-content-start">
                                            @if($item->category)
                                                <span class="badge bg-secondary item-badge">{{ $item->category->name }}</span>
                                            @endif
                                            @if($item->itemType)
                                                <span class="badge bg-secondary item-badge">{{ $item->itemType->name }}</span>
                                            @endif
                                            @if($item->material)
                                                <span class="badge bg-secondary item-badge">{{ $item->material->name }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div class="d-flex justify-content-center mt-4 mb-5">
                        {{ $items->links() }}
                    </div>
                @endif
            @else
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>ค้นหารายการ</h4>
                        <p class="text-muted">กรุณาระบุคำค้นหาหรือตัวกรองเพื่อค้นหารายการ</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
