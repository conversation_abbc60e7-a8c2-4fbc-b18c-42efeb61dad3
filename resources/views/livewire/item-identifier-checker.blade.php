<div>
    <label for="identifier_no" class="form-label">รหัสรายการ (ไม่จำเป็นต้องกรอก)</label>
    <div class="position-relative">
        <input
            type="text"
            class="form-control @error('identifier_no') is-invalid @enderror"
            id="identifier_no"
            name="identifier_no"
            wire:model.live.debounce.300ms="identifier"
            value="{{ $identifier }}"
        >

        @if($isChecking)
            <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">กำลังตรวจสอบ...</span>
                </div>
            </div>
        @elseif($isValid === true)
            <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                <span class="text-success fs-5">
                    <i class="fas fa-check-circle"></i>
                </span>
            </div>
        @elseif($isValid === false)
            <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                <span class="text-danger fs-5">
                    <i class="fas fa-exclamation-circle"></i>
                </span>
            </div>
        @endif
    </div>

    <small class="text-muted">สามารถเว้นว่างได้ หรือระบุรหัสรายการที่ไม่ซ้ำกับรายการอื่น</small>

    @error('identifier_no')
        <div class="invalid-feedback">{{ $message }}</div>
    @enderror

    <div class="mt-1">
        @if($isChecking)
            <div class="alert alert-info py-1 px-2 mb-0 mt-1">
                <small>กำลังตรวจสอบรหัสรายการ...</small>
            </div>
        @elseif($isValid === true)
            <div class="alert alert-success py-1 px-2 mb-0 mt-1">
                <small><i class="fas fa-check-circle me-1"></i>{{ $message }}</small>
            </div>
        @elseif($isValid === false)
            <div class="alert alert-danger py-1 px-2 mb-0 mt-1">
                <small><i class="fas fa-exclamation-circle me-1"></i>{{ $message }}</small>
            </div>
        @endif
    </div>
</div>
