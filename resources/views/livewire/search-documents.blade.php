<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card mb-4 shadow-sm">
                <div class="card-body p-4">
                    <div class="mb-4">
                        <div class="input-group input-group-lg mb-2">
                            <input type="text" wire:model.live.debounce.300ms="searchTerm" placeholder="ค้นหาเอกสาร..." class="form-control">
                            <button type="button" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>ค้นหา
                            </button>
                        </div>
                        <div class="mt-2">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" wire:model.live="searchType" id="search_type_all" value="all">
                                <label class="form-check-label" for="search_type_all">ทั้งหมด</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" wire:model.live="searchType" id="search_type_title" value="title">
                                <label class="form-check-label" for="search_type_title">ชื่อเอกสาร</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" wire:model.live="searchType" id="search_type_identifier" value="identifier">
                                <label class="form-check-label" for="search_type_identifier">รหัสเอกสาร</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- หมวดหมู่ (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">หมวดหมู่</label>
                            <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                @foreach($categories as $category)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $category->id }}"
                                        wire:model.live="categoryIds"
                                        id="category_{{ $category->id }}">
                                    <label class="form-check-label" for="category_{{ $category->id }}">
                                        {{ $category->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ประเภทเอกสาร (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ประเภทเอกสาร</label>
                            <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                @foreach($documentTypes as $type)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $type->id }}"
                                        wire:model.live="documentTypeIds"
                                        id="doctype_{{ $type->id }}">
                                    <label class="form-check-label" for="doctype_{{ $type->id }}">
                                        {{ $type->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- วัสดุ (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">วัสดุ</label>
                            <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                @foreach($materials as $material)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $material->id }}"
                                        wire:model.live="materialIds"
                                        id="material_{{ $material->id }}">
                                    <label class="form-check-label" for="material_{{ $material->id }}">
                                        {{ $material->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ภาษา (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ภาษา</label>
                            <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                @foreach($languages as $language)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $language->id }}"
                                        wire:model.live="languageIds"
                                        id="language_{{ $language->id }}">
                                    <label class="form-check-label" for="language_{{ $language->id }}">
                                        {{ $language->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ตัวอักษร (Multi-select) -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ตัวอักษร</label>
                            <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
                                @foreach($scripts as $script)
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        value="{{ $script->id }}"
                                        wire:model.live="scriptIds"
                                        id="script_{{ $script->id }}">
                                    <label class="form-check-label" for="script_{{ $script->id }}">
                                        {{ $script->name }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- ช่วงปี -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">ช่วงปี</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" class="form-control" placeholder="ปีเริ่มต้น"
                                        wire:model.live.debounce.500ms="yearFrom">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control" placeholder="ปีสิ้นสุด"
                                        wire:model.live.debounce.500ms="yearTo">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" wire:click="resetFilters" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-2"></i>ล้างตัวกรอง
                        </button>
                    </div>
                </div>
            </div>

            @if($isSearching)
                <div class="card mb-4 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 class="mb-0">ผลการค้นหา</h4>
                            <span class="badge bg-primary rounded-pill">พบ {{ $totalResults }} รายการ</span>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex flex-wrap gap-2">
                                @if($searchTerm)
                                    <div class="badge bg-light text-dark p-2">
                                        คำค้นหา: <strong>{{ $searchTerm }}</strong>
                                        <button wire:click="$set('searchTerm', '')" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if(count($categoryIds) > 0)
                                    <div class="badge bg-light text-dark p-2">
                                        หมวดหมู่:
                                        <strong>
                                            @php
                                                $categoryNames = [];
                                                foreach($categoryIds as $id) {
                                                    $category = $categories->where('id', $id)->first();
                                                    if ($category) {
                                                        $categoryNames[] = $category->name;
                                                    }
                                                }
                                                echo implode(', ', $categoryNames);
                                            @endphp
                                        </strong>
                                        <button wire:click="$set('categoryIds', [])" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if(count($documentTypeIds) > 0)
                                    <div class="badge bg-light text-dark p-2">
                                        ประเภทเอกสาร:
                                        <strong>
                                            @php
                                                $typeNames = [];
                                                foreach($documentTypeIds as $id) {
                                                    $type = $documentTypes->where('id', $id)->first();
                                                    if ($type) {
                                                        $typeNames[] = $type->name;
                                                    }
                                                }
                                                echo implode(', ', $typeNames);
                                            @endphp
                                        </strong>
                                        <button wire:click="$set('documentTypeIds', [])" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if(count($materialIds) > 0)
                                    <div class="badge bg-light text-dark p-2">
                                        วัสดุ:
                                        <strong>
                                            @php
                                                $materialNames = [];
                                                foreach($materialIds as $id) {
                                                    $material = $materials->where('id', $id)->first();
                                                    if ($material) {
                                                        $materialNames[] = $material->name;
                                                    }
                                                }
                                                echo implode(', ', $materialNames);
                                            @endphp
                                        </strong>
                                        <button wire:click="$set('materialIds', [])" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if(count($languageIds) > 0)
                                    <div class="badge bg-light text-dark p-2">
                                        ภาษา:
                                        <strong>
                                            @php
                                                $languageNames = [];
                                                foreach($languageIds as $id) {
                                                    $language = $languages->where('id', $id)->first();
                                                    if ($language) {
                                                        $languageNames[] = $language->name;
                                                    }
                                                }
                                                echo implode(', ', $languageNames);
                                            @endphp
                                        </strong>
                                        <button wire:click="$set('languageIds', [])" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if(count($scriptIds) > 0)
                                    <div class="badge bg-light text-dark p-2">
                                        ตัวอักษร:
                                        <strong>
                                            @php
                                                $scriptNames = [];
                                                foreach($scriptIds as $id) {
                                                    $script = $scripts->where('id', $id)->first();
                                                    if ($script) {
                                                        $scriptNames[] = $script->name;
                                                    }
                                                }
                                                echo implode(', ', $scriptNames);
                                            @endphp
                                        </strong>
                                        <button wire:click="$set('scriptIds', [])" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if($yearFrom)
                                    <div class="badge bg-light text-dark p-2">
                                        ปีเริ่มต้น: {{ $yearFrom }}
                                        <button wire:click="$set('yearFrom', '')" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif

                                @if($yearTo)
                                    <div class="badge bg-light text-dark p-2">
                                        ปีสิ้นสุด: {{ $yearTo }}
                                        <button wire:click="$set('yearTo', '')" class="ms-2 btn-close btn-close-sm" aria-label="Close"></button>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <label for="sort_by" class="form-label me-2">เรียงตาม:</label>
                                <select class="form-select form-select-sm d-inline-block w-auto" id="sort_by" wire:model.live="sortBy">
                                    <option value="newest">ล่าสุด</option>
                                    <option value="oldest">เก่าสุด</option>
                                    <option value="a-z">ชื่อ A-Z</option>
                                    <option value="z-a">ชื่อ Z-A</option>
                                    <option value="year-asc">ปี (น้อยไปมาก)</option>
                                    <option value="year-desc">ปี (มากไปน้อย)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                @if($documents->isEmpty())
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4>ไม่พบเอกสารที่ตรงกับเงื่อนไขการค้นหา</h4>
                            <p class="text-muted">ลองเปลี่ยนคำค้นหาหรือตัวกรองแล้วลองใหม่อีกครั้ง</p>
                            <button wire:click="resetFilters" class="btn btn-primary mt-2">ล้างการค้นหา</button>
                        </div>
                    </div>
                @else
                    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4 mb-4">
                        @foreach($documents as $document)
                            <div class="col">
                                <div class="card h-100 shadow-sm">
                                    <a href="{{ route('documents.show', $document->id) }}" class="text-decoration-none">
                                        <img src="{{ get_image_url($document->first_image, 'defaults/document-default.svg') }}?v={{ time() }}" alt="{{ $document->title }}" class="card-img-top" style="height: 200px; object-fit: cover;">
                                    </a>
                                    <div class="card-body">
                                        <a href="{{ route('documents.show', $document->id) }}" class="text-decoration-none">
                                            <h5 class="card-title text-truncate">{{ $document->title }}</h5>
                                        </a>
                                        <div class="d-flex flex-wrap gap-2 mt-2">
                                            @if($document->year)
                                                <span class="badge bg-light text-dark">ปี {{ $document->year }}</span>
                                            @endif
                                            @if($document->documentType)
                                                <span class="badge bg-light text-dark">{{ $document->documentType->name }}</span>
                                            @endif
                                            @if($document->category)
                                                <span class="badge bg-light text-dark">{{ $document->category->name }}</span>
                                            @endif
                                            @if($document->material)
                                                <span class="badge bg-light text-dark">{{ $document->material->name }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div class="d-flex justify-content-center mt-4 mb-5">
                        {{ $documents->links() }}
                    </div>
                @endif
            @else
                <div class="card shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>ค้นหาเอกสารโบราณ</h4>
                        <p class="text-muted">กรุณาระบุคำค้นหาหรือตัวกรองเพื่อค้นหาเอกสาร</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
