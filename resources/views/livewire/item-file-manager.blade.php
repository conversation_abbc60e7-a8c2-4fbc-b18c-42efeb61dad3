<div>
    <!-- Success and Error Messages -->
    @if($successMessage)
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ $successMessage }}
            <button type="button" class="btn-close" wire:click="$set('successMessage', '')"></button>
        </div>
    @endif

    @if($errorMessage)
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ $errorMessage }}
            <button type="button" class="btn-close" wire:click="$set('errorMessage', '')"></button>
        </div>
    @endif

    <!-- Item Files Display as Table -->
    @if($itemFiles->count() > 0)
        <div class="card">
            <div class="card-header bg-white py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-file-alt text-primary me-2"></i> ไฟล์รายการ ({{ $itemFiles->count() }} ไฟล์)
                    </h6>
                    <span class="badge bg-primary rounded-pill">{{ $itemFiles->count() }} ไฟล์</span>
                </div>
                <div class="mt-2 small text-muted">
                    <i class="fas fa-info-circle me-1"></i> คลิกที่รายการไฟล์เพื่อตั้งเป็นไฟล์หลัก <span class="text-warning"><i class="fas fa-star"></i></span>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 50px;" class="text-center">#</th>
                                <th style="width: 100px;">ประเภท</th>
                                <th>ชื่อไฟล์</th>
                                <th style="width: 100px;" class="text-end">ขนาด</th>
                                <th style="width: 180px;" class="text-center">การจัดการ</th>
                            </tr>
                        </thead>
                <tbody>
                    @foreach($itemFiles as $index => $file)
                        @php
                            // Handle both DB files and temporary files
                            $isTemp = is_array($file) || (is_object($file) && !($file instanceof \App\Models\ItemFile));

                            if ($isTemp) {
                                // Temporary file (array format)
                                $fileId = $file['id'] ?? 'temp_' . uniqid();
                                $fileName = $file['file_name'] ?? '';
                                $isMain = $file['is_main'] ?? false;
                                $filePath = $file['file_path'] ?? '';

                                // Standardize file path handling for temporary files
                                $cleanPath = preg_replace('#^/?(storage/)?#', '', $filePath);

                                // Ensure the path starts with 'files/'
                                if (!str_starts_with($cleanPath, 'files/')) {
                                    $cleanPath = 'files/' . $cleanPath;
                                }

                                // Create the asset URL
                                $fileUrl = $file['url'] ?? asset('storage/' . $cleanPath);
                                $fileType = strtolower($file['file_type'] ?? '');

                                // Get file size from the temporary file data
                                $fileSize = $file['file_size'] ?? $file['size'] ?? 0;

                                // If file size is not available or is 0, try to get it from the file system
                                if ($fileSize == 0) {
                                    // Try to get file size from public storage
                                    $publicPath = public_path('storage/' . $cleanPath);
                                    if (file_exists($publicPath)) {
                                        $fileSize = filesize($publicPath);
                                    }
                                    // Try storage path if public path doesn't exist
                                    else {
                                        $storagePath = storage_path('app/public/' . $cleanPath);
                                        if (file_exists($storagePath)) {
                                            $fileSize = filesize($storagePath);
                                        }
                                        // Try with the original path as a fallback
                                        else {
                                            // Try with the original path
                                            $originalPath = public_path(ltrim($filePath, '/'));
                                            if (file_exists($originalPath)) {
                                                $fileSize = filesize($originalPath);
                                            }
                                            // Try with storage path using the original path
                                            else {
                                                $originalStoragePath = storage_path('app/public/' . ltrim(str_replace('/storage/', '', $filePath), '/'));
                                                if (file_exists($originalStoragePath)) {
                                                    $fileSize = filesize($originalStoragePath);
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                // Database file (ItemFile model)
                                $fileId = $file->id;
                                $fileName = $file->file_name;
                                $isMain = $file->is_main;
                                $filePath = $file->file_path;

                                // Standardize file path handling
                                $cleanPath = preg_replace('#^/?(storage/)?#', '', $filePath);

                                // Ensure the path starts with 'files/'
                                if (!str_starts_with($cleanPath, 'files/')) {
                                    $cleanPath = 'files/' . $cleanPath;
                                }

                                // Create the asset URL
                                $fileUrl = asset('storage/' . $cleanPath);
                                $fileType = strtolower($file->file_type);

                                // Format file size - first try to get it from the model
                                $fileSize = $file->file_size ?? 0;

                                // If file size is not available in the model or is 0, try to get it from the file system
                                if ($fileSize == 0) {
                                    // Try to get file size from public storage
                                    $publicPath = public_path('storage/' . $cleanPath);
                                    if (file_exists($publicPath)) {
                                        $fileSize = filesize($publicPath);
                                    }
                                    // Try storage path if public path doesn't exist
                                    else {
                                        $storagePath = storage_path('app/public/' . $cleanPath);
                                        if (file_exists($storagePath)) {
                                            $fileSize = filesize($storagePath);
                                        }
                                        // Try with the original path as a fallback
                                        else {
                                            // Try with the original path
                                            $originalPath = public_path(ltrim($file->file_path, '/'));
                                            if (file_exists($originalPath)) {
                                                $fileSize = filesize($originalPath);
                                            }
                                            // Try with storage path using the original path
                                            else {
                                                $originalStoragePath = storage_path('app/public/' . ltrim(str_replace('/storage/', '', $file->file_path), '/'));
                                                if (file_exists($originalStoragePath)) {
                                                    $fileSize = filesize($originalStoragePath);
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            $icon = 'fa-file';
                            $typeLabel = 'ไฟล์';
                            $bgColor = 'bg-secondary';

                            if (in_array($fileType, ['pdf'])) {
                                $icon = 'fa-file-pdf';
                                $typeLabel = 'PDF';
                                $bgColor = 'bg-danger';
                            } elseif (in_array($fileType, ['mp3', 'wav', 'ogg'])) {
                                $icon = 'fa-file-audio';
                                $typeLabel = 'เสียง';
                                $bgColor = 'bg-warning';
                            } elseif (in_array($fileType, ['mp4', 'webm', 'mov', 'avi'])) {
                                $icon = 'fa-file-video';
                                $typeLabel = 'วิดีโอ';
                                $bgColor = 'bg-info';
                            }

                            // Format the file size
                            $formattedSize = '';
                            if ($fileSize < 1024) {
                                $formattedSize = $fileSize . ' B';
                            } elseif ($fileSize < 1024 * 1024) {
                                $formattedSize = round($fileSize / 1024, 2) . ' KB';
                            } else {
                                $formattedSize = round($fileSize / (1024 * 1024), 2) . ' MB';
                            }
                        @endphp
                        <tr wire:key="file-{{ $fileId }}" class="{{ $isMain ? 'table-primary' : '' }}" style="cursor: pointer;" title="{{ $isMain ? 'คลิกเพื่อยกเลิกการตั้งเป็นไฟล์หลัก' : 'คลิกเพื่อตั้งเป็นไฟล์หลัก' }}"
                            @if(!$isTemp) onclick="setMainFile('{{ $fileId }}', {{ $isMain ? 'true' : 'false' }})" @endif>
                            <td class="align-middle text-center">
                                @if($isMain)
                                    <i class="fas fa-star text-warning"></i>
                                @else
                                    {{ $index + 1 }}
                                @endif
                            </td>
                            <td class="align-middle">
                                <span class="badge {{ $bgColor }} text-white">
                                    <i class="fas {{ $icon }}"></i> {{ $typeLabel }}
                                </span>
                            </td>
                            <td class="align-middle">{{ $fileName }}</td>
                            <td class="align-middle text-end">{{ $formattedSize }}</td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ $fileUrl }}" class="btn btn-sm btn-info" target="_blank" onclick="event.stopPropagation();">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ $fileUrl }}" class="btn btn-sm btn-success" download onclick="event.stopPropagation();">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger delete-file-btn"
                                            wire:click="deleteFile('{{ $fileId }}')"
                                            onclick="event.stopPropagation(); confirm('คุณแน่ใจหรือไม่ว่าต้องการลบไฟล์นี้?') || event.stopImmediatePropagation()">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @else
        <div class="card">
            <div class="card-body text-center py-5">
                <div class="py-4">
                    <i class="fas fa-file-alt text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="mb-2">ยังไม่มีไฟล์รายการ</h5>
                    <p class="text-muted">กรุณาอัพโหลดไฟล์ด้านบนเพื่อเพิ่มรายการ</p>
                </div>
            </div>
        </div>
    @endif

    <script>
        function setMainFile(fileId, isMain) {
            let title, text, icon;

            if (isMain) {
                title = 'กำลังยกเลิก...';
                text = 'กำลังยกเลิกการตั้งเป็นไฟล์หลัก';
                icon = 'info';
            } else {
                title = 'กำลังตั้งค่า...';
                text = 'กำลังตั้งเป็นไฟล์หลัก';
                icon = 'info';
            }

            // Show a small loading indicator
            Swal.fire({
                title: title,
                text: text,
                icon: icon,
                showConfirmButton: false,
                timer: 800,
                timerProgressBar: true
            });

            // Call the Livewire method
            @this.setMainFile(fileId);
        }
    </script>
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:initialized', function () {
        console.log('ItemFileManager component initialized');

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            // Check if the message is from our iframe
            if (event.data && event.data.type === 'uppy-upload-success') {
                console.log('Received upload success message from iframe:', event.data);

                // Handle the uploaded file data
                const fileData = event.data.fileData;
                const filesData = event.data.filesData || [];
                const uploadType = event.data.uploadType;

                // Check if this is a document file (PDF, audio, video)
                const fileType = fileData?.type || fileData?.file_type || '';
                const isDocumentFile = fileType.includes('pdf') ||
                                      fileType.includes('audio') ||
                                      fileType.includes('video') ||
                                      (uploadType && ['document', 'audio', 'video'].includes(uploadType));

                if (isDocumentFile) {
                    // Notify Livewire component about the uploaded files
                    if (filesData.length > 0) {
                        // Handle multiple files
                        filesData.forEach(file => {
                            const type = file.type || file.file_type || '';
                            if (type.includes('pdf') || type.includes('audio') || type.includes('video')) {
                                @this.handleUppyFileUploaded(file);
                            }
                        });
                    } else if (fileData) {
                        // Fallback for backward compatibility
                        @this.handleUppyFileUploaded(fileData);
                    }
                }
            }
        });

        // Listen for custom events from the page
        document.addEventListener('uppyFileUploaded', function(event) {
            console.log('ItemFileManager received uppyFileUploaded DOM event:', event.detail);

            if (event.detail && event.detail.fileData) {
                const fileData = event.detail.fileData;

                // ตรวจสอบว่าเป็นไฟล์ (ไม่ใช่รูปภาพ)
                const isImage = fileData.is_image === true;
                const fileType = fileData.type || fileData.file_type || '';

                if (!isImage || fileType.includes('pdf') || fileType.includes('audio') || fileType.includes('video')) {
                    console.log('Processing non-image file from DOM event');
                    @this.handleUppyFileUploaded(fileData);
                }
            }
        });

        // Listen for Livewire events
        Livewire.on('uppyFileUploaded', (fileData) => {
            console.log('ItemFileManager received uppyFileUploaded Livewire event:', fileData);

            if (fileData) {
                // ตรวจสอบว่าเป็นไฟล์ (ไม่ใช่รูปภาพ)
                const isImage = fileData.is_image === true;
                const fileType = fileData.type || fileData.file_type || '';

                if (!isImage || fileType.includes('pdf') || fileType.includes('audio') || fileType.includes('video')) {
                    console.log('Processing non-image file from Livewire event');
                    @this.handleUppyFileUploaded(fileData);
                }
            }
        });

        // Listen for dropzone-file-uploaded event
        window.addEventListener('dropzone-file-uploaded', function(event) {
            console.log('ItemFileManager received dropzone-file-uploaded event:', event.detail);

            if (event.detail && event.detail.fileData) {
                const fileData = event.detail.fileData;

                // ตรวจสอบว่าเป็นไฟล์ (ไม่ใช่รูปภาพ)
                const isImage = fileData.is_image === true;

                if (!isImage) {
                    console.log('Processing non-image file from dropzone event');
                    @this.handleUppyFileUploaded(fileData);
                }
            }
        });
    });
</script>
@endpush
