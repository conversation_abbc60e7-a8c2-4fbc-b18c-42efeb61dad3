/* Statistics Page Styles */
.statistics-container {
    padding: 30px 0;
}

.page-title {
    font-size: 2rem;
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.stats-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
}

.stats-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    align-items: center;
    width: calc(25% - 20px);
    min-width: 200px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stats-icon {
    font-size: 2.5rem;
    color: #007bff;
    margin-right: 20px;
}

.stats-info h3 {
    font-size: 1.8rem;
    margin: 0;
    color: #333;
}

.stats-info p {
    margin: 5px 0 0;
    color: #666;
}

.stats-tabs {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-container {
    height: 300px;
    max-width: 100%;
    margin: 0 auto 30px;
    position: relative;
}

.data-table {
    margin-top: 30px;
}

.data-table h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #333;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th, .data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.data-table th {
    background-color: #f5f5f5;
    font-weight: 500;
}

.data-table tr:hover {
    background-color: #f9f9f9;
}

@media (max-width: 992px) {
    .chart-container {
        height: 280px;
    }
    
    .tab-content {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .stats-card {
        width: calc(50% - 20px);
    }
    
    .chart-container {
        height: 250px;
    }
    
    .tab-header {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .tab-item {
        padding: 12px 15px;
        font-size: 0.9rem;
        white-space: nowrap;
    }
    
    .data-table {
        overflow-x: auto;
    }
}

@media (max-width: 576px) {
    .stats-card {
        width: 100%;
    }
    
    .chart-container {
        height: 220px;
    }
    
    .page-title {
        font-size: 1.5rem;
        margin-bottom: 20px;
    }
    
    .stats-summary {
        gap: 15px;
    }
    
    .stats-icon {
        font-size: 2rem;
    }
    
    .stats-info h3 {
        font-size: 1.5rem;
    }
}
