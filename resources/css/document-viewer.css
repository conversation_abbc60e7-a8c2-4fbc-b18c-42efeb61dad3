/* Document Viewer Styles */
.document-viewer {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.viewer-toolbar {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.toolbar-left, .toolbar-right {
    display: flex;
    gap: 5px;
}

.tool-btn {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.tool-btn:hover {
    background-color: #f0f0f0;
}

.main-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f9f9f9;
    min-height: 400px;
    position: relative;
    overflow: hidden;
}

.main-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

#document-image {
    max-width: 100%;
    max-height: 400px;
    transition: transform 0.3s ease;
}

.viewer-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
}

.pagination-btn {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f0f0f0;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    font-size: 14px;
    color: #666;
}

.thumbnails {
    display: flex;
    gap: 10px;
    padding: 15px;
    overflow-x: auto;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

.thumbnail {
    width: 80px;
    height: 60px;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail.active {
    border-color: #007bff;
}

.thumbnail:hover:not(.active) {
    border-color: #ddd;
}
