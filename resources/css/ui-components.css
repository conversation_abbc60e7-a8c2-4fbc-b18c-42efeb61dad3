/* Tab Styles */
.tab-header {
    display: flex;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    overflow-x: auto;
}

.tab-item {
    padding: 15px 20px;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.2s;
}

.tab-item:hover {
    background-color: #e9e9e9;
}

.tab-item.active {
    background-color: #fff;
    border-bottom: 2px solid #007bff;
    font-weight: 500;
}

.tab-content {
    padding: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
