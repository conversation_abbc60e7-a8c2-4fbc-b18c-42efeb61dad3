<?php

// สคริปต์กู้คืนข้อมูลจาก test (documents) ไปยัง digital_collections_laravel (items)
require __DIR__.'/vendor/autoload.php';

// ใช้ Laravel Application แทน
$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;

// กำหนดการเชื่อมต่อฐานข้อมูลเก่า (test)
Config::set('database.connections.old', [
    'driver'    => 'mysql',
    'host'      => env('DB_HOST', '127.0.0.1'),
    'port'      => env('DB_PORT', '3306'),
    'database'  => 'test',
    'username'  => env('DB_USERNAME', 'root'),
    'password'  => env('DB_PASSWORD', ''),
    'charset'   => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix'    => '',
]);

// ใช้การเชื่อมต่อปัจจุบันเป็นฐานข้อมูลใหม่ (digital_collections_laravel)
$currentConnection = Config::get('database.default');

// เริ่มกู้คืนข้อมูล
echo "เริ่มกู้คืนข้อมูล...\n";

try {
    // 1. ตรวจสอบตารางในฐานข้อมูลเก่า
    $tables = DB::connection('old')->select('SHOW TABLES');
    echo "ตารางในฐานข้อมูลเก่า:\n";
    foreach ($tables as $table) {
        $tableArray = (array)$table;
        $tableName = reset($tableArray);
        echo "- $tableName\n";
    }

    // 2. ตรวจสอบว่ามีตาราง documents หรือไม่
    $hasDocuments = false;
    foreach ($tables as $table) {
        $tableArray = (array)$table;
        $tableName = reset($tableArray);
        if ($tableName === 'documents') {
            $hasDocuments = true;
            break;
        }
    }

    if (!$hasDocuments) {
        echo "ไม่พบตาราง documents ในฐานข้อมูลเก่า\n";
        exit(1);
    }

    // 3. ดึงข้อมูลจากตาราง documents
    $documents = DB::connection('old')->table('documents')->get();
    echo "พบข้อมูลในตาราง documents จำนวน " . count($documents) . " รายการ\n";

    // 4. ตรวจสอบตาราง document_files
    $hasDocumentFiles = false;
    foreach ($tables as $table) {
        $tableArray = (array)$table;
        $tableName = reset($tableArray);
        if ($tableName === 'document_files') {
            $hasDocumentFiles = true;
            break;
        }
    }

    if ($hasDocumentFiles) {
        $documentFiles = DB::connection('old')->table('document_files')->get();
        echo "พบข้อมูลในตาราง document_files จำนวน " . count($documentFiles) . " รายการ\n";
    } else {
        echo "ไม่พบตาราง document_files ในฐานข้อมูลเก่า\n";
    }

    // 5. ตรวจสอบตาราง document_images
    $hasDocumentImages = false;
    foreach ($tables as $table) {
        $tableArray = (array)$table;
        $tableName = reset($tableArray);
        if ($tableName === 'document_images') {
            $hasDocumentImages = true;
            break;
        }
    }

    if ($hasDocumentImages) {
        $documentImages = DB::connection('old')->table('document_images')->get();
        echo "พบข้อมูลในตาราง document_images จำนวน " . count($documentImages) . " รายการ\n";
    } else {
        echo "ไม่พบตาราง document_images ในฐานข้อมูลเก่า\n";
    }

    // 6. นำเข้าข้อมูลไปยังฐานข้อมูลใหม่
    echo "เริ่มนำเข้าข้อมูลไปยังฐานข้อมูลใหม่...\n";

    // 6.1 นำเข้าข้อมูล documents -> items
    foreach ($documents as $document) {
        $documentData = (array) $document;

        // ปรับชื่อฟิลด์ document_type_id เป็น item_type_id ถ้ามี
        if (isset($documentData['document_type_id'])) {
            $documentData['item_type_id'] = $documentData['document_type_id'];
            unset($documentData['document_type_id']);
        }

        // กรองฟิลด์ที่มีในตาราง items เท่านั้น
        $validFields = [
            'id', 'title', 'description', 'identifier_no', 'year',
            'item_type_id', 'category_id', 'material_id', 'language_id',
            'script_id', 'creator', 'author', 'location', 'country',
            'province', 'latitude', 'longitude', 'remark',
            'manuscript_condition', 'image_path', 'file_path',
            'file_type', 'created_at', 'updated_at'
        ];

        $filteredData = [];
        foreach ($validFields as $field) {
            if (isset($documentData[$field])) {
                $filteredData[$field] = $documentData[$field];
            }
        }

        // ตรวจสอบว่ามีข้อมูลในตาราง items หรือไม่
        $existingItem = DB::table('items')
            ->where('id', $filteredData['id'])
            ->first();

        if ($existingItem) {
            echo "อัปเดตข้อมูล item ID: " . $filteredData['id'] . "\n";
            DB::table('items')
                ->where('id', $filteredData['id'])
                ->update($filteredData);
        } else {
            echo "เพิ่มข้อมูล item ใหม่ ID: " . $filteredData['id'] . "\n";
            DB::table('items')->insert($filteredData);
        }
    }

    // 6.2 นำเข้าข้อมูล document_files -> item_files
    if ($hasDocumentFiles) {
        foreach ($documentFiles as $file) {
            $fileData = (array) $file;

            // ปรับชื่อฟิลด์ document_id เป็น item_id
            if (isset($fileData['document_id'])) {
                $fileData['item_id'] = $fileData['document_id'];
                unset($fileData['document_id']);
            }

            // กรองฟิลด์ที่มีในตาราง item_files เท่านั้น
            $validFields = [
                'id', 'item_id', 'file_path', 'file_name', 'file_type',
                'file_extension', 'sort_order', 'is_main', 'created_at', 'updated_at'
            ];

            $filteredData = [];
            foreach ($validFields as $field) {
                if (isset($fileData[$field])) {
                    $filteredData[$field] = $fileData[$field];
                }
            }

            // ตรวจสอบว่ามีข้อมูลในตาราง item_files หรือไม่
            $existingFile = DB::table('item_files')
                ->where('id', $filteredData['id'])
                ->first();

            if ($existingFile) {
                echo "อัปเดตข้อมูล item_file ID: " . $filteredData['id'] . "\n";
                DB::table('item_files')
                    ->where('id', $filteredData['id'])
                    ->update($filteredData);
            } else {
                echo "เพิ่มข้อมูล item_file ใหม่ ID: " . $filteredData['id'] . "\n";
                DB::table('item_files')->insert($filteredData);
            }
        }
    }

    // 6.3 นำเข้าข้อมูล document_images -> item_images
    if ($hasDocumentImages) {
        foreach ($documentImages as $image) {
            $imageData = (array) $image;

            // ปรับชื่อฟิลด์ document_id เป็น item_id
            if (isset($imageData['document_id'])) {
                $imageData['item_id'] = $imageData['document_id'];
                unset($imageData['document_id']);
            }

            // กรองฟิลด์ที่มีในตาราง item_images เท่านั้น
            $validFields = [
                'id', 'item_id', 'image_path', 'image_name', 'image_type',
                'sort_order', 'is_main', 'created_at', 'updated_at'
            ];

            $filteredData = [];
            foreach ($validFields as $field) {
                if (isset($imageData[$field])) {
                    $filteredData[$field] = $imageData[$field];
                }
            }

            // ตรวจสอบว่ามีข้อมูลในตาราง item_images หรือไม่
            $existingImage = DB::table('item_images')
                ->where('id', $filteredData['id'])
                ->first();

            if ($existingImage) {
                echo "อัปเดตข้อมูล item_image ID: " . $filteredData['id'] . "\n";
                DB::table('item_images')
                    ->where('id', $filteredData['id'])
                    ->update($filteredData);
            } else {
                echo "เพิ่มข้อมูล item_image ใหม่ ID: " . $filteredData['id'] . "\n";
                DB::table('item_images')->insert($filteredData);
            }
        }
    }

    echo "กู้คืนข้อมูลเสร็จสิ้น\n";

} catch (Exception $e) {
    echo "เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    exit(1);
}
